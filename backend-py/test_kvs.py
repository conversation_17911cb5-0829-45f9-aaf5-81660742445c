import unittest
from kvs import KVS
from memory_profiler import profile


class KvsTest(unittest.TestCase):
    def test_get_put(self):
        kvs = KVS(10)
        self.assertEqual(kvs.get("key1"), None)
        kvs.put("key1", 1)
        self.assertEqual(kvs.get("key1"), 1)
        kvs.put("key2", 2)
        self.assertEqual(kvs.get("key2"), 2)
        kvs.put("key1", 3)
        self.assertEqual(kvs.get("key1"), 3)
        self.assertEqual(kvs.get("key2"), 2)

    def test_max(self):
        kvs = KVS(2)
        kvs.put("key1", 1)
        self.assertEqual(kvs.get("key1"), 1)
        kvs.put("key2", 2)
        kvs.put("key3", 3)
        self.assertEqual(kvs.get("key1"), None)
        self.assertEqual(kvs.get("key2"), 2)
        self.assertEqual(kvs.get("key3"), 3)

    @profile
    def test_mem_check(self):
        kvs = KVS(1000)
        for i in range(1000):
            kvs.put(f"key{i}", i)
        for i in range(100000):
            kvs.put(f"key_1_{i}", i)
        for i in range(100000):
            kvs.put(f"key_2_{i}", i)
        for i in range(100000):
            kvs.put(f"key_3_{i}", i)
        for i in range(100000):
            kvs.put(f"key_4_{i}", i)
        for i in range(100000):
            kvs.put(f"key_5_{i}", i)


if __name__ == '__main__':
    unittest.main()
