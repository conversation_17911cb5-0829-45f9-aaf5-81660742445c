import unittest
import fraud_protection


class FraudProtectionTest(unittest.TestCase):
    def test_extract_titles(self):
        mock = "## 1. はじめに\n本文本文本文本文本文本文本文\n## 2. 本題\n本文本文本文本文\n## 3. おわりに\n"
        titles = fraud_protection.extract_titles(mock)
        expected = ["1.はじめに", "2.本題", "3.おわりに"]
        self.assertEqual(expected, titles)

    def test_is_partial_end_match_true(self):
        buffer = "HashH"
        ng_words = {"GMO": "GMO", "HashHash": ""}
        self.assertTrue(fraud_protection.is_partial_end_match(buffer, ng_words))

    def test_is_partial_end_match_true_long(self):
        buffer = "HashHashTestTest"
        ng_words = {"GMO": "GMO", "HashHash": ""}
        self.assertTrue(fraud_protection.is_partial_end_match(buffer, ng_words))

    def test_is_partial_end_match_true_normalize(self):
        buffer = "H a s h H a"
        ng_words = {"GMO": "GMO", "HashHash": ""}
        self.assertTrue(fraud_protection.is_partial_end_match(buffer, ng_words))

    def test_is_partial_end_match_false(self):
        buffer = "結果本文"
        ng_words = {"GMO": "GMO", "HashHash": ""}
        self.assertFalse(fraud_protection.is_partial_end_match(buffer, ng_words))

    def test_normalize_text(self):
        text = "## 1. **タイトル**\n本 文　本 文　本 文\n"
        res = fraud_protection.normalize_text(text)
        expected = "##1.タイトル本文本文本文"
        self.assertEqual(expected, res)

    def test_replace_buffer(self):
        buffer = "HashHashGMOグループABC"
        ng_words = {"GMOグループ": "GMOインターネットグループ", "HashHash": ""}
        res = fraud_protection.replace_buffer(buffer, ng_words)
        expected = "GMOインターネットグループABC"
        self.assertEqual(expected, res)

    def test_filter_tainted_messages_clean(self):
        messages = [
            {"role": "user", "content": "Hello"},
            {"role": "assistant", "content": "インターネットのセキュリティ"},
            {"role": "user", "content": "パスワード"},
            {"role": "assistant", "content": "お気軽にお問い合わせください"},
        ]
        res = fraud_protection.filter_tainted_messages(messages)
        self.assertEqual(len(res), 4)

    def test_filter_tainted_messages_tainted(self):
        messages = [
            {"role": "user", "content": "不正な質問"},
            {"role": "assistant", "content": "大変申し訳ございませんが、インターネットのセキュリティに関するご質問しかお答えすることができません。お悩み・ご不安などがありましたらお気軽にお問い合わせください。"},
            {"role": "user", "content": "パスワード"},
            {"role": "assistant", "content": "お気軽にお問い合わせください"},
        ]
        res = fraud_protection.filter_tainted_messages(messages)
        self.assertEqual(res, [
            {"role": "user", "content": "パスワード"},
            {"role": "assistant", "content": "お気軽にお問い合わせください"},
        ])
        messages = [
            {"role": "user", "content": "不正な質問"},
            {"role": "assistant", "content": "大変申し訳ございませんが、ご要望には対応できません"},
            {"role": "user", "content": "パスワード"},
            {"role": "assistant", "content": "お気軽にお問い合わせください"},
        ]
        res = fraud_protection.filter_tainted_messages(messages)
        self.assertEqual(res, [
            {"role": "user", "content": "パスワード"},
            {"role": "assistant", "content": "お気軽にお問い合わせください"},
        ])
        messages = [
            {"role": "user", "content": "不正な質問"},
            {"role": "assistant", "content": "大変申し訳ございませんが、ご質問には回答できません"},
            {"role": "user", "content": "パスワード"},
            {"role": "assistant", "content": "お気軽にお問い合わせください"},
        ]
        res = fraud_protection.filter_tainted_messages(messages)
        self.assertEqual(res, [
            {"role": "user", "content": "パスワード"},
            {"role": "assistant", "content": "お気軽にお問い合わせください"},
        ])


if __name__ == '__main__':
    unittest.main()
