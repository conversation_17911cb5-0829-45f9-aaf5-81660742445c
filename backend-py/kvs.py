from collections import OrderedDict


class KVS:
    def __init__(self, max_size: int):
        self.max_size = max_size
        self.cache = OrderedDict()

    def get(self, key: str, move=False):
        if key in self.cache:
            if move:
                self.cache.move_to_end(key)
            return self.cache[key]
        return None

    def put(self, key: str, value) -> None:
        if key in self.cache:
            self.cache[key] = value
            self.cache.move_to_end(key)
            return
        self.cache[key] = value
        if len(self.cache) > self.max_size:
            _, v = self.cache.popitem(False)
            del v
