import argparse
import json
import os
import re
import subprocess
import time
import unicodedata
import logging

import pandas as pd
from google.cloud import aiplatform
from vertexai.language_models import TextEmbeddingInput, TextEmbeddingModel

# ロガーの設定
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def set_env(path: str = ".env.yaml"):
    with open(path, "r") as f:
        lines = [
            line.strip()
            for line in f.read().strip("\n").split("\n")
            if line and not line.strip().startswith("#")
        ]
    for line in lines:
        match = re.match(r"^(\S+)\s*:\s*(\S.*\S)\s*$", line)
        if match:
            key, value = match[1], match[2]
            os.environ[key] = value
            print(key, value)
        else:
            print(f"Failed to parse line in {path}:", line)


# ドキュメント用 Dense Vector (密ベクトル) 取得関数
def get_document_dense_embedding(text, model):
    """入力ドキュメントを密ベクトルに変換"""
    logger.debug("Generating document dense embedding for text of length %d", len(text))
    input_data = TextEmbeddingInput(text=text, task_type="RETRIEVAL_DOCUMENT")
    embedding = model.get_embeddings([input_data])[0].values
    logger.debug(
        "Generated document dense embedding with %d dimensions", len(embedding)
    )
    return embedding


# クエリ用 Dense Vector (密ベクトル) 取得関数
def get_query_dense_embedding(text, model):
    """入力クエリを密ベクトルに変換"""
    logger.debug("Generating query dense embedding for text: %s", text)
    input_data = TextEmbeddingInput(text=text, task_type="RETRIEVAL_QUERY")
    embedding = model.get_embeddings([input_data])[0].values
    logger.debug("Generated query dense embedding with %d dimensions", len(embedding))
    return embedding


def load_json_data(json_data_dir="data/faq"):
    logger.info("Loading JSON data from directory: %s", json_data_dir)
    documents = []
    files = sorted(os.listdir(json_data_dir))
    logger.info("Found %d files in directory", len(files))
    for file_name in files:
        if file_name.endswith(".json"):
            file_path = os.path.join(json_data_dir, file_name)
            logger.info("Processing file: %s", file_path)
            with open(file_path, "r", encoding="utf-8") as f:
                try:
                    data = json.load(f)
                    for d in data:
                        if d.get("question") and d.get("answer"):
                            # 正規化して不要な文字（例：\xa0など）を除去
                            d["question"] = unicodedata.normalize("NFKC", d["question"])
                            d["answer"] = unicodedata.normalize("NFKC", d["answer"])
                    documents.extend(data)
                except json.JSONDecodeError as e:
                    logger.error("Error decoding JSON from file %s: %s", file_name, e)

    output_json_path = "documents.json"
    logger.info("Saving consolidated JSON data to: %s", output_json_path)
    with open(output_json_path, "w", encoding="utf-8") as f:
        json.dump(documents, f, ensure_ascii=False, indent=4)

    df = pd.DataFrame(documents)
    df.reset_index(drop=True, inplace=True)
    df["question_service"] = df["question"].fillna("") + " " + df["service"].fillna("")
    logger.info("DataFrame created with %d records", len(df))
    logger.debug("Records with null service: %s", df[df["service"].isnull()])
    logger.debug("Type value counts: %s", df["type"].value_counts())
    logger.debug("Service value counts: %s", df["service"].value_counts())

    return df


def create_index_file(df, model, output_path):
    logger.info("Creating index file from DataFrame with %d records", len(df))
    items = []
    for i in range(len(df)):
        if i % 100 == 0:
            logger.info("Processing record %d/%d", i, len(df))
        id = i
        question = df.at[i, "question"]
        answer = df.at[i, "answer"]
        company = df.at[i, "company"]
        service = df.at[i, "service"]
        question_service = df.at[i, "question_service"]
        dense_embedding = get_document_dense_embedding(question_service, model)
        items.append(
            {
                "id": id,
                "question": question,
                "answer": answer,
                "company": company,
                "service": service,
                "embedding": dense_embedding,
            }
        )

    logger.info("Writing index file to: %s", output_path)
    with open(output_path, "w", encoding="utf-8") as f:
        for item in items:
            f.write(f"{item}\n")
    logger.info("Index file creation completed.")


def create_and_deploy_index_endpoint(
    index_display_name: str,
    contents_delta_uri: str,
    index_endpoint_display_name: str,
    deployed_index_id: str,
    machine_type: str,
):
    logger.info(
        "Creating Matching Engine Index with display name: %s", index_display_name
    )
    my_hybrid_index = aiplatform.MatchingEngineIndex.create_tree_ah_index(
        display_name=index_display_name,
        contents_delta_uri=contents_delta_uri,
        dimensions=768,
        approximate_neighbors_count=20,
        shard_size="SHARD_SIZE_SMALL",  # SHARD_SIZE_SMALL, SHARD_SIZE_MEDIUM, SHARD_SIZE_LARGE
    )
    logger.info("Index created: %s", my_hybrid_index.display_name)

    logger.info(
        "Creating Matching Engine Index Endpoint with display name: %s",
        index_endpoint_display_name,
    )
    my_index_endpoint = aiplatform.MatchingEngineIndexEndpoint.create(
        display_name=index_endpoint_display_name,
        public_endpoint_enabled=True,
    )
    logger.info("Index Endpoint created: %s", my_index_endpoint.display_name)

    logger.info(
        "Deploying index to index endpoint with deployed index ID: %s",
        deployed_index_id,
    )
    my_index_endpoint.deploy_index(
        index=my_hybrid_index,
        deployed_index_id=deployed_index_id,
        min_replica_count=2,
        max_replica_count=10,
        machine_type=machine_type,  # n1-standard-16 や e2-standard-16 など　# default: e2-standard-2
    )
    logger.info("Index deployed successfully.")

    index_endpoints = sorted(
        aiplatform.MatchingEngineIndexEndpoint.list(),
        key=lambda x: x.gca_resource.create_time,
    )
    index_endpoint_name = index_endpoints[-1].gca_resource.name
    logger.info("Using index endpoint: %s", index_endpoint_name)
    my_index_endpoint = aiplatform.MatchingEngineIndexEndpoint(
        index_endpoint_name=index_endpoint_name
    )

    return my_index_endpoint


def get_dense_embedding_neighbor(
    query_text, model, deployed_index_id, my_index_endpoint, df, silent=False
):
    logger.info("Searching for neighbors using query: %s", query_text)
    start = time.perf_counter()
    query_dense_emb = get_query_dense_embedding(query_text, model)
    response = my_index_endpoint.find_neighbors(
        deployed_index_id=deployed_index_id,
        queries=[query_dense_emb],
        num_neighbors=10,
    )

    context = []
    for idx, neighbor in enumerate(response[0]):
        record_id = int(neighbor.id)
        question = df.at[record_id, "question"]
        answer = df.at[record_id, "answer"]
        company = df.at[record_id, "company"]
        service = df.at[record_id, "service"]
        dense_dist = neighbor.distance if neighbor.distance else 0.0
        text = (
            f"<距離> dense_dist: {dense_dist}</距離> \n"
            f"<関連情報> question: {question:<9}, answer: {answer}, company: {company}, service: {service} </関連情報>"
        )
        if not silent:
            logger.info("Neighbor %d: %s", idx, text)
        context.append(text)
    end = time.perf_counter()
    logger.info("Neighbor search completed in %.2f seconds", end - start)
    return context


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Vertex AI Search Index Deployment")
    parser.add_argument(
        "--env_file",
        required=True,
        help="Path to the environment file",
    )
    args = parser.parse_args()
    set_env(args.env_file)

    DATA_DIR = "./data"
    PROJECT_ID = os.environ.get("PROJECT_ID")
    LOCATION = os.environ.get("REGION")
    aiplatform.init(project=PROJECT_ID, location=LOCATION)
    logger.info(
        "Initialized Vertex AI with project: %s, location: %s", PROJECT_ID, LOCATION
    )

    # パラメータの設定
    VERTEXAI_SEARCH_INDEX_DISPLAY_NAME = os.environ.get(
        "EMBEDDING_NEIGHBOR_INDEX_DISPLAY_NAME"
    )
    VETEXAI_SEARCH_INDEX_ENDPOINT_DISPLAY_NAME = os.environ.get(
        "EMBEDDING_NEIGHBOR_INDEX_ENDPOINT_DISPLAY_NAME"
    )
    DEPLOYED_HYBRID_INDEX_ID = os.environ.get("EMBEDDING_NEIGHBOR_DEPLOY_INDEX_ID")
    MACHINE_TYPE = os.environ.get("EMBEDDING_NEIGHBOR_MACHINE_TYPE")
    BUCKET_URI = f"gs://{PROJECT_ID}-vertexai-search_asia-northeast1"

    logger.info("Loading JSON data...")
    df = load_json_data()
    pickle_path = os.path.join(DATA_DIR, os.environ.get("DOCUMENTS_DF_PKL"))
    df.to_pickle(pickle_path)
    logger.info("DataFrame saved to pickle at: %s", pickle_path)

    logger.info("Loading pre-trained text embedding model...")
    model = TextEmbeddingModel.from_pretrained("text-multilingual-embedding-002")

    index_file_path = "documents_index.json"
    logger.info("Creating index file at: %s", index_file_path)
    create_index_file(df, model, output_path=index_file_path)

    logger.info("Creating bucket: %s", BUCKET_URI)
    try:
        subprocess.run(
            f"gsutil mb -l {LOCATION} -p {PROJECT_ID} {BUCKET_URI}",
            shell=True,
            check=True,
        )
    except Exception as e:
        print(e)
    logger.info("Uploading index file to bucket: %s", BUCKET_URI)
    subprocess.run(f"gsutil cp {index_file_path} {BUCKET_URI}", shell=True, check=True)

    logger.info("Creating and deploying index endpoint...")
    my_index_endpoint = create_and_deploy_index_endpoint(
        index_display_name=VERTEXAI_SEARCH_INDEX_DISPLAY_NAME,
        contents_delta_uri=BUCKET_URI,
        index_endpoint_display_name=VETEXAI_SEARCH_INDEX_ENDPOINT_DISPLAY_NAME,
        deployed_index_id=DEPLOYED_HYBRID_INDEX_ID,
        machine_type=MACHINE_TYPE,
    )

    logger.info("=" * 60)
    logger.info("Update EMBEDDING_NEIGHBOR_INDEX_ENDPOINT in %s", args.env_file)
    logger.info("EMBEDDING_NEIGHBOR_INDEX_ENDPOINT: %s", my_index_endpoint)
    logger.info("=" * 60)

    query_text = "脆弱性診断"
    logger.info("Performing neighbor search for query: %s", query_text)
    context = get_dense_embedding_neighbor(
        query_text, model, DEPLOYED_HYBRID_INDEX_ID, my_index_endpoint, df
    )
    logger.info("Search results: %s", context)
