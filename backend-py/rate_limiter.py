from kvs import KVS
import datetime


KVS_MAX_SIZE = 1000
MINUTE_LIMIT = 5
DAY_LIMIT = 50


class RateLimiter:
    def __init__(self, permit_ip_list=[]):
        self.kvs = KVS(1000)
        self.permit_ip_list = permit_ip_list

    def is_allowed_by_minute(self, ip: str, now: datetime.datetime) -> bool:
        if ip in self.permit_ip_list:
            print(f"Permitted IP address: {ip}")
            return True
        key = f"{ip}-{now.strftime('%Y%m%d%H%M')}"
        return self._is_allowed(key, MINUTE_LIMIT)

    def is_allowed_by_day(self, ip: str, now: datetime.datetime) -> bool:
        if ip in self.permit_ip_list:
            print(f"Permitted IP address: {ip}")
            return True
        key = f"{ip}-{now.strftime('%Y%m%d')}"
        return self._is_allowed(key, DAY_LIMIT)

    def _is_allowed(self, key: str, limit: int) -> bool:
        res = self.kvs.get(key)
        print(f"checking {key} -> {res}")
        if res is None:
            self.kvs.put(key, 1)
            return True
        self.kvs.put(key, res + 1)
        if res >= limit:
            return False
        return True
