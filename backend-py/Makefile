start:
	bash -c "$(node ../backend/commands/set_env.js) functions-framework --target=chat --port=8081"

deploy_dev:
	gcloud functions deploy internal-api-ai --project=dev-net-security-marketing --gen2 --runtime=python312 --region=asia-northeast1 --source=. --entry-point=chat --trigger-http --allow-unauthenticated --env-vars-file=.env.dev.yaml --set-secrets='SECRET_PROMPT_HASH_SALT=SECRET_PROMPT_HASH_SALT:latest,SECRET_OPENAI_API_KEY=SECRET_OPENAI_API_KEY:latest,SECRET_PERPLEXITY_API_KEY=SECRET_PERPLEXITY_API_KEY:latest,SECRET_AZURE_OPENAI_API_KEY=SECRET_AZURE_OPENAI_API_KEY:latest'

deploy_prod:
	gcloud functions deploy internal-api-ai --project=net-security-marketing --gen2 --runtime=python312 --region=asia-northeast1 --source=. --entry-point=chat --trigger-http --allow-unauthenticated --env-vars-file=.env.prod.yaml --set-secrets='SECRET_PROMPT_HASH_SALT=SECRET_PROMPT_HASH_SALT:latest,SECRET_OPENAI_API_KEY=SECRET_OPENAI_API_KEY:latest,SECRET_PERPLEXITY_API_KEY=SECRET_PERPLEXITY_API_KEY:latest,SECRET_AZURE_OPENAI_API_KEY=SECRET_AZURE_OPENAI_API_KEY:latest'

upload_dev:
	gsutil cp -r ./data/* gs://dev-security-ai/

upload_prod:
	gsutil cp -r ./data/* gs://security-prompt/

update_index_dev:
	python -m script.update_vertex_ai_index --env_file .env.dev.yaml

update_index_prod:
	python -m script.update_vertex_ai_index --env_file .env.prod.yaml