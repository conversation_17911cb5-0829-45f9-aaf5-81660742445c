[{"question": "中間CA証明書に関して", "answer": "グローバルサインのサーバ証明書はセキュリティ上の理由等により3階層の証明書を発行しています。証明書をご利用いただくには、ご利用のサービスに適した中間CA証明書もあわせてサーバに設定する必要があります。（\nhttps://jp.globalsign.com/support/rootcertificates/about-intermediate_certificate.html\n）\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "中間認証局の証明書の設定はなぜ必要なのですか", "answer": "弊社サーバ証明書はセキュリティ上の理由等により3階層の証明書を発行しております。ブラウザは証明書の有効性を確認する際に3階層すべての証明書を検証する必要があります。通常ブラウザには最上位の認証局の証明書のみ格納されていますので、WEBサーバには下位2階層を設定する必要があります。そうすることによってブラウザは証明書の階層を正しくたどることが可能になります。\n※クロス証明書がインストールされている環境では4階層となります。中間認証局の証明書は（\nhttps://jp.globalsign.com/repository/#root\n）よりダウンロードが可能です。\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "ルート証明書に関して", "answer": "詳細およびダウンロードは（\nhttps://jp.globalsign.com/repository/#root\n）よりご確認ください。\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "CSRについて教えてください", "answer": "CSRとは、サーバ証明書を発行するための署名要求（Certificate Signing Request）です。 グローバルサインでは、提出されたCSRに認証機関としての署名をして、サーバ証明書を発行します。（\nhttps://jp.globalsign.com/support/ssl/certificates/about-csr.html\n）\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "コモンネームについて教えてください", "answer": "SSLサーバ証明書のコモンネームは、CSRを生成する際に入力する項目で、ブラウザでサーバにアクセスする際に入力するURL（FQDNまたはIPアドレス）が該当いたします。コードサイニング証明書・PDF文書署名用証明書・クライアント証明書の場合は、お申し込みの形態により組織名や部門名または個人名が該当いたします。（\nhttps://jp.globalsign.com/support/cert-management/common-name.html\n）\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "ディスティングイッシュネームとは何ですか", "answer": "ディスティングイッシュネームとは、CSRに含まれる、サイトやサイト運営団体に関わる情報です。（\nhttps://jp.globalsign.com/support/cert-management/distinguish-name.html\n）\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "ポート番号とは何ですか", "answer": "ポート番号は、コンピュータで、実際に通信される出入口の番号です。（\nhttps://jp.globalsign.com/support/ssl/certificates/about-portno.html\n）\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "SANについて教えてください", "answer": "SANとは、「Subject Alternative Name」の略称で、「サブジェクトの別名」という意味です。（\nhttps://jp.globalsign.com/support/ssl/certificates/about-san.html\n）\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "JAVAの実行環境は、どのバージョンで対応していますか", "answer": "以下バージョンにてご利用可能となっております。\n- JRE 1.4.2 update 16 以降- JRE 1.5.0 update 13 以降- JRE 1.6.0 update 07 以降- JRE 1.7.0 以降関連リンク- （\nhttp://www.oracle.com/technetwork/java/javase/6u3-137914.html\n）-\n （\nhttp://www.oracle.com/technetwork/java/javase/6u7-136303.html\n）\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "PKCS#12とは", "answer": "Public Key Cryptography Standard#12の略です。公開鍵証明書および秘密鍵をパスワードベースの対象鍵で保護し、 安全に保存または転送するために使用されるファイル形式です。", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "鍵長とは", "answer": "鍵長とは、WEBサーバが暗号化などを行うために使用するコード（鍵）の長さです。一般的に鍵長が長いほど暗号の強度が上がりますが、処理に要する時間は長くなります。お使いになっているWEBサーバがサポートしている鍵長をご確認の上、ご指定ください。", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "証明書の期限について教えてください", "answer": "証明書はお申し込みの年数によって、以下のような有効期間が設定されます。クイック認証SSL/企業認証SSL/EV SSL- 半年: 有効期間が214（183+31）日間- 1年: 有効期間が396日23時間59分59秒間\n※証明書の有効期間は、証明書発行に必要な審査が完了し、「サーバ証明書発行のお知らせ」のメールが送付されたタイミングから開始となります。\n※更新時でも、更新元証明書の有効期間残存分の付与はなく、更新後の証明書の有効期間は上記発行のタイミングから開始となります。そのため、本来の有効期間を無駄なく利用されたい場合は有効期限の残りが30日を超えたタイミングで更新を行っていただくことをおすすめしております。イントラネットSSL- 半年: 184日間- 1年: 366日間- 2年: 731日間- 3年: 1096日間- 4年: 1461日間- 5年: 1827日間\n※イントラネットSSL更新の場合、更新特典として、更新元証明書の残期間と30日ボーナスが上乗せされます。", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "CP・CPS、利用約款はどこにありますか。", "answer": "（\nhttps://jp.globalsign.com/repository/\n）をご参照ください。\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "2wayとは何ですか", "answer": "2wayとは、「www.」または「*.」から始まるコモンネームでSSLサーバ証明書を取得した場合に、1枚の証明書でサブドメイン無しのドメイン名のサイトへのアクセスにも利用可能となるサービスです。\n※証明書に2wayを適用させるには、申請や認証時に条件がございます。詳しくは（\nhttps://jp.globalsign.com/support/ssl/certificates/about-2way.html\n）をご参照ください。\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "秘密鍵を送ってください", "answer": "秘密鍵に関しましては、証明書ご申請時のCSRを作成する際に必要な情報であり、サーバ上で作成・保管していただきます。弊社でのお預かりやお渡し等は行っておりません為、CSRを作成したサーバをご確認いただきますようお願い申し上げます。\n※秘密鍵はセキュリティ上、非常に重要な情報のため、厳重に管理する必要がございます。お問合せの際も秘密鍵は送らないようご注意ください。", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "証明書の取得を代行しますが、「契約者」は誰になりますか。", "answer": "証明書の取得を代行する場合でも、エンドユーザが証明書の契約者となります。詳細はこちら（\nhttps://jp.globalsign.com/support/cert-management/straw-purchaser.html\n）\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "証明書の再発行をしましたが、どれが最新かわかりません", "answer": "最新の証明書は、ステータスが「発行済み」となっています。GSパネルから再発行をお申し込みいただくと、元のご注文は「再発行済み」というステータスになります。この注文の他に再発行分のオーダーIDが新規に作成され、ステータスが「申請済み」から「発行済み」となりましたら発行完了です。発行にはおよそ10分ほどかかります。", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "費用について教えてください", "answer": "証明書導入に関してはウェブサイト上でご案内している価格以外に料金はいただきません。弊社ウェブサイト上にて見積もりが可能となっておりますので、よろしければお試しください。なお、ホスティングをご利用の場合は、ホスティングサービスに対する費用が別料金として設定されている場合がありますので、ホスティングサービス業者様へお問い合わせくださいますようお願いいたします。", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "証明書の購入時に初期費用はかかりますか？", "answer": "初期費用はかかりません。必要な費用は証明書代金のみになります。※銀行振り込み時の振り込み手数料はお客様負担となります。※税別価格表示時は、別途消費税が必要になります。", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "再発行は無料ですか？", "answer": "グローバルサインのサーバ証明書は、管理画面から無料で即日再発行させていただきます。手続きにつきましては、下記を参照ください。\n詳細はこちら（\nhttps://jp.globalsign.com/support/cert-management/reissue.html\n）\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "証明書情報の「技術担当者」は、証明書に入るのでしょうか", "answer": "証明書情報の中に技術担当者情報がありますが、これは証明書に書き込まれる情報ではございません。各証明書ごとに技術担当者を設定することができるようになっているため、画面上では証明書情報として表示されるようになっています。証明書には、ディスティングイッシュネームの内容が含まれますが、クイック認証SSLでは、一部情報が上書きされますので、詳しくは『ディスティングイッシュネームとは何ですか（\nhttps://jp.globalsign.com/support/cert-management/distinguish-name.html\n）』をご参照ください。\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "まとめ買いのレベルはどのように変動しますか", "answer": "まとめ買いによって価格レベルが決定しましたら、そのレベルは、デポジットを購入いただいた日から1年間有効です。1年以内でも、更に上のレベルでデポジットを購入いただくと、その時から1年間、レベルが上がることになります。詳しくは下記を参照ください。\n詳細はこちら（\nhttps://jp.globalsign.com/support/cert-management/deposit-level.html\n）\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "パートナーにならないとまとめ買いはできませんか", "answer": "「まとめ買い」はどなたでもご利用いただけます。一定の価格を超えます場合は、ぜひ「まとめ買い」をご利用ください。「まとめ買い購入支援ツール」による割引購入のお手続き（\nhttps://jp.globalsign.com/support/cert-management/deposit-ssl.html\n）「まとめ買い直接購入」による割引購入のお手続き（\nhttps://jp.globalsign.com/support/cert-management/deposit.html\n）なお、企業認証SSLは、「契約者＝認証する企業」であることを確認するため、1つのアカウントで複数の企業のお申し込みを管理いただくことができません。パートナー契約を結んでいただきますと、1つのパートナーアカウントで複数のお客様の企業認証SSLを管理することが可能です。パートナープログラムについて（\nhttps://jp.globalsign.com/partner/\n）クイック認証SSLは、ドメイン管理者による承認をメールにて行うサービスであるため、承認いただける状態であれば発行可能です。ご登録内容に制限はありません。\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "パートナープログラムに関してのQ&A", "answer": "パートナープログラムに関するよくある質問をまとめました。下記をご参照ください。詳細はこちら（\nhttps://jp.globalsign.com/partner/faq.html\n）\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "日本の企業ではありませんが、証明書の発行は可能ですか", "answer": "『クイック認証SSL』は、オンラインの手続きのみで発行ができるので、海外の方でもお申し込みいただけます。ただし、お振り込み先口座が日本の銀行となっており、経理書類の海外発送には対応していないため、クレジットカードのご利用をお勧めいたします。『企業認証SSL』は、お問い合わせフォーム（\nhttps://jp.globalsign.com/contact/customer/\n）より詳細をお書き添えの上ご相談ください。『EV\n SSL』は、日本に登記のある企業のみ受け付けております。\n※弊社ではヨーロッパ、アメリカ、中国にて窓口を設けておりますので、こちら（\nhttps://jp.globalsign.com/cominfo/international_site.html\n）もご参照ください。\n\n※クイック認証SSLにおいても、発行をお断りする場合がございます。詳しくは「グローバルサインのフィッシングサイトに対する取り組み（\nhttps://info-globalsign.com/news/against_phishing\n）」をご参照ください。\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "契約者ではありませんが、お問い合わせは可能ですか", "answer": "SSLサーバ証明書の設定を代行されている方から、設定に関するお問い合わせをいただくことに問題はございません。ご利用状況を特定できる、コモンネームまたはオーダーIDをご用意のうえ、お問い合わせください。ご契約に関する個人的な情報は、いただいたお電話ではご案内いたしかねますので、その点はご了承ください。弊社の販売代理店を通してお申し込みの方は、契約上販売店でのサポートとなりますので、ご購入の業者様にお問い合わせくださいますようお願いいたします。", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "SSLサーバ証明書は個人でも利用可能ですか。", "answer": "クイック認証SSLは企業の実在証明を伴わないサービスのため、個人の方でもご導入いただけます。", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "ネットワークに制限がある環境でも利用できますか", "answer": "ネットワークに制限がある環境で証明書をご利用いただく場合、下記の点にご注意ください。証明書発行のための審査時証明書・ドメインの審査にページ認証をご利用いただく場合、弊社システムが対象のページへアクセス可能である必要がございます。そのため、ご希望の場合には弊社システムのページ認証時に利用するアクセス元IPアドレスをお知らせいたしますので、別途お問い合わせください。\n関連リンク\nドメイン名所有者・使用権の確認について（\nhttps://jp.globalsign.com/support/cert-management/domain-confirmation.html\n）\n\n\n証明書の失効確認\n一般的に、証明書を利用する際には、証明書の有効性を確認するための失効確認が行われます。失効確認を行う環境が、弊社のシステムへアクセス出来ない環境の場合、弊社のシステムから失効情報を取得出来ないため、失効確認が完了せず、正常にアクセスできない可能性がございます。失効確認を正常に行うためには、失効確認を行う環境にて、対象の証明書・中間CA証明書の失効確認時に利用するURLへアクセス可能な状態を構築していただく必要がございます。なお、失効確認を行う環境はご利用いただく商材によって下記のように異なります。\n\n・SSLサーバ証明書証明書を設定したサーバに対してアクセスを行うクライアント環境\n・クライアント証明書\nクライアント証明書の提示を受ける、サーバ環境\n・コードサイニング証明書、文書署名用証明書\n署名済みプログラムの検証を行う環境\n\nコードサイニング証明書、文書署名用証明書でのタイムスタンプ\n署名を行う際に、タイムスタンプを付与される場合、弊社システムへのアクセスが必要となります。タイムスタンプの付与はタイムスタンプURLを指定することで行うため、当該URLへアクセス可能な状態を構築していただく必要がございます。\n※タイムスタンプURLにつきましては、弊社署名マニュアルに記載がございます。", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "ライセンスについて教えてください", "answer": "証明書は1コモンネームにつき１枚必要です。同じ証明書を複数台のサーバで利用する場合は、1ライセンスのご契約で問題ございません。（サーバごとにライセンスを契約する必要はございません。）証明書や鍵の移行方法に関しては下記をご参照ください。サーバを変更しますが、何か手続きは必要ですか。（\nhttps://jp.globalsign.com/support/ssl/intro/transfer-cert.html\n）合計金額が一定の価格を超える場合、お得なまとめ買い割引がご利用いただけます。詳細は「おトクな標準付属サービス」内『【最大25%】まとめ買い割引』（\nhttps://jp.globalsign.com/service/#basic\n）をご参照ください。\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "1台のウェブサーバで複数のウェブサイトを立ち上げたい", "answer": "1台のウェブサーバで複数のウェブサイトを立ち上げたい場合、弊社ではお客様の状況に応じたオプションをご用意しております。\nマルチドメインオプション\n複数のドメインを1台のウェブサーバで運用する場合、企業認証SSL・EV SSLのマルチドメインオプション（有償）をご利用いただきますと、1枚の証明書で複数の異なるドメインのサイトのSSL通信が可能となります。本オプションは、企業認証SSL・EV SSLにてご利用いただけます。詳しくは（\nhttps://jp.globalsign.com/ssl/products_price/mdomainssl.html\n）をご参照ください。\n\nワイルドカードオプション\nドメインが同一で、複数のサブドメインを1台のウェブサーバで運用する場合は、ワイルドカードオプション（有償）をご利用いただきますと、1枚の証明書で複数サブドメインのサイトのSSL通信が可能となります。こちらはクイック認証SSLと企業認証SSLでご利用いただけます。詳しくは（\nhttps://jp.globalsign.com/ssl/products_price/wildcardssl.html\n）をご参照ください。\n\n※2021年11月29日の仕様変更に伴い、ページ認証ではワイルドカード証明書の発行は出来ませんのでご注意ください。詳細は（\nhttps://info-globalsign.com/news/20210917\n）\n\n上記オプションをご利用いただかずに、それぞれのウェブサイトに個別に証明書を設定いただく場合には、ウェブサイト毎にIPアドレスの設定が必要となります。なお、バーチャルホスト毎に異なるグローバルIPアドレスを割り当てていない場合、通常は複数のSSLサーバ証明書を設定することができません。\n\n関連リンク\nIISでワイルドカード/マルチドメイン証明書を利用する方法（\nhttps://jp.globalsign.com/support/ssl/intro/iis6-mdomain-wildcard.html\n）\n\nApacheでワイルドカード／マルチドメイン証明書を利用する方法（\nhttps://jp.globalsign.com/support/ssl/intro/apache-mdomain-wildcard.html\n）\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "複数のドメイン名を1つの証明書で登録できますか", "answer": "企業認証SSLまたはEV SSLのマルチドメインオプション（\nhttps://jp.globalsign.com/ssl/products_price/mdomainssl.html\n）をご利用いただくことで、複数ドメイン名を1つの証明書で登録することが可能となります。また、サブドメインが複数ある場合には、クイック認証SSLまたは企業認証SSLのワイルドカードオプション（\nhttps://jp.globalsign.com/ssl/products_price/wildcardssl.html\n）をご利用いただくことで、対応が可能です。\n\n※2021年11月29日の仕様変更に伴い、ページ認証ではワイルドカード証明書の発行は出来ませんのでご注意ください。詳細は（\nhttps://info-globalsign.com/news/20210917\n）\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "xxxx.jpとwww.xxxx.jpの両方を運用することは可能ですか", "answer": "コモンネームを www.+ドメイン名 で指定し、ドメイン認証をwww.無しのドメイン名で実施することで、2wayの機能が適用され、標準で証明書のSANにwww.無しのドメイン名が追加登録されます。これにより、www.有り無し両方のアクセスに利用可能な証明書となります。\n例：コモンネーム：www.globalsign.co.jp SAN：globalsign.co.jp\n ※ページ認証の場合、www.ありとなしの両方のページでドメイン認証を行う必要があります。\n詳細は（\nhttps://jp.globalsign.com/support/ssl/certificates/about-2way.html\n）\n\n※ワイルドカード証明書の場合も2wayが適用されます。\n詳細は（\nhttps://jp.globalsign.com/support/ssl/certificates/about-2way.html\n）\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "ワイルドカード証明書の運用について", "answer": "メールサーバや、FTPサーバ、Webサーバが同一ドメインの場合、ワイルドカード証明書をご利用いただくことで、1枚の証明書で運用することが可能です。\n例：ワイルドカード証明書のコモンネーム：　*.globalsign.com\n 受信メールサーバ： pop.globalsign.com\n 送信メールサーバ： smtp.globalsign.com\n FTPサーバ： ftp.globalsign.com\n ウェブサーバ： www.globalsign.com \nウェブサーバ2：　shop.globalsign.com \nワイルドカード証明書は、クイック認証、企業認証SSLのいずれにも組み合わせてご利用いただけます。\n※携帯電話のブラウザでは警告が出る場合はございます。モバイルサイトで利用の際はご注意ください。\n※2021年11月29日の仕様変更に伴い、ページ認証ではワイルドカード証明書の発行は出来ませんのでご注意ください。\n詳細は（\nhttps://info-globalsign.com/news/20210917\n）\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "マルチドメインオプションに関するよくある質問", "answer": "マルチドメインオプションに関するよくある質問をまとめました。詳細はこちら（\nhttps://jp.globalsign.com/support/ssl/intro/mdomain-faq.html\n）\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "Proxyサーバで使用できますか", "answer": "Proxyサーバでの動作につきましては、検証・実績はありませんが、x.509サーバ証明書に対応したCSRを生成する機能が装備されていればインストールが可能かと思われます。インターネット側よりアクセスされるURLをコモンネームとして、CSRを生成してください。", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "自社サーバにインストールすることはできますか", "answer": "ウェブサーバにSSLの機能があれば問題ありません。一般的なウェブサーバ(IIS、Apache)にはSSLの機能が実装されています。ウェブサーバの機能でCSRを作成いただき、申請後発行された証明書をインストールする形になります。ご不明な点がございましたらお問い合わせ（\nhttps://jp.globalsign.com/contact/customer/\n）ください。\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "レンタルサーバ（ホスティング）で利用することは可能ですか", "answer": "レンタルサーバ（ホスティング）でご利用いただくためには、ホスティングサービスが独自ドメイン名でのSSLに対応している必要があります。具体的には、 共用サーバサービスであれば専用のIPアドレスが割当てられたもの、専用サーバサービスであれば、ほとんどのものがご利用可能です。詳しくは、ホスティングサービス業者様へお問い合わせください。", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "注文フォームだけをSSLで暗号化することはできますか", "answer": "注文フォームのみSSLでセキュアにしたいということであれば、フォームに繋がるリンクの指定を \nhttps://で指定し、フォームが終了した先のリンクを\n \nhttp://に戻るように指定すればよろしいかと思います。現在のフォームを変更する必要はございませんが、URLの指定によってはアンカー部分のHTMLを手直しする必要があるかもしれません。\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "証明書のサブジェクトが、CSRと異なります", "answer": "「クイック認証SSL」では、ドメインの認証のみを行うため、CSRの内容と、発行される証明書のサブジェクト（ディスティングイッシュネーム）は一致しません。企業名や所在地にかかわる部分は、証明書発行時に削除、または上書きされますので、すべての値を反映させたい場合は、「企業認証SSL」のご利用をご検討ください。関連リンクディスティングイッシュネームとは何ですか（\nhttps://jp.globalsign.com/support/cert-management/distinguish-name.html\n）\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "OCNのホスティングサービスを利用中ですが", "answer": "OCNホスティングサービスをご利用の場合、こちら（\nhttps://ocngs.globalsign.com/\n）の専用ページをご覧ください。ホスティングサービスに付随する部分については、弊社ではご案内いたしかねますため、OCN様のサポートにご確認くださいますようお願いいたします。\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "日本語ドメイン名には対応していますか", "answer": "日本語のドメイン名（IDN：Internationalized Domain Name)は、DNSで名前解決をするため、PUNYコードという文字列に変換・符号化されます。この「PUNYコード」で証明書を発行することによってSSLをご利用いただくことは可能ですが、サイトシールは日本語表記に対応しておりません。\nお名前.com　→　xn--t8jx73hngb.com サイトシールで表示されるコモンネームは、上記例の場合「xn--t8jx73hngb.com」となります。また、ブラウザによっては、シール自体が表示されない場合もございます。ご了承ください。関連リンクIDN Conversion Tool（\nhttp://punycode.jp/\n）\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "グローバルサインでは、証明書の発行が早くなりますか", "answer": "国内で証明書を発行できますので、以前より迅速、柔軟な対応が可能です。また、条件次第となりますが、後払いによる証明書発行も可能です。", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "証明書を取得することによって、サイトの運営についても保証されるのでしょうか", "answer": "サイトの運営内容については保証されません。認証情報、証明される内容はサイトシールでご確認いただけますが、以下のとおりです。\n認証情報\nクイック認証SSL・コモンネーム・ステータス・有効期限\n企業認証SSL・コモンネーム・ステータス・組織名・住所・有効期限\nEV SSL・コモンネーム・有効期限・ステータス・組織名・事業所所在地国都道府県市区町村番地および丁目郵便番号電話番号・法人設立管轄地国登録番号\n\n証明される内容\nクイック認証SSL\nウェブサーバとブラウザ間の通信が暗号化されていること\n企業認証SSL / EV SSL\nウェブサーバとブラウザ間の通信が暗号化されていること\n[企業名]が実在し、[コモンネーム]を使用したウェブサイトの運営者であること\n※[ ]内は証明書の内容に置き換えてお読みください。関連情報サイトシールについて（\nhttps://jp.globalsign.com/ssl/guide/siteseal.html\n）\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "弁護士意見書・公認会計士意見書について", "answer": "グローバルサインでは、提出いただいた審査書類や第三者企業データベースの情報を元にEV SSL証明書発行のための認証作業を行います。提出いただいた審査書類や第三者企業データベースの情報で、申請組織の実在確認や署名権限の確認を行うことができない場合、「弁護士意見書」または「公認会計士意見書」をご提出いただき、その意見書で証明された項目に基づき認証を進めることが可能です。詳細は下記をご参照ください。詳細はこちら（\nhttps://jp.globalsign.com/support/ssl/evssl/writtenopinion.html\n）\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "CSRの作成方法は異なりますか", "answer": "CSRの生成方法は、通常の手順と変わりございません。『マニュアル：CSRの生成方法（\nhttps://jp.globalsign.com/support/ssl/manual-csr/\n）』をご確認ください。\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "EV SSLの再発行について", "answer": "下記詳細より手順をご確認ください。詳細はこちら（\nhttps://jp.globalsign.com/support/cert-management/ssl/reissue.html\n）\n", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "EV証明書の鍵長の制限について", "answer": "EV証明書の仕様を策定しているガイドラインにより、2010年12月31日以降に鍵長が2048bit未満のEV SSLを使用することは禁止されています。上記に伴い、EV SSLにつきまして2048bit未満の鍵長でのお申し込みの受け付けを停止いたしました。2048bit以上の鍵長でお申し込みください。", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}]