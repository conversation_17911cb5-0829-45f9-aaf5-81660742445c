[{"question": "サイバー攻撃とは？目的や手口、対策を解説\nサイバー攻撃とは", "answer": "サイバー攻撃とは、インターネットを含むネットワークを利用して、情報を盗んだり、システムを妨害したりする悪意のある行為を指します。攻撃者は、企業や個人のシステムに侵入し、データの改ざんや窃取、システム自体の破壊や停止などを目的としています。サイバー攻撃の手法は多岐にわたり、ウイルスやマルウェアの使用、ネットワークやWebアプリケーションに対するDDoS攻撃、さらにはフィッシングメールを通じてユーザーの認証情報を盗む行為などが挙げられます。企業にとっては、情報漏洩や業務停止といった深刻な影響を受ける可能性があり、対策が急務とされています。サイバー攻撃は年々高度化しており、単なる技術的な問題を超えて、社会的・経済的な影響をもたらす重要な課題となっています。\n参照：https://gmo-cybersecurity.com/column/security-measures/cyber-attack/", "company": "GMOサイバーセキュリティ byイエラエ", "service": "DDoS攻撃 マルウェア ランサムウェア フィッシング", "type": "blog"}, {"question": "サイバー攻撃とは？目的や手口、対策を解説\nサイバー攻撃の近年の動向", "answer": "もともとサイバー攻撃は攻撃者が技術を誇示する愉快犯などの側面もありましたが、近年では金銭や活動の妨害、機密の奪取等、直接的な目的の攻撃が主流を占め、企業や政府機関などが標的となるケースが増加しており、その手段もますます巧妙化しています。2020年以降、リモートワークやクラウドサービスの普及に伴い、サイバー攻撃の件数が急増しています。NICTが運用するネットワーク観測システム（NICTER）の調査によると、2023年のサイバー攻撃通信パケット数は2014年の約25倍にも上っています。\n参照：https://www.nict.go.jp/press/2024/02/13-1.html\nかつては個人や中小企業が主な標的でしたが、今では大企業やインフラ関連企業も攻撃対象となっており、被害額が億単位に及ぶことも珍しくありません。攻撃手法としては、ランサムウェアやフィッシングが主要な手口となっています。また、ゼロデイ攻撃(※1)やサプライチェーン攻撃(※2)といった、セキュリティの隙を突く高度な技術が多用されるようになっています。加えて、AIや機械学習を利用した攻撃が登場し、防御側も迅速な対応を求められています。\n※1ゼロデイ攻撃…ソフトウェアやシステムの脆弱性が一般に知られていない、または修正パッチがリリースされていない段階で行われる攻撃。\n※2サプライチェーン攻撃…製品やサービスの供給過程を狙った攻撃。これによりその顧客や利用者に被害が及ぶ。\n参照：https://gmo-cybersecurity.com/column/security-measures/cyber-attack/", "company": "GMOサイバーセキュリティ byイエラエ", "service": "ランサムウェア フィッシング ゼロデイ攻撃 サプライチェーン攻撃 DDoS攻撃 マルウェア", "type": "blog"}, {"question": "サイバー攻撃とは？目的や手口、対策を解説\nサイバー攻撃の目的", "answer": "サイバー攻撃の目的は多岐にわたりますが、代表的なものとして「金銭的利益の追求」が挙げられます。ランサムウェア攻撃は、システムやデータを暗号化し、元に戻すための身代金を要求する典型的な手法です。近年、企業や政府機関を標的とした大規模なランサムウェア攻撃が報告されており、多くの場合、攻撃者は暗号通貨での身代金を要求しています。もう一つの目的は、特定の企業の知的財産や機密情報を盗むことを狙った「産業スパイ活動」です。これらの情報は、攻撃者自身が利用する場合もありますが、競合企業や国家がバックにおり、情報の売買が行われることがあります。加えて、攻撃者の中には政治的またはイデオロギー的な動機で行動する者もおり、政府機関やインフラを狙った攻撃を通じて混乱や不安を引き起こすことを目的としています。\n参照：https://gmo-cybersecurity.com/column/security-measures/cyber-attack/", "company": "GMOサイバーセキュリティ byイエラエ", "service": "ランサムウェア攻撃, 産業スパイ活動, DDoS攻撃, マルウェア, フィッシング", "type": "blog"}, {"question": "サイバー攻撃とは？目的や手口、対策を解説\nサイバー攻撃の種類", "answer": "サイバー攻撃にはさまざまな種類があり、それぞれ手法や目的が異なります。代表的な攻撃手法を以下に紹介します。\n\n標的型攻撃：\n標的型攻撃は、特定の企業や個人を狙い撃ちする高度な攻撃手法です。攻撃者は長期間にわたり、システムに侵入し、機密情報を盗んだり、業務を妨害したりします。この攻撃は偵察から始まり、内部の脆弱性を発見してから侵入し、情報を盗むという慎重な手順を踏みます。攻撃が巧妙であるため、検出が難しく、被害が長期間にわたることが多いのが特徴です。\n\nDDoS攻撃（Distributed Denial of Service）：\nDDoS攻撃は、多数のコンピューターやボットネットを利用して、標的となるサーバーやネットワークに膨大なトラフィックを送り込み、サービスを一時的に停止させる攻撃です。企業やウェブサービスにアクセス障害を引き起こし、取引やサービス提供ができなくなるため、顧客の信頼を失う可能性があります。\n\nマルウェア：\nマルウェアは、システムに悪意のあるソフトウェアを感染させ、情報を盗む、システムを破壊する、あるいは不正な操作を行うプログラムです。ウイルス、トロイの木馬、スパイウェアなど、多様な形態をとります。攻撃者は、マルウェアを使ってシステムを乗っ取り、攻撃を行うための踏み台として利用することがあります。\n\n・マルウェアの種類\n- コンピューターウイルス\nコンピュータープログラムに自己複製し、他のプログラムやファイルに感染します。ユーザーの操作で広がることが多く、システム全体を混乱させます。\n- ワーム\n自己複製する点ではウイルスに似ていますが、ユーザーの操作を必要とせず、自動的にネットワークを通じて拡散します。\n- トロイの木馬\nその挙動から、ギリシャ神話の『トロイの木馬』がそのまま語源となっているマルウェアです。一見無害なソフトウェアに見えますが、実際にはシステムにバックドアを開け、攻撃者が遠隔操作できるようにします。\n- ランサムウェア\n被害者のデータを暗号化し、解除のために身代金を要求するマルウェアです。企業や個人に大きな経済的被害をもたらします。\n- スパイウェア\n実際のスパイのようにユーザーの行動を監視・ロギングすることで、個人情報や機密情報を盗むことを目的としたマルウェアです。\n- アドウェア\nAdvertise（広告）＋Softwareの造語です。ユーザーの意図しない広告を表示し、クリックさせることで広告収入を得るタイプのマルウェアが存在します。\n\n・マルウェアの感染経路\n- メールの添付ファイル\n最も一般的な感染手段の一つです。攻撃者は、マルウェアが埋め込まれたファイルをメールに添付し、ユーザーにそれを開かせようとします。添付ファイルを開くと、マルウェアがシステムにインストールされることがあります。\n- 不正なWebサイトやリンク\n攻撃者は、悪意のあるウェブサイトやリンクを通じてマルウェアを配布します。ユーザーが不注意にクリックすると、マルウェアが自動的にダウンロードされ、コンピューターに感染します。特に、セキュリティ証明書（HTTPS）がない、あるいは不適切なドメイン名を使用しているなど、信頼性の低いサイトには注意が必要です。\n- USBメモリや外部デバイス\n外部デバイスを使用してマルウェアを広げる手法もよく使われます。感染したUSBメモリや外部ハードディスクをシステムに接続すると、マルウェアがシステム内に侵入します。また、マルウェアが感染したPCに接続された外部デバイスを使いまわしていると、知らず知らずのうちに感染している事例もあるため、共有デバイスの使用には注意が必要です。\n- ソフトウェア・機器の脆弱性\n攻撃者は、ソフトウェアやネットワーク機器などの脆弱性を利用して、マルウェアをシステムに侵入させます。セキュリティパッチが適用されていないシステムは、特に攻撃に対して脆弱です。定期的なアップデートや脆弱性チェックが何よりも重要です。\n- ファイル共有ソフトやネットワーク\nピアツーピア（P2P）ネットワークなどのファイル共有サービスも、マルウェアの感染源となることがあります。不正なファイルや違法なダウンロードを通じて、知らぬ間にマルウェアをダウンロードしてしまうリスクがあります。\n\nランサムウェア：\nランサムウェアは、システムやデータを暗号化し、復旧のために身代金を要求する攻撃です。企業のデータを人質に取る形で金銭を要求するため、被害者は業務の継続が困難になります。ランサムウェアは、特に中小企業や医療機関が標的になることが多く、データのバックアップがなければ、深刻な業務停止や財務的損失を招く可能性があります。\n\n・ランサムウェアの種類\n- クリプトランサムウェア（Crypto Ransomware）\nクリプトランサムウェアは、最も一般的なタイプのランサムウェアで、システム内のファイルを暗号化し、被害者がそれらのファイルにアクセスできなくします。このタイプのランサムウェアは、復号キーを提供する代わりに身代金を要求するのが特徴です。代表的な例として、WannaCryが挙げられ、企業や公共機関を広範囲にわたって被害に巻き込んだことで知られています。\n- ロッカーランサムウェア（Locker Ransomware）\nロックランサムウェアは、システム全体をロックし、被害者がコンピューターを使用できなくするタイプです。この場合、ファイル自体は暗号化されませんが、コンピューターが完全に操作不能となるため、結果的に被害者はシステムにアクセスできなくなります。コンピューターを再び使えるようにするために、攻撃者に対して身代金を支払う必要があります。\n- 二重恐喝（Double Extortion）\nExtortion(恐喝・強要）の意味のとおり、ダブルエクストーション型のランサムウェアは、単にデータを暗号化するだけでなく、被害者のデータを盗み出し、そのデータを公開しないために追加の身代金を要求します。これにより、被害者はデータの復元と情報漏えい防止の二重の脅迫に直面します。この手法は特に、企業の機密情報や顧客データを持つ組織を狙う場合に多く使われます。\n- 多面的恐喝（Multifaceted Extortion）\n最近のランサムウェア攻撃では、単に暗号化やデータの盗難だけでなく、DDoS（分散型サービス拒否）攻撃や、企業の評判を傷つけるキャンペーンなど、複数の攻撃手段を組み合わせた手法が用いられます。攻撃者はこれらの複数の脅威を利用して、被害者にさらなるプレッシャーをかけ、身代金の支払いを強要します。\n\n・ランサムウェアの感染経路\n- VPN機器の脆弱性\nリモートワークの増加に伴い、VPNは企業における重要な接続手段となっていますが、そのセキュリティが不十分だと、攻撃者にとって侵入経路となります。特に、古いファームウェアを使用しているVPN機器や、既知の脆弱性を放置している場合、攻撃者はこれらの脆弱性を悪用し、ネットワークに侵入します。攻撃者は侵入後、脆弱性を持つ社内のシステムを探し、管理権限を奪取し、その後ランサムウェアを展開します。\n- リモートデスクトップ（RDP）\nリモートデスクトッププロトコル（RDP）も、ランサムウェア攻撃の主要な侵入手段の一つです。RDPを利用してリモートアクセスを行っている企業が、強力な認証や多要素認証（MFA）を設定していない場合、攻撃者はブルートフォース攻撃（総当たり攻撃）や盗まれた認証情報を使用して、不正にシステムにアクセスします。一度アクセスに成功すると、攻撃者は社内ネットワークを探索し、重要なデータを暗号化します。\n- 標的型メール\n標的型メールは、依然として最も多いランサムウェアの感染経路の一つです。攻撃者は企業の従業員に対し、信頼できる送信者を装ったメールを送り、その中にある悪意のあるリンクをクリックさせたり、添付ファイルを開かせたりします。これにより、ランサムウェアが自動的にダウンロードされ、企業のネットワークに感染が広がるケースが頻繁に見られます。特に、従業員のセキュリティ意識が低い場合、この手法は非常に効果的です。\n\nフィッシング：\nフィッシングは、メールや偽のウェブサイトを通じて、ユーザーからパスワードやクレジットカード情報を盗む手口です。巧妙に作られたメールやメッセージで正規の組織やサービスを装い、ユーザーをだまして個人情報を入力させます。この手法は、セキュリティ意識が低い個人を狙った攻撃に多く利用されます。\n参照：https://gmo-cybersecurity.com/column/security-measures/cyber-attack/", "company": "GMOサイバーセキュリティ byイエラエ", "service": "標的型攻撃 DDoS攻撃 マルウェア コンピューターウイルス ワーム トロイの木馬 ランサムウェア クリプトランサムウェア ロッカーランサムウェア 二重恐喝 多面的恐喝 フィッシング RDP脆弱性 VPN脆弱性", "type": "blog"}, {"question": "サイバー攻撃とは？目的や手口、対策を解説\nサイバー攻撃が企業に与える影響", "answer": "サイバー攻撃による被害は、企業にとって多岐にわたる損失をもたらします。\n\n財務的損失：\nサイバー攻撃による直接的な財務的損失は、データ漏洩、業務停止、復旧費用、罰金などにより非常に大きなものとなります。特にランサムウェア攻撃の場合、システムの復旧やデータの復元にかかる費用が莫大です。場合によっては身代金の支払いを選択せざるを得ないこともあるかもしれません。これにより、中小企業は深刻な経営危機に陥ることがあります。\n\nブランドイメージの低下：\nデータ漏洩やサービスの停止は、顧客の信頼を失わせ、ブランドイメージに悪影響を与えます。顧客情報や機密情報が流出した場合、信頼回復には長い時間がかかり、顧客離れが進行する可能性があります。企業は、その影響を最小限に抑えるため、迅速な対応と透明性の高い情報開示が求められます。\n\n法的責任：\nデータ漏洩やセキュリティ侵害が発生した場合、企業は法的責任を問われる可能性があります。個人情報保護法やGDPRなどの規制が強化されている現代では、違反に対する罰金や賠償金が高額になるケースが増えています。これにより、企業は経済的損失だけでなく、法的な問題にも直面し、長期的な対応を迫られることがあります。\n参照：https://gmo-cybersecurity.com/column/security-measures/cyber-attack/", "company": "GMOサイバーセキュリティ byイエラエ", "service": "ランサムウェア攻撃, 標的型攻撃, DDoS攻撃, マルウェア, フィッシング", "type": "blog"}, {"question": "サイバー攻撃とは？目的や手口、対策を解説\n企業の情報セキュリティ担当者が行うべきサイバー攻撃の対策", "answer": "社内ネットワークの強化や社員教育の徹底：\n企業がサイバー攻撃に対抗するためには、社内のネットワーク利用環境の強化が不可欠です。近年ではほとんどの企業において、社内のローカルネットワークの枠を超えて、外部サービスの利用が業務に組み込まれています。そのような環境では、まず、利用ユーザーの認証を強化する事が重要になります。多要素認証（MFA）の導入は、ユーザーのIDやパスワードが漏洩した場合でも、不正アクセスを防ぐための基本的な対策です。また一方で、ローカルネットワークの機密性を前提とした環境も、まだ手放せないという状況が多くあります。そのような環境に対し、ローカルネットワークとインターネットの接続点の機器（VPN等）の脆弱性を突き、外部からの侵入を行う攻撃パターンが大変増えていますので、これらの機器の把握と脆弱性の管理が重要度を増しています。さらに、データの定期的なバックアップを実施することで、ランサムウェアなどによるデータ損失リスクに備えることができます。これに加え、社員教育も欠かせません。フィッシング攻撃などは人のミスを狙ったものが多いため、定期的なセキュリティトレーニングや演習を行い、全社員の意識を高めることには、一定の効果が期待できます。\n\n外部公開資産やサービスの脆弱性診断：\n外部に公開している資産やサービスには、常に脆弱性が発見されるリスクがあります。特にWebサイトやSaaS（Software as a Service）などのクラウドベースのサービスは、インターネットを介してアクセス可能であるため、攻撃者にとって格好の標的となります。セキュリティベンダーが提供する脆弱性診断サービスや脆弱性診断ツールを活用することで、自社の資産に潜むリスクを早期に発見し、対策を講じることができます。これにはWebサイトやSaaSに限らず、クラウド、IoTデバイス、さらにはネットワークに接続されているすべての資産が対象となります。\n定期的な診断を行うことで、潜在的な脆弱性を発見し、サイバー攻撃のリスクを低減させることが可能です。\n\nCSIRTなどのセキュリティ組織の組成：\n企業内でサイバー攻撃に対抗する組織として、CSIRT（Computer Security Incident Response Team）の設置が重要です。CSIRTは、サイバーインシデント発生時に迅速な対応を行う専門チームで、攻撃の被害を最小限に抑えるための計画立案やインシデント対応を担当します。CSIRTを組成するためには、セキュリティに精通したメンバーの選定や、適切なツールの導入が必要です。また、CSIRTの組成を支援するコンサルティングサービスや、運営サポートを提供する外部の専門機関を活用することも有効です。これにより、企業はインシデント発生時に迅速かつ的確な対応が可能となり、リスクの拡大を防ぐことができます。\n\nインシデント対応計画の策定：\nインシデントが発生した際に迅速かつ効果的に対応するためには、事前にCSIRP（Cyber Security Incident Response Plan：サイバーセキュリティインシデント対応計画）を策定しておく必要があります。CSIRPは、インシデント発生時の対応フローや責任分担、報告手順、復旧計画などを具体的に定めたもので、組織全体が一致団結してインシデントに対処できるようにするためのガイドラインです。計画の中には、初期対応、インシデントの封じ込め、原因の特定、復旧プロセス、さらには再発防止策までが含まれます。これを定期的に見直し、訓練を行うことで、実際のインシデント発生時に混乱することなく対応でき、企業のダメージを最小限に抑えることが可能です。\n参照：https://gmo-cybersecurity.com/column/security-measures/cyber-attack/", "company": "GMOサイバーセキュリティ byイエラエ", "service": "多要素認証（MFA）, VPN脆弱性, ランサムウェア, フィッシング攻撃, 脆弱性診断, CSIRT, CSIRP", "type": "blog"}, {"question": "脆弱性診断ツールのメリット・デメリット、選び方のポイント\n脆弱性診断ツールのメリットとデメリット", "answer": "1. 脆弱性診断ツールのメリット3点\n①定期診断の容易さ\n脆弱性診断ツールの利用は、特に定期診断の実施において大きなメリットを提供します。セキュリティ環境は常に変化しており、新たな脆弱性が日々発見されています。そのため、定期的な診断が非常に重要ですが、手動での診断は人手・時間・コストがかかり実施が難しいことがあります。脆弱性診断ツールの中には、スケジュール設定による自動実施が可能なツールもあります。また通知結果から自動的にレポートを生成する機能がついている場合もあります。これらの機能により、迅速に脆弱性を把握することが可能となり、常にシステムのセキュリティ状態を最新の状態に保つことができます。したがって安全なIT環境を維持するために必要な定期診断を、より効率的かつ効果的に行うことが可能となります。\n②コスト効率の向上\n次にあげられるメリットとしては、コスト効率の良さです。第三者による手動診断サービスは、専門のセキュリティエキスパートが行うため、高額な費用がかかることがあります。一方で、ツール診断では自動化されているため、一度設定すれば複数回の診断を追加のコストなく実施できる場合があります。これにより、特に予算に限りがある企業にとっては、セキュリティ対策のコストを大幅に削減できるというメリットがあります。\n③ 人に依存しない脆弱性の特定\n脆弱性診断においては、最新の情報に基づいて抜け漏れのない診断を行うことが何よりも重要です。手動診断では、診断する人のスキルや経験に大きく依存する場合があります。一方で脆弱性診断ツールは、最新の脆弱性データベースを基に迅速にシステムをスキャンし、一定の品質でリスクを特定することができます。また、短時間で広範囲の脆弱性を検出可能です。これにより、全社的に統一の基準で診断を行うことができ、セキュリティレベルの維持・向上に寄与します。\n\n2. 脆弱性診断ツールのデメリット3点\n①専門人材の確保と育成コスト\n脆弱性診断ツールの適切な運用とその結果に関する対応策の立案には、専門的な知識を持った人材が必要不可欠です。これらの人材の確保は、特に多くの企業にとっては時間とコストを要する大きな課題です。専門人材を外部から獲得する場合、そもそも市場に人材が少なく採用母集団の形成ができません。また高い給与など魅力的な待遇を設定する必要があり、非常に難易度が高くコストがかかります。更に内部で育成しようとすると、長期的な教育プログラムと実践経験など研修や教育コストが必要となります。サイバー攻撃手法の複雑化とそれに伴う脆弱性診断ツールの急速な進化に対応するためには、これらの人材に対する継続的な研修が不可欠であり、そのためのコストも考慮する必要があります。専門人材の不足は、ツールの潜在能力を十分に引き出せないリスクを生じさせ、結果としてセキュリティ対策の効果を損なう可能性があります。このため、専門人材の確保と育成に関する戦略的な計画が、セキュリティ対策の成功には不可欠です。\n②人為的な脆弱性は診断できない\n脆弱性診断ツールは、あくまでもシステムなどにおける脆弱性を診断します。セキュリティに対する脆弱性は、居室環境といった物理的な環境が起因するものや、アプリケーションの実装面における脆弱性も含まれることがあります。具体的には、前者であればUSBなどの機器について適切な保管や廃棄のプロセスが定められていないことによる情報漏洩、後者であればセッションタイムアウトが長すぎる、またはタイムアウト後にセッションIDが無効にならない実装をしている場合攻撃者がセッションを乗っ取る機会が増える、などです。手動診断では、専門家がその場の状況を判断し、直感や経験を駆使してこれらの脆弱性を発見することも可能ですが、脆弱性診断ツールでこのような脆弱性を発見することは不可能です。\n③誤検知と見逃しの問題\n脆弱性診断ツールを使用する際の大きな課題の一つが、誤検知（フォールスポジティブ）と見逃し（フォールスネガティブ）です。誤検知は、実際には安全な要素を脆弱性があると誤って識別し、セキュリティチームが不必要な修正作業に時間を割くことを強いられる結果につながります。一方、見逃しは、実際に脆弱性が存在するにもかかわらず検出できないケースを指し、攻撃者による悪用のリスクを残します。ツールはAIなどを駆使して、このような誤検知や見逃しを最小限にしていますが、どうしても発生するものとして認識しておく必要があります。\n\n脆弱性診断ツールの種類：\n\n・ソフトウェア型・クラウド型\n脆弱性診断ツールには、ソフトウェア型とクラウド型があります。\nソフトウェア型脆弱性診断ツールは、ユーザーが直接自身のシステムにインストールして使用するタイプのツールです。これにより、内部ネットワークやアプリケーションのセキュリティスキャンをローカルで実行することができます。インストール型の利点は、オフライン環境での使用が可能であることや、内部ネットワークにおける細かいセキュリティ設定の検証が行える点にあります。クラウド型脆弱性診断ツールは、インターネットを介してサービス提供者のクラウドプラットフォーム上で動作します。ユーザーはブラウザやAPIを通じてサービスにアクセスし、外部からのセキュリティスキャンを実施できます。クラウド型のメリットは、最新のセキュリティ脅威に対する自動更新や、どこからでもアクセス可能な柔軟性がある点です。また、設定や管理が簡単であり、拡張性にも優れています。\n参照：https://gmo-cybersecurity.com/column/assessment/tool/", "company": "GMOサイバーセキュリティ byイエラエ", "service": "脆弱性診断ツール、セッションハイジャック、フォールスポジティブ、フォールスネガティブ、クラウド型脆弱性診断ツール、ソフトウェア型脆弱性診断ツール", "type": "blog"}, {"question": "脆弱性診断ツールのメリット・デメリット、選び方のポイント\n自社に合う脆弱性診断ツールを選ぶ際のポイント", "answer": "ポイント1：コスト\n脆弱性診断ツールを選択する際、最も重要な観点はコストです。初期導入コスト、ランニングコスト、運用のしやすさ、そして特定の環境や要件に適した機能性について考慮することが重要です。以下に、これらの観点を含めた選択のポイントを詳細に説明します。\n\nイニシャルコスト：脆弱性診断ツールの選択にあたって、最初に考慮すべきはイニシャルコストです。高機能でサポートが充実しているツールは高価になる傾向があります。\n一方で、オープンソースツールは無料または低コストで利用開始できますが、自身でのカスタマイズや問題発生時のサポートにリソースが必要となる場合があります。導入前には、ツールの価格だけでなく、設定やカスタマイズに要するコストも含めた全体的な初期投資を検討することが重要です。\n\nランニングコスト：次に、ライセンス更新費用、クラウドサービス利用料金といったランニングコストを考慮します。セキュリティ専門家の継続的な教育やトレーニング費用もランニングコストに含まれます。\nイニシャルとランニングを含めて、導入から3～5年にかかる総コストを基準に投資判断を行うといいでしょう。\n運用のしやすさ：ツールの運用のしやすさは、日々の作業効率に大きく影響します。直感的なユーザーインターフェース、簡単な設定プロセス、自動化されたスキャン機能、わかりやすいレポート出力機能などが、運用のしやすさを高める要素です。運用にあたっては、ツールが組織の既存のセキュリティポリシーとシームレスに統合できるか、カスタマイズが可能かどうかも重要なポイントです。\n\n1サイトあたりのコストとスケーラビリティ：特に複数のウェブサイトやアプリケーションを運用している場合、1サイトあたりのコストとツールのスケーラビリティは重要な考慮事項です。脆弱性診断ツールがスケールアウトしやすいか、複数のプロジェクトやサイトに対応できる柔軟性があるかを確認します。\n\nポイント2：対象範囲\nツールの特質：脆弱性診断ツールを選択する際は、診断対象の範囲や深度、ツールに求める機能性を明確にすることが重要です。まず、診断したい対象がWebアプリケーションなのか、ネットワークインフラなのか、またはその両方を含む広範なプラットフォームなのかを特定します。Web診断に特化したツールは、ウェブアプリケーションの脆弱性を詳細に分析する高度な機能を提供しますが、ネットワークレベルの脆弱性はカバーできない場合があります。逆に、プラットフォーム全体の脆弱性を検出するツールは、システムの広範囲にわたるセキュリティ状態を把握するのに適していますが、特定のアプリケーションの深い分析には限界があるかもしれません。\n\n診断の深さ：診断の深度に関して考慮が必要です。浅く広い範囲をスキャンして概要を把握したい場合は、高速で広範囲をカバーするツールが適しています。一方で、特定のシステムやアプリケーションに存在する深い脆弱性を詳細に分析したい場合は、細かい設定やカスタマイズが可能な、より専門的なツールを選ぶべきです。\n\nポイント3：サポートの有無\nツールが提供するサポート体制：脆弱性診断ツールを選択する際には、サポート体制の充実度が重要な判断基準の一つです。サポートの有無やその質は、ツールの効果的な運用やトラブル発生時の対応速度に直接影響を与えます。サポートサービスは、メールベースの問い合わせ対応から、電話やオンラインミーティングによる直接のサポート、さらには緊急時の24時間対応まで、提供範囲は様々です。メールのみのサポートでは、問題解決までに時間がかかる可能性があり、迅速な対応が求められる状況では不十分かもしれません。一方で、オンラインミーティングによるサポートや緊急対応が可能なサービスでは、より具体的かつ迅速な解決策を提供できるため、運用上の安心感が大きくなります。\n運用する人材のスキル：脆弱性診断ツールの運用には、一定レベルの技術的知識が必要とされるため、サポート体制は社内の人材レベルにも影響を及ぼします。専門的なセキュリティ知識が豊富なチームであれば、サポートの利用頻度は低くても問題ありませんが、セキュリティ専門家が不在、または経験が浅い場合は、充実したサポート体制があるツールの方が適しています。\n\nポイント4：同時実行できるシステム数\n同時実行数：多くの企業では、複数のシステムやアプリケーションを同時に運用しています。これら全てに対して脆弱性診断を効率的に実行するためには、同時実行数が何よりも重要です。ツールによっては同時にスキャンできるシステムの数に制限があり、大規模なインフラストラクチャを持つ組織にとっては、この制限が診断作業の効率に大きく影響します。資産管理機能の有無：ツールによっては、社内にどのようなシステムやWebアプリケーションがあるのか、一覧を表示して保存する機能を持つツールもあります。これにより、企業内でどのようなIT資産を保有しているか可視化できます。可視化することで、脆弱性診断を急ぎ対応する必要があるものやそうでないものを区別することができるため、脆弱性ツールの同時実行数が少なくても優先順位をつけて対応ができます。\n参照：https://gmo-cybersecurity.com/column/assessment/tool/", "company": "GMOサイバーセキュリティ byイエラエ", "service": "脆弱性診断ツール, オープンソースツール, 自動化スキャン機能, 資産管理機能, スケーラビリティ, 同時実行数", "type": "blog"}, {"question": "脆弱性診断ツールのメリット・デメリット、選び方のポイント\n脆弱性診断ツールを利用するにあたっての留意点", "answer": "一つの手段ですべてのリスクが防げるわけではない\n脆弱性診断ツールを利用することは、セキュリティリスクを軽減する重要な手段の一つですが、すべてのリスクを防ぐ万能の解決策ではありません。これらのツールは主に情報システムに対する既知の脆弱性や一般的なセキュリティの弱点を検出するために設計されており、新たに発見される脆弱性に対応できないケースも存在します。\nまた、人為的なミスなどに起因するセキュリティ問題は検知することが難しいです。ペネトレーションテストや専門家の業務プロセスに対するコンサルティングといった、様々なサービスを組み合わせることでセキュリティ対策は初めて万全になるといえるでしょう。\n\n診断をしていない対象にもリスクがある可能性\nセキュリティを万全にするためには、ツールやサービスを組み合わせることと同時に、全社的なガバナンスや周知の仕組みも重要です。\nたとえば、IT部門が把握せず、他の部門で勝手に公開されたWebサイトなどが存在する場合、これらは通常の脆弱性診断の範囲外となりがちです。しかし、攻撃者はこれらの見落とされた資産を標的にすることがあり、組織全体のセキュリティを脅かすことになります。そのため、全社的な情報システムの資産を網羅的に把握する仕組みを作るのは勿論のこと、強制力を持たせる社内規程等に文書化することも非常に重要です。\n参照：https://gmo-cybersecurity.com/column/assessment/tool/", "company": "GMOサイバーセキュリティ byイエラエ", "service": "脆弱性診断ツール, ペネトレーションテスト, 情報システム資産管理, セキュリティガバナンス", "type": "blog"}, {"question": "なりすまし・フィッシングメール対策に有効なDMARC設定！国内企業の設定状況を確認してみた（情報・通信業界編）\n各企業のDMARC設定状況", "answer": "情報・通信業界のDMARC設定状況には企業間で差が見られます。例えばNTTやLINEヤフーなどは、「quarantine」ポリシーを設定し、不正メールを隔離する対策を講じています。一方で、「none」や「DMARC\"なし\"」の企業は 75％ を占め、なりすまし・フィッシング詐欺の完全なブロックには至っていないようです。業界全体で見ると、DMARCを「reject」ポリシーに設定している企業はほとんど見られず、セキュリティ対策としては今後さらなる強化が望まれます。\n参照：https://brandtoday.media/2024/12/10/dmarc-20241210/", "company": "GMOブランドセキュリティ", "service": "DMARC設定, quarantineポリシー, noneポリシー, rejectポリシー, フィッシング詐欺", "type": "blog"}, {"question": "なりすまし・フィッシングメール対策に有効なDMARC設定！国内企業の設定状況を確認してみた（情報・通信業界編）\nDMARC導入による信頼性とブランド価値の向上", "answer": "DMARCを導入することで、自社ドメインを悪用したフィッシング詐欺を防ぎ、顧客や取引先からの信頼を高めることができます。また、BIMI と連携することで、受信者のメールアプリにブランドロゴを表示し、メールが自社からのものであることを視覚的に証明できるため、ブランド認知が向上します。これにより、受信者がメールを信頼しやすくなり、開封率の向上にもつながります。さらに、認証済みのメールがスパムフォルダに入るリスクが低減し、メール受信率が向上します。また、DMARCのレポート機能により、不正メールの発生状況を可視化し、セキュリティ対策を強化することも可能です。ブランドロゴが表示されることで、日常的にブランドに触れる機会が増え、セキュリティ対策とブランドマーケティングの同時強化が実現します（ブランドセキュリティ）\n参照：https://brandtoday.media/2024/12/10/dmarc-20241210/", "company": "GMOブランドセキュリティ", "service": "DMARC, BIMI, フィッシング詐欺", "type": "blog"}, {"question": "メールの信頼性を守るために：DMARCだけではなぜ不十分なのか？\nはじめに", "answer": "ある日、メールを受信したお客様が「これ、偽物かも？」と疑念を抱き、本来なら安心して利用されるはずの企業のメールが無視されてしまう――。\n2023年8月に話題となった事例があります。ある銀行がメールのなりすまし対策として DMARC を正しく設定していたにもかかわらず、メール受信者に「なりすまし」と誤解されてしまい、フィッシング対策協議会に、フィッシングとして報告されてしまいました。この問題の背景には、受信者が “目で見て” 本物かどうか判断できない という課題があります。この課題を解決するのが BIMI/VMC です。\n\nこの記事では、その重要性とメリットを具体的な事例を交えて解説します。\n\n# なりすましとは？\nなりすましとは、他人や組織を詐称してコミュニケーションを取る行為のことです。例えば、悪意のある攻撃者が本人や企業になりすまし、個人情報の窃取や金銭的被害、風評被害などを引き起こします。オンライン上でのなりすましの手口としては、偽のメールやWebサイトを使用して、ユーザーの個人情報を不正に入手するフィッシング攻撃が挙げられます。\nこうした巧妙ななりすましは、世界中で深刻な問題として扱われており、多くの個人や企業が被害に遭っているのが現状です。なりすましによる被害を防ぐためには、セキュリティ意識の向上と適切な対策が不可欠です。\n\nなりすましの代表的な手口：\n・フィッシング攻撃\n偽のメールアドレスや偽のWebサイトを使用して、ユーザーの個人情報や銀行口座などの情報を不正に入手する手口です。この偽のメールはフィッシングメール、偽のウェブサイトはフィッシングサイトと呼ばれます。攻撃者は金融機関や有名企業を装ってフィッシングメールを送り、そこからフィッシングサイトにアクセスさせてログイン情報の入力を求めるケースがほとんどです。\nかつてのフィッシングメールは、たどたどしい日本語で書かれていたものも多く、偽のメールと判断しやすかったですが、近年はAIや機械翻訳の技術が発展したことにより本物と見分けるのが困難なフィッシング攻撃が行われています。\n・SNSなりすまし\n　- 有名人、企業、知人になりすまして、詐欺や偽情報の拡散を行う手口です。\n参照：https://brandtoday.media/2024/12/12/dmarc20241212/", "company": "GMOブランドセキュリティ", "service": "DMARC設定, BIMI, VMC, フィッシング攻撃, フィッシングメール, フィッシングサイト, SNSなりすまし", "type": "blog"}, {"question": "メールの信頼性を守るために：DMARCだけではなぜ不十分なのか？\n事例", "answer": "DMARC設定が正しいのに「偽物」と誤解された理由：\nこの事例では、メール詐欺の注意喚起メールがS/MIME署名あり、SPF/DKIM/DMARC設定ありの状態で、不正なメールは技術的に弾かれる仕組みが整っていました。\nしかし、フィッシング対策協議会に、本物の注意喚起メールがフィッシングとして報告されてしまいました。もしかすると、メールを受け取った人はこう感じたのかもしれません。\n・「ロゴもアイコンもないから、怪しい…」\n・「メールの送信元が本当にあの企業かどうか分からない」\nこのような誤解は、技術的な設定だけでは解消できません。受信者が “目で見て” 本物と判断するための仕組み が必要になります。\n参照：https://brandtoday.media/2024/12/12/dmarc20241212/", "company": "GMOブランドセキュリティ", "service": "DMARC設定, SPF, DKIM, S/MIME署名, フィッシング対策協議会", "type": "blog"}, {"question": "メールの信頼性を守るために：DMARCだけではなぜ不十分なのか？\nBIMI/VMCが解決する視覚的信頼性の課題", "answer": "この課題を解決するのが BIMI（ブランド指標メール識別子） と VMC（認証マーク証明書） です。ここで、BIMIとVMCについて簡単におさらいします。\n・BIMI\n　- メールに企業のロゴを表示する仕組みです。受信者はメールを開く前にロゴを見ることで、送信元が信頼できる企業であることを視覚的に確認できます。\n・VMC\n　- 表示されるロゴが正当な権利者のものであることを証明します。これにより、偽ロゴの使用を防ぎ、ロゴの信頼性をさらに高めます。\n\nさらに、GmailではBIMI対応のメールにチェックマークアイコンが表示される機能が導入されています。このアイコンは、ロゴが認証されたものであることを示し、受信者にさらなる安心感を与えます。ロゴだけでは伝えきれない「このメールは信頼できる」というメッセージを直感的に伝え、視覚的に「本物」であることを明確に示します。また、認証を受けていない偽ロゴとの差別化を図り、なりすましのリスクを最小限に抑える効果もあります。心理的な安心感を与えるチェックマークは、受信者が安心してメールを開封するきっかけにもなり、結果として開封率の向上やブランド信頼性の強化につながります。もしこの機能が活用されていれば、「本物のメールが偽物と誤解される」問題を防げた可能性があります。\n参照：https://brandtoday.media/2024/12/12/dmarc20241212/", "company": "GMOブランドセキュリティ", "service": "BIMI, VMC, DMARC, チェックマークアイコン, ブランド指標メール識別子, 認証マーク証明書", "type": "blog"}, {"question": "メールの信頼性を守るために：DMARCだけではなぜ不十分なのか？\nBIMI/VMC導入のメリット", "answer": "マーケティング効果にも注目！：\nBIMI/VMCの導入は、視覚的な信頼性を向上させるだけでなく、マーケティング効果にも大きなインパクトを与えます。\n\n1. 開封率の向上\n　BIMIの導入により、メールの開封率が最大39%向上する可能性があることが示されています。*3　ロゴが表示されることで受信者の注意を引き、他のメールとの差別化を図れるためです。\n2. ブランドの信頼性向上\n　メールにロゴが表示されることで受信者が企業を視覚的に認識しやすくなり、次回以降のメールでも信頼感を感じやすくなります。\n3. お客様体験の向上\n　ロゴとチェックマークが視覚的に信頼性を伝えることで、お客様は安心してメールを開き、行動（例：リンクのクリック、フォーム記入）を起こしやすくなります。\n4. メールマーケティングのROI向上\n　視覚的な効果により開封率やクリック率が向上し、メールマーケティング全体の費用対効果（ROI）が向上します。\n参照：https://brandtoday.media/2024/12/12/dmarc20241212/", "company": "GMOブランドセキュリティ", "service": "BIMI, VMC, DMARC", "type": "blog"}, {"question": "メールの信頼性を守るために：DMARCだけではなぜ不十分なのか？\nまとめ", "answer": "DMARCだけでは足りない理由とBIMI/VMCの必要性：\nDMARCだけでは視覚的な信頼性を補完できない現実 があります。BIMI/VMC を導入することで、技術的な保証に加え、視覚的な信頼性を提供できます。それにより、お客様の安心感を向上させるだけでなく、開封率やマーケティング効果の向上といった付加価値も得られます。\n参照：https://brandtoday.media/2024/12/12/dmarc20241212/", "company": "GMOブランドセキュリティ", "service": "DMARC, BIMI, VMC", "type": "blog"}, {"question": "クライアント証明書認証の安全性が確保される、CA秘密鍵保護の重要性\nクライアント証明書による認証が安心といえる理由", "answer": "クライアント証明書による認証方式が安心といえる理由は、その証明書を発行している認証局（CA）の「鍵」が厳重に保護されているから、と弊社では考えています。逆に言うと、CAの鍵（秘密にすべき鍵のため「CA秘密鍵」といいます）が高いレベルで保護されていなければ、システム管理者が意図しない不正な証明書を発行されかねないというセキュリティリスクが生じます。つまり、そのCAへの信頼性は十分に担保されているとは言えず、結果として、そのCAから発行されたクライアント証明書による認証の安全性にも疑問が呈される状態にあると考えられます。なお、これはクライアント証明書に限らず、TLSサーバ用の証明書や各種電子署名に使う証明書などにも当てはまります。よって、たとえプライベート環境での利用であっても、CA秘密鍵の保護に関しては慎重に設計されるべきといえるでしょう。\n参照：https://college.globalsign.com/blog/clientcert_privatekey_230509/", "company": "GMOグローバルサイン", "service": "クライアント証明書, 認証局(CA), CA秘密鍵, TLSサーバ証明書, 電子署名", "type": "blog"}, {"question": "クライアント証明書認証の安全性が確保される、CA秘密鍵保護の重要性\n安全なCA秘密鍵の保護の方法", "answer": "一般的に、クライアント証明書の発行は、共用CAによる証明書発行サービス、あるいはプライベートCA機能を有するソフトウェアやアプライアンス製品などにより行われるケースが多いかと思います。それらのサービスやソフトウェアによりCA秘密鍵の管理手法は異なりますが、大別してサーバ機器の内部で管理されるケースと、ハードウェア・セキュリティ・モジュール（HSM）と呼ばれる鍵保護のための専用装置内で管理されるケースがあります。サーバ機器内部でCA秘密鍵が管理される場合は、ソフトウェア上でその鍵を使った演算（署名）処理が行われますが、そこに脆弱性などの不具合がある場合には、鍵データの漏洩リスクが生じます。それに対し、専用保護装置であるHSMで鍵を管理している場合は、演算処理を装置内部で行ってその結果をソフトウェアに渡すことで、装置の外にCA秘密鍵が出ることはないため、安全が確保されます。では、CA秘密鍵を管理するHSMそのものに対する安全性はどのように確保されるのか、という疑問が出てくるかと思います。HSMはそのセキュアな機能により、国際的なセキュリティ認証であるFIPS 140（米国連邦政府の標準規格）やISO/IEC 15408（国際規格。もとは欧州のCommon Criteria）を通常は取得しており、その認証を有することが、装置自体の安全性の保証につながっているといえるでしょう。\n参照：https://college.globalsign.com/blog/clientcert_privatekey_230509/", "company": "GMOグローバルサイン", "service": "クライアント証明書、認証局(CA)、CA秘密鍵、ハードウェア・セキュリティ・モジュール(HSM)、FIPS 140、ISO/IEC 15408、Common Criteria", "type": "blog"}, {"question": "クライアント証明書認証の安全性が確保される、CA秘密鍵保護の重要性\nまとめ", "answer": "既にクライアント証明書を利用されている場合や、今後クライアント証明書の導入を検討する場合、日々の運用（証明書の発行・更新の操作の容易さ、ソフトウェアのアップデートやセキュリティパッチ対応など）や、発行した証明書のデバイスへの配布といった目に見えやすい点にどうしても意識が行きがちです。しかし、証明書を発行するCAの「鍵」がどのように管理されているかについても、認証利用におけるセキュリティレベルに関わる話になりますので、今一度確認してみてはいかがでしょうか？\n参照：https://college.globalsign.com/blog/clientcert_privatekey_230509/", "company": "GMOグローバルサイン", "service": "クライアント証明書, 認証局(CA), CA秘密鍵, 証明書発行, 証明書更新, セキュリティパッチ, デバイス配布", "type": "blog"}, {"question": "SSLサーバ証明書を90日から47日へ短縮する起案と、証明書自動化の役割\n有効期間47日の推進", "answer": "現在、インターネット上で公的に使用されるSSLサーバ証明書の最長有効期間は398日です。しかし、Apple社の提案では、有効期間を数年かけて徐々に短縮するロードマップが示されており、最終的に最大47日となるよう提唱されています。併せて注目すべき内容としては、この提案にはドメイン審査情報（Domain Control Validation、以下DCV）の再利用期間短縮も含まれており、最終的にはわずか10日に短縮されるといったものです。\nApple社によるとSSLサーバ証明書の有効期間を短縮することは潜在的なベストプラクティスを掘り起こした提案であり、有効期間を短くすることで見えていないセキュリティリスクの穴を大幅に狭めることができると記載されています。SSL業界がこのような厳格なライフサイクルに移行することで、利用ユーザはSSLサーバ証明書を常に注意して管理することを余儀なくされ、有効期限切れや誤発行によって引き起こされるセキュリティ事故の可能性を減らすことができます。\n参照：https://college.globalsign.com/blog/90daysTo45daysCertificateLifespansAutomation_241023/", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書, ACME, Domain Control Validation (DCV), 証明書有効期間短縮", "type": "blog"}, {"question": "SSLサーバ証明書を90日から47日へ短縮する起案と、証明書自動化の役割\nSSLサーバ証明書の管理者側の懸念", "answer": "SSLサーバ証明書の有効期間短縮は、電子証明書の自動化ツール採用の増加傾向と足並みを揃えて進行しています。電子証明書の自動化ツールは、SSLサーバ証明書だけでなく近年の電子証明書全般の有効期間短縮化に伴い、より頻繁に更新作業が必要となるため、今後の電子証明書管理には不可欠となってきます。ACME（Automated Certificate Management Environment）は、特に中小企業（SMB）にとって負担となる、頻繁な電子証明書の入れ替え作業を便利にするための重要なプロトコルです。有効期間の短縮化がもたらすセキュリティ上のメリットは明らかですが、同時に利用者側の運用上、大きな課題も生じます。SSLサーバ証明書の取得やインストール、更新などといった業務を人的に行っている利用者の割合はまだまだ多く、より一層の作業負担がかかることが想定されます。一般的には、有効期限の異なるSSLサーバ証明書を複数利用しているケースが大多数であるため、有効期限切れが発生して、サービスの中断を引き起こすリスクの増大につながる懸念もあります。\n参照：https://college.globalsign.com/blog/90daysTo45daysCertificateLifespansAutomation_241023/", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書, ACME, Automated Certificate Management Environment, 電子証明書, 有効期間短縮, DCV", "type": "blog"}, {"question": "SSLサーバ証明書を90日から47日へ短縮する起案と、証明書自動化の役割\n具体的な対策", "answer": "この懸念の対応策として、ACMEを有効に活用することでSSLサーバ証明書の発行、インストール、更新を自動化することができ、45日間という短いサイクルであっても、人手を介さずに必要な作業を実施することができます。これは、手動での業務負担や作業時間を削減するのと同時に、作業に伴う人的ミスの発生を回避することができ、中小企業にとっても非常に有益です。有効期限の短いSSLサーバ証明書へ移行することは、特に小規模事業者にとっては困難に思えるかもしれませんが、ACMEのような自動化ツールによって実現が可能となります。ACMEはセキュリティリスクを減らしつつ、手作業による管理の落とし穴を回避することができるのです。\n参照：https://college.globalsign.com/blog/90daysTo45daysCertificateLifespansAutomation_241023/", "company": "GMOグローバルサイン", "service": "ACME", "type": "blog"}, {"question": "オブジェクトストレージにおけるファイルアップロードセキュリティ - クラウド時代に\"悪意のあるデータの書き込み\"を再考する\nなぜ今、この問題を取り上げるのか？", "answer": "近年のクラウドリフト、クラウドシフトにより、クラウドを活用する場面が多くなってきていると思います。その中で、多くの場面で利用されるオブジェクトストレージにおいて、データの書き込み時に気にすべきセキュリティリスクが存在するのをご存知でしょうか？\n近年、オブジェクトストレージの不適切な利用に起因する情報漏洩が多く発生しています。そのような事例がニュース等で度々取り上げられていることから、オブジェクトストレージにおけるデータの読み取りに関するセキュリティリスクについてご存じの方は増えてきていると考えられます。しかし、書き込みに関するセキュリティリスクについては、\"改ざん\"という点では一定の理解があるものの、\"悪意のあるデータの書き込み\"に関しては、意識が低いのが現状だと考えています。本ブログでは、\"悪意のあるデータの書き込み\"、その中でもプロダクトの利用者に被害を及ぼすケースについて、理解を深めることを目的としています。\n参照：https://blog.flatt.tech/entry/object_storage", "company": "GMO Flatt Security", "service": "オブジェクトストレージ 不適切利用 情報漏洩 改ざん 悪意のあるデータ書き込み", "type": "blog"}, {"question": "オブジェクトストレージにおけるファイルアップロードセキュリティ - クラウド時代に\"悪意のあるデータの書き込み\"を再考する\n概要", "answer": "オブジェクトストレージにおけるオブジェクトのアップロード方法は複数あります。その中で、ある設定値が抜けていることが原因で、悪意のあるオブジェクトがアップロードされることを許容してしまう可能性があります。例えば、AWS SDK for JavaScript v3で、署名付き URL を発行する際に利用する@aws-sdk/s3-request-presignerのgetSignedUrlの設定ミスに関するリスクについてご存知でしょうか？\ngetSignedUrlでは、署名付き URL を生成する際に、expiresInやsignedHeadersなどのオプションを指定することができます。このオプションによって、生成される署名付き URL の有効期限や、署名に含めるヘッダーを指定することができます。このようにオプションを含めて署名付き URL を生成する際に、signedHeadersにContent-TypeやContent-Dispositionなどのヘッダーを指定しない場合、アップロードされるファイルのContent-TypeやContent-Dispositionが、PutObjectCommandの引数に指定された値と異なることを許容してしまいます。この場合、悪意のあるユーザーは、アップロード時に指定された値と異なるContent-TypeやContent-Dispositionを設定する、または異なる値になるようにヘッダーの値を改ざんすることで、オブジェクトストレージに対して悪意のあるオブジェクトをアップロードできます。このリスクの原因は、SDK及び公式ドキュメントなどで例示されている実装を参照したとしても、getSignedUrlが開発者にとって自明ではない動作をすることにあると考えています。本ブログでは、知っていそうで、知らない、オブジェクトストレージにおいてよくあるアップロード処理の実装ミスを取り上げ、そのリスクと対策について解説します。\n参照：https://blog.flatt.tech/entry/object_storage", "company": "GMO Flatt Security", "service": "getSignedUrl, @aws-sdk/s3-request-presigner, expiresIn, signedHeaders, Content-Type, Content-Disposition, PutObjectCommand", "type": "blog"}, {"question": "オブジェクトストレージにおけるファイルアップロードセキュリティ - クラウド時代に\"悪意のあるデータの書き込み\"を再考する\nオブジェクトストレージの特性への再入門", "answer": "そもそも、オブジェクトストレージにおけるオブジェクトとは何か、オブジェクトストレージの特性とは何か、改めて確認しておきましょう。オブジェクトストレージは、ファイルシステムとは異なり階層構造を持たず、保存対象のデータを単一のオブジェクトとして扱います。このオブジェクトは、オブジェクトに関するメタ情報が含まれたメタデータとデータ本体から構成されています。このメタデータには、オブジェクトの Content-Type や Content-Disposition などの情報を付与することができます。この情報は、オブジェクトの取得時にレスポンスヘッダーとして返却され、ブラウザやクライアントアプリケーションにおいて、そのオブジェクトの種類を判別するために利用されます。\n\nObject = Data(Binary) + Metadata：\n・HTTPを用いてAPI経由でデータを保存することが可能\n　- Content-Typeをはじめとした特定のメタデータをAPI経由で設定できる\n・クラウドを用いたアプリケーションにおいて、Object Storageは従来のファイル保存先であるディスクストレージに変わる形で、ファイルを保存する先としても用いられる\n\nメタデータ自体は、データのアップロードを行う際に、HTTP ヘッダーとして付与することができ、その値を含めてオブジェクトとして保存されます。\n\n設定可能なメタデータ：\n次に、オブジェクトストレージで設定可能なメタデータについて見ていきましょう。 本ブログでは S3 を例にしつつも、どのオブジェクトストレージでも共通して設定可能なメタデータについて解説します。\n・Content-Type：ファイルの MIME タイプを指定するためのメタデータ\n・Content-Disposition：ファイルのダウンロード時のファイル名を指定するためのメタデータ\n・Cache-Control：ファイルのキャッシュ制御を行うためのメタデータ\n・Storage-Class：オブジェクトの保存クラスを指定するためのメタデータ\n参照：https://blog.flatt.tech/entry/object_storage", "company": "GMO Flatt Security", "service": "Content-Type, Content-Disposition, Cache-Control, Storage-Class, S3, HTTPヘッダー, オブジェクトストレージ", "type": "blog"}, {"question": "オブジェクトストレージにおけるファイルアップロードセキュリティ - クラウド時代に\"悪意のあるデータの書き込み\"を再考する\nオブジェクトストレージにおけるファイルのアップロード方法", "answer": "オブジェクトストレージにおけるファイルのアップロード方法には、主に以下の 3 つの方法があります。\n①サーバーサイドからのアップロード\n②クライアントサイドからのアップロード(署名付き URL)\n③クライアントサイドからのアップロード(Post Policy)\n\n1. サーバーサイドからのアップロード\nサーバーサイドからのアップロードは、サーバーサイドでファイルを受け取り、オブジェクトストレージにアップロードする方法です。この方法は、サーバーサイドでファイルの内容を検証し、必要に応じて加工を行うことができるため、セキュリティリスクを抑えることができます。一方で、サーバーサイドでの処理が必要となるため、サーバーのリソースを消費することがデメリットとして挙げられます。主なデータの流れとしては、まず、クライアントからファイルをアップロードするリクエストがサーバーに送信されます。サーバーは、ファイルを受け取り、必要に応じて検証や加工を行った後、ファイルをオブジェクトストレージにアップロードします。その後、クライアントに対して、アップロードの結果を返却します。\n\n2. クライアントサイドからのアップロード(署名付き URL)\nクライアントサイドからのアップロード(署名付き URL)は、サーバーサイドのアプリケーションが署名付き URL を生成し、その URL を用いてクライアントが直接オブジェクトストレージにファイルをアップロードする方法です。この方法は、サーバーサイドでの処理が不要となるため、サーバーのリソースを消費することがないというメリットがあります。一方で、サーバーサイドからのアップロードに比べ柔軟な検証や加工が行えないことがデメリットとして挙げられます。主なデータの流れとしては、まず、サーバーサイドで署名付き URL を生成し、クライアントに返却します。クライアントは、この署名付き URL を用いて、直接オブジェクトストレージにファイルをアップロードします。その後、オブジェクトストレージからクライアントに対して、アップロードの結果が返却されます。\nクライアントサイドからのアップロード(署名付き URL)において、署名付き URL の生成には、以下のようなコードが利用され、サーバーサイドが想定したリクエストと同一のリクエストが送信されることが期待されます。もし、「署名付き URL の生成時にサーバーサイドが想定したリクエスト」と異なるリクエストがクライアント側から送信されることがあれば、オブジェクトストレージ側での署名検証時に検知することが可能です。\n署名時のアルゴリズムにはAWS4-HMAC-SHA256が用いられており、APIに送信される各要素を署名し、受信した値が想定通りに署名されたものかを検証します。\n・署名される要素 HTTP Method URI Query HeaderとHeaderの値のペア 署名に用いられるHeaderの名前 BodyなどのPayload\n\n3. クライアントサイドからのアップロード(Post Policy)\nPost Policy は、先の署名付き URL と似ているものの、アップロードされようとしているデータや付与されるメタデータがポリシーに基づいているかどうかを検証する方法です。この方法において、まず、サーバーサイドで生成されたポリシーと、関連する Fields を含むフォームをクライアントに返却します。その後、クライアントは、これらの情報を用いてフォームを生成し、オブジェクトストレージにファイルをアップロードします。この方法は、署名付き URL に比べ、Content-type の指定やファイルサイズの制限などを柔軟に設定することができるというメリットがあります。\n参照：https://blog.flatt.tech/entry/object_storage", "company": "GMO Flatt Security", "service": "署名付きURL AWS4-HMAC-SHA256 Post Policy", "type": "blog"}, {"question": "オブジェクトストレージにおけるファイルアップロードセキュリティ - クラウド時代に\"悪意のあるデータの書き込み\"を再考する\nメタデータの値を変更することで発生するリスク", "answer": "1. 観点1: Content-Type の変更による XSS\nContent-Type は、ファイルの MIME タイプを指定するためのメタデータです。この値は、ファイルの取得時にレスポンスヘッダーとして返却され、ブラウザやクライアントアプリケーションにおいて、そのファイルの種類を判別するために利用されます。すなわち、レンダリングを行う際の処理に影響を与えます。\n\n例えば、text/html という Content-Type を指定した場合、ブラウザは、そのファイルを HTML として解釈し、レンダリングを行います。\n\nこの Content-Type を任意の値に設定することが可能な場合、アップロードの際に、例えば text/html という Content-Type を指定することができます。するとブラウザに対して、そのファイルを HTML として解釈させ、XSS を引き起こすことができます。\n\n2. 観点2: Content-Disposition の変更による Reflected File Download\nContent-Disposition は、ファイルのダウンロード時のファイル名を指定するためのメタデータです。\n例えば下記のようなレスポンスが返却された場合、ブラウザはexample.txt というファイル名でダウンロードを行います。\n\nHTTP/1.1 200 OK\nContent-Disposition: attachment; filename=\"example.txt\"\nContent-Type: text/plain\nHello, World!\nExample File.\n\nこの Content-Disposition の値を任意の値に設定することが可能な場合、アップロードの際に、例えば attachment; filename=\"example.exe\" という Content-Disposition を指定することができます。するとブラウザに対して、そのファイルを example.exe としてダウンロードさせることが可能であり、Reflected File Download が引き起こされます。Reflected File Download は、信頼できるドメインから任意のコンテンツを強制的にダウンロードさせる攻撃手法です。ユーザーはダウンロード元のドメインを信用しているので、比較的高い確率でユーザーのホスト上で悪意のあるコードを実行させることができます。\n\n3. 観点3: Storage-Classを変更することによる EDoS\nStorage-Class は、オブジェクトの保存クラスを指定するためのメタデータです。例えば、STANDARD、INTELLIGENT_TIERING、ONEZONE_IA、GLACIER などの保存クラスを指定することが可能です。関連するリスクとして、一部のStorage-Classを指定することで、オブジェクトの読み取り時に高額な利用料金が発生することがあります。 例えば、GLACIER というStorage-Classを指定することで、悪意のあるユーザーは、オブジェクトストレージの利用者(サービス提供者)に対して、高額な利用料金を発生させる、すなわち Economic Denial of Sustainability (EDoS) を引き起こすことが可能となります。\n参照：https://blog.flatt.tech/entry/object_storage", "company": "GMO Flatt Security", "service": "Content-Type XSS, Content-Disposition Reflected File Download, Storage-Class EDoS, Economic Denial of Sustainability", "type": "blog"}, {"question": "オブジェクトストレージにおけるファイルアップロードセキュリティ - クラウド時代に\"悪意のあるデータの書き込み\"を再考する\nリスクの詳細と対策", "answer": "1. リスク1: AWS SDKのドキュメンテーションされていない動きに起因するリスク\nAWS などの SDK において、オブジェクトストレージにアップロードする際の署名を生成する際の前処理が異なる場合があり、これらを知らないで利用することで、利用者が先のリスクに直面する可能性があります。\n\n事例: @aws-sdk/s3-request-presignerの挙動：\nAWS SDK for JavaScript V3を利用すると、署名生成時に暗黙的に削除されるヘッダーが存在します。これはドキュメントに未記載の挙動であり、利用者がこの挙動を知らないまま利用することで、先のリスクが発生する可能性があります。具体的には、@aws-sdk/s3-request-presignerのgetSignedUrlでは、署名付き URL を生成する際に署名に含めるヘッダーを指定しない場合、content-typeヘッダーが削除されてしまいます。このような事象は、SDK における署名生成時のリクエストの処理に起因するものです。SDK の実装を見てみるとprepareRequestメソッドにおいて、unsignableHeadersとしてcontent-typeが指定されています。この場合、content-typeヘッダーは、署名生成時に削除されてしまいます。\nこの事象への対策として、getSignedUrlを用いる場合は、署名対象のヘッダーを設定するsignedHeadersにcontent-typeヘッダーを含めるべきです。getSignedUrlはsignedHeadersに明示的にヘッダーが指定された場合、unsignableHeadersに含まれるヘッダーも署名生成時に署名要素として含めてくれます。\n下記が実装例になります。\n\nconst url = await getSignedUrl(s3, command, {\n  expiresIn: 60 * 60 * 24,\n  signedHeaders: new Set(['content-type']),\n});\n\n事例: AWS SDK for Go の挙動：\nAWS SDK for Goでは、署名付きURLを生成する際に、Content-Typeを含めて署名を行うためには、Content-Lengthに1以上の値が設定されている必要があります。AWS SDK for Goで署名付きURLを生成する際は、クライアントから明示的にアップロードするファイルのSizeを指定する必要があります。\n\n2. リスク2: 署名生成時のメタデータに関連するヘッダーの未設定\n先のメタデータの値によって発生するリスクでも述べたように、特定のメタデータが設定されることで、クライアントやメタデータの値を活用するシステムで作用を起こすことがあります。このようなリスクへの対策として、アップロード時にユーザーの入力を用いてメタデータの値の設定を行わない、または、設定可能な値を制限することが有効です。例えば、署名を生成する際にsignedHeadersにおいて署名に含めるヘッダーを指定することで、ユーザーが任意のヘッダー書き換えることを禁止できます。\n\nconst url = await getSignedUrl(s3, command, {\n  expiresIn: 60 * 60 * 24,\n  signedHeaders: new Set(['content-type', 'content-length', 'content-disposition', 'x-amz-storage-class']),\n});\n\nこれらのヘッダーがsignedHeadersに指定されなかった場合、先のクライアントサイドからのアップロード(署名付き URL)で述べたように、ユーザーが任意の値を設定可能になり、先に述べたXSSなどのリスクが発生します。\n\n3. リスク3: 署名時やポリシー生成時に用いる値の検証不備\n署名付きURLやPOST Policyの生成時に、ユーザーの入力をそのまま利用するとリスクが生じます。例えば、署名付き URL の生成について下記のコードのような実装をしていたとします。一見、signedHeadersにcontent-typeを含めることで、content-typeヘッダーの改ざんなどを防ぐことができているように見えます。しかし実際にはcontent-typeヘッダーの値をユーザーの入力から取得しているため、ユーザーが任意の値を設定でき、XSS のリスクが生じます。\n例示したコードでは、request.body.contentTypeのようなユーザーから入力された値を検証せずにそのまま利用してます。そのため、ユーザーが任意の値を設定することが可能です。このような例以外にも、不完全な検証(e.g. 正規表現やstartsWith、endsWithなどの検証)が原因で、悪意のあるユーザーが任意の値を設定でき、Content-Typeに起因するXSSなどが引き起こされます。\n\n4. リスク4: 不完全なポリシーの設定\nオブジェクトストレージに Post Policy を利用してアップロードする際に、ポリシーの設定が不完全である場合、悪意のあるユーザーが任意のメタデータの値を設定でき、Content-Typeに起因するXSSなどのリスクが発生します。Post Policy にはstarts-withと呼ばれる属性があります。この属性を用いることで、特定の文字列で始まる値だけを設定できるようになり、アップロードの制約を柔軟にかけることができます。一方で、設定する制約が不完全だとリスクに繋がります。例えば、Content-Typeの検証を行う際に、先頭の文字列がimageで設定されているとします。そのような設定では、悪意のあるユーザーは、imageで始まる任意の値を設定するなどの方法で、ブラウザにMimeTypeを誤って解釈させることができます。結果、レスポンスの内容がHTMLとして解釈された場合、XSSのリスクが発生します。\n対策として、Content-Typeをstarts-withで設定する際に、MimeType における type のみを指定するのではなく、区切り文字である/を含めることが有効です。\n\nconst { url, fields } = await createPresignedPost(s3, {\n  Bucket: process.env.BUCKET_NAME!,\n  Key: `upload/${filename}`,\n  Conditions: [\n    ['content-length-range', 0, 1024 * 1024 * 100],\n    ['starts-with', '$Content-Type', 'image/'],\n  ],\n  Fields: {\n    'Content-Type': request.body.contentType,\n  },\n  Expires: 600,\n});\n参照：https://blog.flatt.tech/entry/object_storage", "company": "GMO Flatt Security", "service": "<PERSON>WS SDK, @aws-sdk/s3-request-presigner, getSignedUrl, signedHeaders, unsignableHeaders, Content-Type, XSS, POST Policy, starts-with, createPresignedPost", "type": "blog"}, {"question": "オブジェクトストレージにおけるファイルアップロードセキュリティ - クラウド時代に\"悪意のあるデータの書き込み\"を再考する\nまとめ", "answer": "本ブログでははじめに、オブジェクトストレージにおけるファイルのアップロード方法およびメタデータについて解説し、次にメタデータにユーザー入力が発生した際のリスクについて解説しました。\n\nオブジェクトストレージにおけるファイルのアップロード方法には、サーバーサイドからのアップロード、クライアントサイドからのアップロード(署名付き URL)、クライアントサイドからのアップロード(Post Policy)の 3 つの方法があり、それぞれの特性によって、利用されるシーンが異なります。\n\nまた、メタデータについては、Content-Type、Content-Disposition、Storage-Classなどの値を変更することで、XSS、Reflected File Download、EDoS などのリスクが発生する可能性があります。これらのリスクを軽減するためには、ユーザーの入力を用いた値の設定を行わない、または、設定可能な値を制限することが重要です。\n\n最後に、署名生成時の SDK の挙動に起因するリスクについて解説を行いました。特にリスク1では、JavaScript V3 SDK やAWS SDK for Goでの実装を例にSDKの実装に起因する署名生成の差異について解説しました。このSDKにおける署名生成の特性をよく確認しないまま開発者が利用するとサービス利用者にリスクが生じる可能性があります。\n\n結論として、オブジェクトストレージにおけるファイルのアップロード方法とメタデータについて理解を深め、メタデータの値に起因して発生するリスクを把握し、それらのリスクを軽減するための対策を行うことが重要であると考えます。\n参照：https://blog.flatt.tech/entry/object_storage", "company": "GMO Flatt Security", "service": "署名付きURL, Post Policy, XSS, Reflected File Download, EDoS, JavaScript V3 SDK, AWS SDK for Go", "type": "blog"}, {"question": "重複したIAM、拒否と許可どっちが優先？アクセス制御の特性をAWS・Google Cloud・Azure・Firebaseそれぞれについて理解する\n調査対象のクラウドサービス", "answer": "この記事では、以下のクラウドサービス上のプロダクトにおけるアクセス制御の検証を実施し、ポリシーの優先順位の調査を行いました。\n・AWS: S3\n・Google Cloud: Google Cloud Storage\n・Firebase: Firestore\n・Azure: Blob Storage\nそれぞれのクラウドサービス上で、最低限「許可する対象および条件を記述する」形でのアクセス制御が可能なプロダクトを選択しました。\n参照：https://blog.flatt.tech/entry/cloud_access_control", "company": "GMO Flatt Security", "service": "AWS S3, Google Cloud Storage, Firebase Firestore, Azure Blob Storage", "type": "blog"}, {"question": "重複したIAM、拒否と許可どっちが優先？アクセス制御の特性をAWS・Google Cloud・Azure・Firebaseそれぞれについて理解する\nまとめ", "answer": "サービス：優先されるルール（許可 or 拒否）\nAWS：拒否\nGoogle Cloud：拒否 ※細かいリソースレベルでの拒否設定はできない\nAzure：拒否 ※デプロイスタックで定義したリソースに対する限定的な拒否設定のみ存在する\nFirebase：許可\n\n今回、対象としたサービスだと操作対象の指定方法や条件の設定などはAWSが一番細かい粒度で許可と拒否それぞれが設定可能でした。そのため柔軟さという意味ではAWSが一番高いと言えます。一方でFirebase(Firestore)のセキュリティルールでは複数のルールのうち、一個でも該当する許可ルールが存在すれば許可と判定されるため、慎重な扱いが必要です。これについては私見ですが、Firestoreはシンプルなデータ構造の管理に向いていると考えれば、セキュリティルールも単純な評価ロジックであることは一定の理が見いだせます。また、Google CloudとAzureは拒否の設定はそれぞれ限定的な形で提供されており、これらのサービスも、機能の不足ではなく、あえて柔軟な拒否設定をできないようにすることで複雑なポリシーを作りにくいような設計にしたのではないかと考えています。\n参照：https://blog.flatt.tech/entry/cloud_access_control", "company": "GMO Flatt Security", "service": "AWS IAM、Google Cloud IAM、Azure IAM、Firebase Security Rules", "type": "blog"}, {"question": "重複したIAM、拒否と許可どっちが優先？アクセス制御の特性をAWS・Google Cloud・Azure・Firebaseそれぞれについて理解する\n検証シナリオ", "answer": "以下のシナリオで検証を進めました。\n・原則としてすべてのオブジェクトにはアクセスできる(全体許可)。\n・ただし、pathA/ 以下の(または pathA/ からオブジェクト名が始まる)オブジェクトにはアクセスできない(一部禁止) 。\nこの状態で、全体に対するアクセス許可ルールが優先されるのか、あるいは一部のオブジェクトに対するアクセス禁止ルールが優先されるのか、という点を以下のように確認していきます。\n・pathA/foo.txt にアクセスした場合、拒否されるか。\n・pathB/bar.txt にアクセスした場合、許可されるか。\n以降では、個々のサービスについての検証内容とその結果について書いていきます。 (アカウントIDなど、一部の情報を **** などの文字列に置き換えています。)\n参照：https://blog.flatt.tech/entry/cloud_access_control", "company": "GMO Flatt Security", "service": "アクセス制御, IAM, AWS, Google Cloud, Azure, Firebase, パスベース制御, オブジェクトレベルアクセス", "type": "blog"}, {"question": "重複したIAM、拒否と許可どっちが優先？アクセス制御の特性をAWS・Google Cloud・Azure・Firebaseそれぞれについて理解する\nAWS: Amazon S3", "answer": "Amazon S3はAWS上で提供される、オブジェクトストレージサービスです。バケット上のオブジェクトに対するアクセス制御ルールを定義するための方法を以下に示します。(以下では省略していますが、S3 Access Pointsという機能も存在します)\n\nIAMポリシー：\n・IAMポリシーでアクセス制御を行う方法です。本記事はIAMポリシーの評価ロジックの比較を目的としているため、こちらの方法について確認します。\n\nACL(アクセスコントロールリスト)：\n・オブジェクトごとにアクセス許可の設定が可能な仕組みです。以下の公式ドキュメントにあるように、現在は多くのユースケースでは選択されないものとなっています。\n・https://docs.aws.amazon.com/ja_jp/AmazonS3/latest/userguide/acls.html\n\nS3 Access Grants：\n・2023年に発表された、大規模なアクセス管理を行いたい場合の選択肢です。\n・IAM Identity Centerと連動したアクセス制御が可能になります\n\nまた、IAMポリシーベースでのアクセス制御には、大きく分けて以下の二通りの仕組みがあります。\n・ロールやユーザ、グループ等に対してポリシーを付与する方法\n・S3バケットに対して直接許可のルールを記述する方法 (バケットポリシー)\nこれらのうち、今回は前者の方法を用いて、IAMユーザに対して直接ポリシーを付与します。\n\n# 設定\nまず、AWSのポリシーについての簡単な解説をします。\n・IAMポリシーは複数の許可/拒否の設定文(Statement)から構成される。\n・Statementには「Effect(Allow/Denyのどちらか)」と「どのリソースに対して効果を発揮するか」、また「どの操作(Action)を対象とするか」を記述する。\n・更に、任意でStatementが有効になる条件を設定可能(タグや現在時刻など様々な条件が存在する)。\n\nこれらを踏まえた上で、以下のようなIAMポリシーを作成します。\n{\n    \"Version\": \"2012-10-17\",\n    \"Statement\": [\n        {\n            \"Sid\": \"GrantAllAccess\",\n            \"Effect\": \"Allow\",\n            \"Action\": [\n                \"s3:*\"\n            ],\n            \"Resource\": [\n                \"arn:aws:s3:::okazu-bucket-policy-test-20241005\",\n                \"arn:aws:s3:::okazu-bucket-policy-test-20241005/*\"\n            ]\n        },\n        {\n            \"Sid\": \"DenyPathAAccess\",\n            \"Effect\": \"Deny\",\n            \"Action\": [\n                \"s3:*\"\n            ],\n            \"Resource\": [\n                \"arn:aws:s3:::okazu-bucket-policy-test-20241005/pathA/*\"\n            ]\n        }\n    ]\n}\nこのIAMポリシーについて解説します。\n・このポリシーには2つのステートメントがある。\n・1つ目のステートメントでは okazu-bucket-policy-test-20241005 バケットに対するすべてのアクセスを許可する。\n・2つ目のステートメントでは ​​okazu-bucket-policy-test-20241005 バケット内の、 pathA/ から始まる名前のオブジェクトに対するすべてのアクセスを拒否する。\nAWSのIAMポリシーに関しては、以下のドキュメントにあるように、明示的な拒否のルールが最も優先されます。\nhttps://docs.aws.amazon.com/ja_jp/IAM/latest/UserGuide/reference_policies_evaluation-logic.html\n\nそのため、上記のポリシーでは、pathA/ 以下のオブジェクトに対するアクセス拒否のルールが、バケット全体に対するアクセス許可のルールよりも優先されることが期待されます。\n次に、userAというIAMユーザを作成し、このポリシーを付与します。これにより、userAは当該バケットへの全面的なアクセスが許可されるが、pathA/ 以下へのアクセスのみ禁止される、という状態を実現しました。 また、S3には以下のような準備をしておきました。\nokazu-bucket-policy-test-20241005 というバケットを作成。\n(当初はバケットポリシーの挙動の確認のためのバケットでしたが、そのまま今回の検証にも流用したため、実態と乖離した名前になっております)\npathA/foo.txt, pathB/bar.txt をバケット以下に作成。\nまた、AWS CLIの準備として、userAのアクセスキーを発行してCLIで使うように設定しました。\n\n# 補足: 特定pathのみオブジェクト一覧取得を禁止することは可能か？\n今回はあくまで pathA/ 以下のオブジェクトをアクセスできないようにする、という目的のため簡単に「pathA/foo.txt が取得できるか」だけを確認しました。その結果、期待通りpathA/ 以下のすべてのオブジェクトに対する操作を禁止できていることが分かりました。しかし、直感に反して「pathA/ 以下のオブジェクト一覧」は取得可能です。\nこれは、以下のような事情から発生しています。\n・AWSでオブジェクト一覧を取得する際には、s3:ListBucket の許可が必要\n・s3:ListBucket はバケットを対象に取る\n・今回はバケットそのものに対する操作は全面的に許可している\n以上より、オブジェクト一覧の取得は許可されていることがわかります。\n参照：https://blog.flatt.tech/entry/cloud_access_control", "company": "GMO Flatt Security", "service": "IAMポリシー、バケットポリシー、アクセスコントロールリスト（ACL）、S3 Access Points、S3 Access Grants、s3:ListBucket", "type": "blog"}, {"question": "重複したIAM、拒否と許可どっちが優先？アクセス制御の特性をAWS・Google Cloud・Azure・Firebaseそれぞれについて理解する\nGoogle Cloud: Google Cloud Storage", "answer": "Google Cloud Storage(GCS)は、Google Cloud において提供されているオブジェクトストレージサービスです。GCSのアクセス制御には、以下のような方法があります。\n・IAMベースでのアクセス制御\n　- 大まかにはAWSと同じく、Google Cloud上のプリンシパル(Googleアカウントやサービスアカウント、Googleグループなど)に許可設定を付与する仕組みです。\n・ACL\n　- こちらもS3のACLと似ており、オブジェクトレベルのアクセス制御を実現したい際など特殊なユースケースにおいて必要とされます。\n　- 公式ドキュメントにおいても、基本的にはACLよりもIAMによる制御を推奨しています。\n・マネージドフォルダ\n　- こちらも厳密にはIAMによる制御の一種ですが、フォルダレベルでのアクセス制御を実現するための機構です。\n\n# 設定\nGoogle CloudにおけるIAMの権限設定は許可と拒否が完全に分離されており、条件設定の粒度や設定内容も異なるためやや複雑です。\n・基本は許可のみ\n　- 許可する権限のセットをロールという形でまとめている。\n　- 条件はプリンシパルにロールを付与する際に設定できる。\n・通常の権限許可設定とは別に、拒否ポリシー という仕組みが存在する。\n　- 許可と比較すると粒度は粗い。\n　　- 具体的には、許可の場合は操作対象のリソース名を対象に取る条件を設定できるが、拒否の場合はタグしか対象に取れない。\n　- 拒否ポリシーはルールごとに、「拒否するプリンシパル」「拒否する権限」「拒否する条件」をセットとして管理する(ロールを作って付与、という許可の方式とは違い直接拒否のルールを付与する)\n　- 試す場合には組織レベルで”拒否管理者”のロールが必要な点に注意。\nIAMに関する基本的な概念は以下の公式ドキュメントにもまとまっているため、ぜひご一読ください。\nhttps://cloud.google.com/iam/docs/overview?hl=ja\n\n以上を踏まえて、今回はまずサービスアカウント(Googleアカウントに紐づかないAPIを外部から利用する用途のためなどのアカウント)に対して、storage.objects.get の権限のみを(カスタムロールを作成して)付与しました。 また、カスタムロールの付与の際には、以下のような条件でpathA以下のオブジェクト以外のみアクセスできるようにしています。\n\n!resource.name.startsWith(\"projects/_/buckets/okazu-blog-bucket-202410/objects/pathA/\")\n\n# 補足: 許可を追加で割り当ててしまう落とし穴に注意\n以上のやり方は、あくまで「pathA/ 以下のオブジェクトに対して許可を出していない」だけにすぎません。 これは「pathA/ 以下のオブジェクトに対する許可」を改めて付与した場合、それによって、pathA/ 以下にアクセス可能になるということを意味します。\n例えば、条件を指定せずに別で storage.objects.get を含む権限を許可してしまうと、意図せずして pathA/ 以下にアクセス可能になってしまう事故が考えられるため、注意が必要です。\n\n# 追加の確認: 拒否ポリシーは許可に優先されるか\n明示的に拒否ポリシーで拒否されたエラーメッセージが出るため、許可よりも拒否が強いことがわかります。\n\n# 補足: 拒否ポリシーのユースケースについて\n前述したように拒否ポリシーの条件設定は、タグをベースとしたもののみで、リソース名などのレベルでは設定ができません。 この点から、今回実現しようとしていた「GCSバケット内の特定のpath以下へのアクセスを禁止」という用途には使えないことがわかります。\n参照：https://blog.flatt.tech/entry/cloud_access_control", "company": "GMO Flatt Security", "service": "Google Cloud Storage, IAM, ACL, サービスアカウント, カスタムロール, storage.objects.get, 拒否ポリシー, プリンシパル, リソースタグ", "type": "blog"}, {"question": "重複したIAM、拒否と許可どっちが優先？アクセス制御の特性をAWS・Google Cloud・Azure・Firebaseそれぞれについて理解する\nFirebase: Firestore", "answer": "Cloud Firestore(文中では単にFirestoreとします)はmBaaSプラットフォームであるFirebase上で提供されるNoSQLデータベースです。Firebaseが2014年にGoogleに買収されたため、現在はGoogle Cloudと連携する形で提供されています。ちなみに、FirebaseにはオブジェクトストレージであるCloud Storage for Firebaseがありますが、プロダクトとしてはFirestoreの方が有名であると考え、Firestoreを採用しました。 Firestore上のデータに対するアクセス制御には、FirestoreとCloud Storage for Firebaseの中で使えるアクセス制御機構である、セキュリティルールを利用します。\nこれはCELで記述するアクセス制御のルールで、Firebaseが提供するIDaaSであるFirebase Authenticationと連携したルールも実現可能です。\n\n# 設定\n実際の設定内容に触れる前に、まずはFirebaseのセキュリティルールについて解説します。 以下は、セキュリティルールに関する公式ドキュメントです。\nhttps://firebase.google.com/docs/rules/rules-language?hl=ja#rule_constructs\n\nFirebaseのセキュリティルールの例を以下に示します。\n   // rule1\n   match /{document=**} {\n        allow read, write  \n    }\n\n    // rule2\n    match /pathA/{document=**} {\n        allow read, write: if false;\n    }\n\nこれは、セキュリティルールの一部の例で、この部分に含まれる2️つのルールはそれぞれ以下のような意味になります。\n1. データベース以下のすべてのデータに対する読み書きを許可する\n2. /pathA 以下のすべてのデータに対する読み書きを許可しない(「禁止」ではない点に注意)\npathA/ 以下にアクセスしようとした際、1番目は許可する、2番目は許可しない、となっておりAWSやGoogle Cloudのことを思い返すと2番目の方が優先されて禁止されるようにも思えますが、実際に /pathA/ 以下にアクセスすると以下のように許可されてしまいます。\nこれは、前述したドキュメントの中にもあるようにFirestoreのセキュリティルールではアクセス権を付与するルールが1つでも一致すれば、リクエストは許可されるという評価ロジックになっているためです。 そのため、許可するルールと許可しないルールが存在した場合、許可が優先されます(明示的な禁止が存在しない)。\nこのことから、データベースの規模が大きくなり、ルールが複雑化すればするほど思わぬアクセスを許可してしまう事故のリスクは(他のサービスに比べて)高くなると言えます。\nさて、ここまでに確認した許可優先の原則を踏まえて、今回の意図に沿うように書き直したセキュリティルールが以下です。\nrules_version = '2';\nservice cloud.firestore {\n  match /databases/{database}/documents {\n\n   match /{col}/{document=**} {\n      allow read, write: if col != \"pathA\";  \n    }\n\n  }\n}\n修正後のルールでは、pathA/ 以下以外の全てに対してのアクセスを許可する、というルールになっています。(ちなみに前述した、GCSの検証の際も同じ趣旨の条件設定をしています)\n\nFirestoreでは明示的な拒否はできないため複雑な権限設計の難易度は高いものの、パスのマッチングと文字列比較を用いてパスベースでのアクセス制御は可能であることを示しました。\n参照：https://blog.flatt.tech/entry/cloud_access_control", "company": "GMO Flatt Security", "service": "Cloud Firestore, Firebase Authentication, セキュリティルール, CEL, パスベースアクセス制御", "type": "blog"}, {"question": "重複したIAM、拒否と許可どっちが優先？アクセス制御の特性をAWS・Google Cloud・Azure・Firebaseそれぞれについて理解する\nAzure: Blob Storage", "answer": "Microoft AzureのオブジェクトストレージであるBlob Storageについて検証します。 Blob Storageのサービスは以下のようなモデルとなっています(関係ある部分だけを抜粋)。\n・ストレージアカウント: サービスを管理する1単位の概念で、想定されるワークロードなどによって異なる種類のアカウントが作成可能。\n・コンテナ: オブジェクトを配置する空間。ストレージアカウント内に複数作成可能。\n・BLOBオブジェクト: 実際に読み書きするファイル。\n(公式ドキュメントより転載)\n\nBlob Storageに対するアクセス許可の最小単位はコンテナです。\n\n# 設定\nAzureもGoogle Cloudと同じく、IAMの設定は許可のみで、ロールを割り当てる際に条件を設定できる点も同じです。また、拒否割り当てという機能で限定的に拒否設定を実現できます。\nまず、以下の準備をします。\n・”path-a”, “path-b” という2つのコンテナを用意\n・”foo.txt”, “bar.txt”というオブジェクトをそれぞれのコンテナに配置\nまた、Azure CLIからアクセスする際に使用するユーザを作成し、そのユーザでログインしておきます。\nAzureでは、様々な単位(サブスクリプション、リソースグループ、ストレージアカウント、コンテナなど)でIAMの設定を作成することができますが、今回はストレージアカウント内で以下のような条件で「ストレージ BLOB データ閲覧者」のロールを検証用のユーザに付与します。\n(\n (\n  !(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/read'})\n )\n OR \n (\n  @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:name] StringNotEquals 'path-a'\n )\n)\n\n# 補足: 拒否割り当てについて\n前述した通り、Azureには拒否割り当てという仕組みがあり、これを使うことで限定的な範囲で特定の操作の拒否を実現できます。ただし、「削除の拒否」「書き込みと削除の拒否」のみが実現可能となっており、今回実現したかった「読み取りを含めた完全なアクセスの拒否」は実現できません。 \n\n「独自の拒否割り当てを直接作成することはできません。 ただし、デプロイ スタックの作成中に拒否設定を指定できます。これにより、そのデプロイ スタックのリソースが所有する拒否の割り当てが作成されます。」\n\nつまり、前提としてデプロイスタックという仕組みの管理下にリソースを置かないと拒否割り当ては使えないということです。デプロイスタックは後述しますが、Azureのリソースを記述するための独自のテンプレート言語を用いたIaCに近い仕組みです。\n\nAzureは基本的には許可しかないため、IAMの条件で操作対象外にしたいリソースを含まないような記述が必要であり、Google Cloudと同じく誤って別のロールを付与する際に、許可範囲を広げないように注意する必要があります。\n参照：https://blog.flatt.tech/entry/cloud_access_control", "company": "GMO Flatt Security", "service": "Blob Storage、ストレージアカウント、コンテナ、BLOBオブジェクト、拒否割り当て、Microsoft.Storage/storageAccounts/blobServices/containers/blobs/read、デプロイスタック、ストレージ BLOB データ閲覧者", "type": "blog"}, {"question": "重複したIAM、拒否と許可どっちが優先？アクセス制御の特性をAWS・Google Cloud・Azure・Firebaseそれぞれについて理解する\nおわりに", "answer": "今回の記事では、各種クラウドサービスのアクセス制御について、その設定や「それぞれのアクセス制御は拒否優先なのかどうか」を調査しました。 IAMベースでのアクセス制御という点ではFirestore以外のサービスは共通していますが、それぞれ制御の粒度やアクセス拒否の実現レベルに大きな差がある点は特徴的でした。\n今回はIAMサービスについてのみ注目しましたが、実際にクラウドを活用したシステムやアプリケーションをセキュアに運用するためには多層防御的な緩和策や攻撃を検知する仕組みなどが必要です。また、それぞれのクラウドサービスごとに対応したセキュリティ関連のサービスやベストプラクティスがあるため、IAMを完璧にしたから安心、とはならない点にご注意ください。\n参照：https://blog.flatt.tech/entry/cloud_access_control", "company": "GMO Flatt Security", "service": "IAM、アクセス制御、Firestore、AWS、Google Cloud、Azure、Firebase", "type": "blog"}]