[{"question": "GMOセキュリティ24について", "answer": "GMOインターネットグループは、2025年2月6日より、24時間無料で利用可能な総合ネットセキュリティサービス「GMOセキュリティ24」を開始しました。 \nGMO.JP\n\nこのサービスは以下の2つの機能を提供しています：\n\nパスワード漏洩・Webサイトリスク診断：メールアドレスやWebサイトのURLを入力するだけで、パスワードの漏洩状況やWebサイトの脆弱性、なりすまし、盗聴リスクなどを簡単にチェックできます。\n\nセキュリティ相談AIチャットボット：GMOインターネットグループのAIエンジニアが開発したAIチャットボットが、24時間365日、セキュリティに関する不安や疑問に回答します。\n\nこれらの機能により、誰でも手軽にセキュリティ対策を講じられる環境を提供し、より安心なインターネット社会の実現を目指しています。", "company": "GMOインターネットグループ", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "パスワード漏洩診断について", "answer": "GMOインターネットグループが提供する「GMOセキュリティ24」のパスワード漏洩診断は、メールアドレスを入力するだけで、そのパスワードが過去に漏洩していないかを無料で確認できるサービスです。\n\n主な特徴\n無料で利用可能：誰でも24時間365日、簡単にチェック可能。\n即時診断：メールアドレスを入力するだけで、過去の漏洩履歴を照合し、結果を即座に確認できる。\nセキュリティ向上のサポート：漏洩が確認された場合、パスワードの変更やセキュリティ対策のアドバイスを提供。\nこの機能により、ユーザーは自身のアカウントの安全性を簡単にチェックし、サイバーリスクを軽減することができます。\n", "company": "GMOインターネットグループ", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "Webサイトリスク診断について", "answer": "「GMOセキュリティ24」のWebサイトリスク診断は、サイトのURLを入力するだけで、セキュリティ上のリスクを簡単にチェックできる無料サービスです。\n\n主な特徴\nサイトの安全性を即時診断\n・URLを入力するだけで、そのサイトの脆弱性やリスクを判定。\nチェック対象のリスク\n・なりすまし診断（フィッシングサイトなど）\n・実在証明・盗聴防止（SSL）診断（SSL/TLSの適用状況）\n・Webサイト脆弱性診断（セキュリティの欠陥がないか）\n・クラウド利用・リスク診断\n\n誰でも簡単に利用可能\n・IT専門知識がなくても、簡単に自分のサイトや気になるサイトのリスクを確認できる。\n\nこの診断を活用することで、ユーザーは自サイトのセキュリティ対策を強化し、より安全なインターネット環境を確保することができます。", "company": "GMOインターネットグループ", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "AIチャットボットについて", "answer": "「GMOセキュリティ24」のAIチャットボットは、セキュリティに関する疑問や不安に対して24時間365日対応する無料のサポート機能です。ユーザーは、セキュリティ対策やリスクについて気軽に相談できます。\n主な特徴\n24時間365日対応\n・いつでもリアルタイムでセキュリティに関する質問が可能。\n専門的なセキュリティアドバイスを提供\n・サイバー攻撃対策、パスワード管理、フィッシング詐欺対策などの知識を提供。\n・パスワード漏洩やWebサイトのセキュリティリスクに関する診断結果の解説も可能。\n初心者から上級者まで対応\n・基本的なセキュリティ対策から、専門的なサイバーセキュリティの相談まで幅広く対応。\nプライバシーと安全性を確保\n・会話データの保護に配慮し、個人情報の取り扱いにも対応。", "company": "GMOインターネットグループ", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "サービスの利用にあたり、ユーザー登録やログインは必要ですか？", "answer": "必要ありません。\n専用サイト（https://www.gmo.jp/security/）にアクセスし、ご自身のメールアドレスや、診断対象のWebサイトのURLを入力するだけで診断が開始されます。診断結果は入力したメールアドレス宛に送られたURLをクリックすると、わかりやすい形式で確認できます。", "company": "GMOインターネットグループ", "service": "一般仕様に関する質問, GMOセキュリティ24", "type": "FAQ"}, {"question": "このサービスの対象ユーザー（個人/企業）と想定するユースケースは？", "answer": "個人や企業を対応しています", "company": "GMOインターネットグループ", "service": "一般仕様に関する質問, GMOセキュリティ24", "type": "FAQ"}, {"question": "GMOセキュリティ24のメールアドレス診断に入力しても結果が帰ってこない", "answer": "パスワード漏洩診断は1分程度で結果をメールで送信します。来なかったら障害。\nサイトリスク診断は30分程度で結果をメールで送信します。来なかったら障害。\n診断したいサイトのドメインとメールのドメインが同じ場合、メールアドレスの確認をもって、診断をすることができます。\nサイトのドメインの所有確認のために、TXTレコードに指定した文字列を登録する必要があります。", "company": "GMOインターネットグループ", "service": "一般仕様に関する質問, GMOセキュリティ24", "type": "FAQ"}, {"question": "この診断結果は何分後ぐらいに送られてきますか？", "answer": "パスワード漏洩診断は1分程度で結果をメールで送信します。\nWebサイトリスク診断は30分程度で結果をメールで送信します。", "company": "GMOインターネットグループ", "service": "一般仕様に関する質問, GMOセキュリティ24", "type": "FAQ"}, {"question": "メール（診断結果）が届かない", "answer": "パスワード漏洩診断は1分程度で結果をメールで送信します。\nWebサイトリスク診断は数分～30分程度で結果をメールで送信します。\n暫くお待ちいただくか、迷惑メールとして振り分けられていないかご確認ください。", "company": "GMOインターネットグループ", "service": "一般仕様に関する質問, GMOセキュリティ24", "type": "FAQ"}, {"question": "定期診断あるいはメルマガをキャンセル（停止）したい", "answer": "件名に、【GMOセキュリティ24】、サイトリスク診断またはパスワード漏洩診断、結果報告という単語が入っているメールを探していただき、メール文中にある診断結果のURLから設定を変更していただけます。", "company": "GMOインターネットグループ", "service": "一般仕様に関する質問, GMOセキュリティ24", "type": "FAQ"}, {"question": "定期診断の期間を変更したい", "answer": "件名に、【GMOセキュリティ24】、サイトリスク診断またはパスワード漏洩診断、結果報告という単語が入っているメールを探していただき、メール文中にある診断結果のURLから設定を変更していただけます。\n1ヶ月、3ヶ月、6ヶ月毎がお選びいただけます。", "company": "GMOインターネットグループ", "service": "一般仕様に関する質問, GMOセキュリティ24", "type": "FAQ"}, {"question": "定期診断とは何ですか？", "answer": "パスワード漏洩診断またはWebサイトリスク診断を一度無料で診断した後、設定した期間ごとに自動で診断するものです。\n定期診断の間隔は1ヶ月、3ヶ月、6ヶ月毎がお選びいただけます。\nWebサイトを常に安全に保つためには月1回の診断を推奨しています。", "company": "GMOインターネットグループ", "service": "一般仕様に関する質問, GMOセキュリティ24", "type": "FAQ"}, {"question": "定期診断あるいはメルマガのメールアドレスを変更したい", "answer": "後からメールアドレスを変更することができません。大変お手数ですが、新しいメールアドレスで新たに無料診断を行っていただけますでしょうか。 (URL: https://www.gmo.jp/security/ )", "company": "GMOインターネットグループ", "service": "一般仕様に関する質問, GMOセキュリティ24", "type": "FAQ"}, {"question": "チャットボットの対応範囲はどこまでカバーしていますか？", "answer": "下記の関連ネットセキュリティ情報はカバーしています\n・ネットセキュリティ全般の専門知識\n・GMOのネットセキュリティサービスの内容と特徴", "company": "GMOインターネットグループ", "service": "AIチャットボット, GMOセキュリティ24", "type": "FAQ"}, {"question": "どのレベルのセキュリティ相談が可能ですか？", "answer": "簡単なセキュリティ用語から、深い専門知識まで提供しております", "company": "GMOインターネットグループ", "service": "AIチャットボット, GMOセキュリティ24", "type": "FAQ"}, {"question": "AIが対応できない場合、有人サポートに切り替えることはできますか？", "answer": "GMOのネットセキュリティサービスの各社のURLを用意しておりますので、内容によって有人サポートに辿り着くことが可能です。", "company": "GMOインターネットグループ", "service": "AIチャットボット, GMOセキュリティ24", "type": "FAQ"}, {"question": "サポートの対応時間は？", "answer": "「パスワード漏洩・Webサイトリスク診断」も「セキュリティ相談AIチャットボット」も24時間365日提供しております。", "company": "GMOインターネットグループ", "service": "サポート, GMOセキュリティ24", "type": "FAQ"}, {"question": "パスワードマネージャーとは", "answer": "パスワードマネージャーは、パスワードを生成、管理、保存するツールです。これを利用することで、ユーザーはさまざまなアカウント用の多くのパスワードを覚える必要がなくなります。パスワードマネージャーは、利便性を向上させるだけでなく、オンラインセキュリティを強化する役割も果たします。\n\n具体的には、パスワードマネージャーは以下のような機能を提供します\n\nパスワードの自動生成: 強力で複雑なパスワードを自動的に生成します。\nパスワードの保存: 生成したパスワードを安全に保存し、必要なときに取り出せるようにします。\nログイン情報の自動入力: 保存されたログイン情報を自動的に入力することで、ユーザーが手動で入力する手間を省きます。\n多要素認証: マスターパスワードに加えて、他の認証方法（例: モバイル認証アプリ、指紋認証、顔認証）を追加することで、セキュリティを強化します。\nパスワードマネージャーを使用することで、各アカウントに対して独自の強力なパスワードを設定し、それを記憶しておく必要がなくなります。また、パスワードリユース（同一のパスワードを複数のアカウントで使用すること）によるアカウント乗っ取りのリスクを大幅に軽減できます。\n\nさらに、パスワードマネージャーは一部のサイトやアプリで自動的にログイン情報を入力する機能を持っており、ユーザーが新たにアカウントを作成する際に強力なパスワードを生成することも可能です。", "company": "GMOグローバルサイン", "service": "TrustLogin, GMOセキュリティ24", "type": "FAQ"}, {"question": "二要素認証（2FA）とは", "answer": "ユーザーがアカウントにアクセスする際に、2つの異なる要素を使用して本人確認を行うセキュリティ手法です。これにより、パスワードだけでは不十分な場合でも、追加の認証要素を用いることでセキュリティを強化します。\n\n二要素認証には主に以下の3つの要素が使用されます:\n\n知識要素: ユーザーが知っている情報（例: パスワード、暗証番号）。\n所有要素: ユーザーが持っているもの（例: スマートフォン、セキュリティトークン）。\n生体要素: ユーザーの身体的特徴（例: 指紋、顔認証、虹彩認証）。\n例えば、オンラインバンキングでは、IDとパスワードに加えて、金融機関から送られてくるワンタイムパスワード（OTP）を入力することで二要素認証が行われます。\n\n二要素認証のメリットには、アカウントの不正アクセスを防ぐことができる点が挙げられます。たとえパスワードが漏洩しても、追加の認証要素が必要なため、第三者がアカウントにアクセスするのは難しくなります。", "company": "GMOグローバルサイン", "service": "TrustLogin, GMOセキュリティ24", "type": "FAQ"}, {"question": "多要素認証（MFA）とは", "answer": "ユーザーが自分の身元を証明するために、複数の異なる要素を使用する認証方法です。これにより、セキュリティが強化され、パスワードだけでは不十分な場合でも、追加の認証要素を用いることで不正アクセスを防ぐことができます。\n\n多要素認証には主に以下の3つの要素が使用されます:\n\n知識要素: ユーザーが知っている情報（例: パスワード、暗証番号）。\n所有要素: ユーザーが持っているもの（例: スマートフォン、セキュリティトークン）。\n生体要素: ユーザーの身体的特徴（例: 指紋、顔認証、虹彩認証）。\n例えば、オンラインバンキングでは、IDとパスワードに加えて、金融機関から送られてくるワンタイムパスワード（OTP）を入力することで多要素認証が行われます。\n\n多要素認証のメリットには、アカウントの不正アクセスを防ぐことができる点が挙げられます。たとえパスワードが漏洩しても、追加の認証要素が必要なため、第三者がアカウントにアクセスするのは難しくなります。", "company": "GMOグローバルサイン", "service": "TrustLogin, GMOセキュリティ24", "type": "FAQ"}, {"question": "二要素認証（2FA）と多要素認証（MFA）の違いについて", "answer": "**二要素認証（2FA）**は、ユーザーがアカウントにアクセスする際に、2つの異なる要素を使用して本人確認を行うセキュリティ手法です。例えば、パスワード（知識要素）とスマートフォンに送られるワンタイムパスワード（所有要素）を組み合わせて認証を行います。\n\n一方、**多要素認証（MFA）**は、2つ以上の異なる要素を使用して本人確認を行うセキュリティ手法です。多要素認証では、知識要素、所有要素、生体要素の中から2つ以上の要素を組み合わせて認証を行います。例えば、パスワード（知識要素）、スマートフォンに送られるワンタイムパスワード（所有要素）、そして指紋認証（生体要素）を組み合わせることができます。\n\nつまり、二要素認証は多要素認証の一部であり、多要素認証はより多くの要素を組み合わせることでセキュリティをさらに強化する方法です。", "company": "GMOグローバルサイン", "service": "TrustLogin, GMOセキュリティ24", "type": "FAQ"}, {"question": "強固なパスワードはどうやって設定しますか？", "answer": "強固なパスワードを設定するためには、以下のポイントを押さえることが重要です。\n\n長さと複雑さ: パスワードはできるだけ長くし、英字（大文字と小文字）、数字、記号を組み合わせることが推奨されます。例えば、「Strong+cyber~security1」のように、大文字と小文字、記号、数字を含めると良いです。\n個人情報を避ける: 誕生日や電話番号、名前などの個人情報をパスワードに使用しないようにしましょう。これらは他人に推測されやすいためです2。\n辞書に載っている単語を避ける: 辞書に載っているような単語や地名などは避け、ランダムな文字列を使用することが望ましいです。\nパスワードの使い回しをしない: 同じパスワードを複数のサービスで使い回すことは避けましょう。各サービスごとに異なるパスワードを設定することで、1つのパスワードが漏洩しても他のサービスへの影響を防ぐことができます2。\nパスワードマネージャーの利用: パスワードマネージャーを使用して、複雑なパスワードを安全に保管し、管理することができます2。\nこれらのポイントを守ることで、強固なパスワードを設定し、セキュリティを強化することができます。さらに、パスワード管理ツールを活用することで、複雑なパスワードを忘れずに管理することができます。\n\nGMOトラスト・ログインではパスワード生成の機能も有しており、手軽に強固なパスワードを生成することも可能です。", "company": "GMOグローバルサイン", "service": "TrustLogin, GMOセキュリティ24", "type": "FAQ"}, {"question": "漏洩したサイト（パスワードが漏洩したサイト）はどこのサイトですか？", "answer": "診断結果に表示されたサイト名、該当サービスをご確認ください。", "company": "GMOグローバルサイン", "service": "TrustLogin, GMOセキュリティ24", "type": "FAQ"}, {"question": "パスワードマネージャーのおすすめはありますか？", "answer": "個人で利用する場合、スマートフォンやブラウザに標準搭載のパスワードマネージャーがいくつかあります。\niPhoneやiPadには標準のパスワードマネージャーが、AndroidではGoogleが提供するパスワードマネージャーが備わっています。またmacOS Sequoiaからはパスワードアプリが標準搭載され、iPhoneなどの端末と連携が可能です。Google Chromeではパスワードマネージャーが標準で使え、Googleアカウントと同期することにより別の端末でも利用可能です。\n法人で利用する場合、組織内での管理を容易にするためのソリューションがいくつかあります。\n\nGMOトラスト・ログインは組織内のパスワードを一元管理するIDaaSであり、ユーザーの代わりに各種サービスへのパスワード入力を代行します。ユーザーは自分のアカウント情報を一つ覚えるだけで、あらゆるサービスにログインできるようになります。\nまた、パスワード生成の機能も有しており、手軽に強固なパスワードを生成することも可能です。", "company": "GMOグローバルサイン", "service": "TrustLogin, GMOセキュリティ24", "type": "FAQ"}, {"question": "証明書の有効期限や、フリーSSLの〇〇を利用中と結果がでてきてもよくわからない", "answer": "ウェブサーバーに設定されているSSL証明書の有効性や発行元の認証局の情報などを確認しています。SSL証明書には有効期限があり、有効な証明書を使用する必要があります。また、フィッシングサイトの95%に無料SSL証明書が使われているというデータもあります。有償のSSL証明書や企業認証SSL証明書を設定することで、ウェブサイト訪問者にセキュリティの高さをアピールし、安心して利用してもらうことができます。", "company": "GMOグローバルサイン", "service": "SSL, GMOセキュリティ24", "type": "FAQ"}, {"question": "何の診断をしているか、何をもって診断しているかがわからない", "answer": "ウェブサーバーに設定されているSSL証明書の有効性や証明書情報の正確性、証明書を発行している認証局の情報などを診断しています。", "company": "GMOグローバルサイン", "service": "SSL, GMOセキュリティ24", "type": "FAQ"}, {"question": "証明書のリスクが出たけどどうすればいいかわからない", "answer": "ウェブサーバーに有効なSSL証明書を設定する必要があります。GMOグローバルサインにお問い合わせ頂ければ詳しい案内をご提供いたします。", "company": "GMOグローバルサイン", "service": "SSL, GMOセキュリティ24", "type": "FAQ"}, {"question": "証明書、SSLが何かわからない", "answer": "SSLサーバ証明書は、ウェブサイトの「運営者の実在性を確認」し、ブラウザとウェブサーバ間で「通信データの暗号化」を行うための電子証明書で、GMOグローバルサインなどの認証局から発行されます。 SSLサーバ証明書には、ウェブサイトの所有者の情報や、暗号化通信に必要な鍵、発行者の署名データが含まれています。SSLサーバ証明書が導入されたウェブサイトは、URLがHTTPSから始まり、先頭には鍵マークが表示されるようになり、SSL通信が提供できるようになります。", "company": "GMOグローバルサイン", "service": "SSL, GMOセキュリティ24", "type": "FAQ"}, {"question": "漏洩した（パスワードが漏洩した）と出てきたが、どうしたらいいか？", "answer": "漏洩したサイトと漏洩した日付をご確認いただき、漏洩した日以降にパスワードを変更していない場合はなるべく早く変更してください。また、他のサイトでも同じパスワードを使い回している場合は同様に変更を推奨します。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "他のサイトで流出（漏洩）（パスワード流出）（パスワード漏洩）があるというように検出されました", "answer": "漏洩したサイトと漏洩した日付をご確認いただき、漏洩した日以降にパスワードを変更していない場合はなるべく早く変更してください。また、他のサイトでも同じパスワードを使い回している場合は同様に変更を推奨します。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "何で漏洩（パスワードの漏洩）したことがわかるのですか？", "answer": "お客様がご利用されているサービスがサイバー攻撃を受けて個人情報が漏洩した場合、その漏洩データがインターネット上に公開されるケースがあります。パスワード漏洩診断では、そのような過去に漏洩したデータを蓄積しているサービス（Have I Been Pwned）と連携してお客様のメールアドレスに紐づく漏洩情報を提供しております。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "漏洩は（パスワードの漏洩は）どうやって診断したのですか？", "answer": "お客様がご利用されているサービスがサイバー攻撃を受けて個人情報が漏洩した場合、その漏洩データがインターネット上に公開されるケースがあります。パスワード漏洩診断では、そのような過去に漏洩したデータを蓄積しているサービス（Have I Been Pwned）と連携してお客様のメールアドレスに紐づく漏洩情報を提供しております。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "漏洩（パスワードの漏洩）とはどういうことですか？", "answer": "お客様がご利用いただいているサービスがサイバー攻撃を受け、お客様の情報が漏洩したことを示しています。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "何が漏洩したのですか？", "answer": "お客様のメールアドレスとパスワードに関する情報が漏洩しています。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "漏洩したサイト（パスワードが漏洩したサイト）とは何のことですか？", "answer": "お客様がメールアドレスで登録しているサービスのうち、サイバー攻撃を受けて情報が漏洩したものを指します。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "アカウントの不正アクセスのリスクってどういうことですか？", "answer": "アカウントが不正に利用され、意図しない操作を実行されることを指します。例えば個人情報を閲覧されたり、決済を実行されたり、なりすましなどをされるおそれがあります。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "二要素認証（多要素認証）とは何のことですか？", "answer": "2つ以上の情報を組み合わせてユーザーを認証することを指します。多くの場合はパスワードで認証をしますが、これに加えて、ユーザーにメールなどで共有されたワンタイムパスワードを入力させたり、秘密の質問など、ユーザーしか知り得ない情報を用いて認証します。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "二要素認証（多要素認証）はどうやったらいいですか？", "answer": "まずは対象のサービス側で対応しているかご確認いただき、対応している場合はサービスのマニュアルに従って設定をお願いいたします。多くの場合、「アカウント管理」や「個人設定」などのメニューから設定できることが多いです。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "パスワードマネージャーとは何ですか？", "answer": "サイト毎に異なるパスワードを自動で生成し、保管してくれるツールです。ブラウザと連携することでサービスへログインする際に自動でパスワードを入力したり、異なるデバイス間で同期するなどの機能があります。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "パスワードマネージャーのおすすめはありますか？", "answer": "個人で利用する場合、スマートフォンやブラウザに標準搭載のパスワードマネージャーがいくつかあります。\niPhoneやiPadには標準のパスワードマネージャーが、AndroidではGoogleが提供するパスワードマネージャーが備わっています。またmacOS Sequoiaからはパスワードアプリが標準搭載され、iPhoneなどの端末と連携が可能です。Google Chromeではパスワードマネージャーが標準で使え、Googleアカウントと同期することにより別の端末でも利用可能です。\n法人で利用する場合、組織内での管理を容易にするためのソリューションがいくつかあります。\n【要GSレビュー】GMOトラストログインは組織内のパスワードを一元管理するIDaaSであり、ユーザーの代わりに各種サービスへのパスワード入力を代行します。ユーザーは自分のアカウント情報を一つ覚えるだけで、あらゆるサービスにログインできるようになります。\n1Passwordはよく使われるパスワードマネージャーの一つです。組織内でのパスワード共有や、パスワード以外の秘密情報の保存にも数多く対応しています。また、個人で利用することも可能です。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "漏洩（パスワードの漏洩）と出てきましたが、おすすめの（一番良い）対策は何ですか？", "answer": "パスワードマネージャーを利用して、より強固なパスワードを設定してください。すべてのサイトで異なるパスワードを生成し、使い回しを避けてください。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "漏洩データはどれくらいの頻度で更新されますか？", "answer": "サービスがサイバー攻撃を受け、新たにその漏洩情報が追加された時に更新されます。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "結果に出る「緊急｜高リスク｜中リスク｜低リスク｜情報」とその数字が何を意味するかの説明がなく、よくわからない", "answer": "ほぼ全ての場合において脆弱性は複数個検出されますが、それぞれの危険性は異なり、危険度の高いものから順に「緊急、高リスク、中リスク、低リスク、情報」とレベル分けをしています。お客様のサイトで検出された脆弱性のうち、それぞれのレベルのものがいくつずつあるのか、その数が横に数字で記載されています。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "所見ありや欠陥ありと診断されても何していいのかわからない", "answer": "脆弱性への対策方法を同時に記載しておりますので、お客様で判断可能な場合はご対応をお願いします。もしわからない場合や、代行を依頼される場合はフォームよりご相談ください。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "診断結果としてリストアップされる内容（緊急｜高リスク｜中リスク｜低リスク｜情報それぞれ）が専門的すぎてよくわからない", "answer": "誤解を避けるため、専門家向けの説明文が標準となっておりますのでご容赦ください。もしご不明な点などありましたらお気軽にご相談ください。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "「より詳しい診断や対策の相談はこちら」を押しても、上記の内容には触れておらず、いきなりネットde診断やCloud SaaSが出てくる", "answer": "お客様の状況に応じて適切な案内をするため、まずはお困りごとについてご相談ください。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "Webサイトリスク診断で、診断結果が要対策だったが、対応は必要？", "answer": "Webサイトリスク診断では、リスクレベルが高リスクまたは中リスクのものが含まれていた場合に要対策となります。緊急度の高い脆弱性から対応することを推奨します。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "Webサイトリスク診断で、診断結果が要緊急対応だったが、どうすればよいか？", "answer": "Webサイトリスク診断では、リスクレベルが緊急のものが含まれていた場合に要緊急対応となります。危険度の高い脆弱性が存在する状態であり、サイバー攻撃の標的になりやすい状況です。至急対応することを推奨します。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "クラウド利用・リスク診断の結果って何ですか？", "answer": "クラウド利用・リスク診断は、入力された内容内のドメインの名前解決結果などから、「クラウドサーバー（AWS・Google Cloud・Microsoft Azureのいずれかのパブリッククラウド）が利用されているか」を判定する診断です。疑似攻撃などは行っていないため、必ずしもセキュリティリスクを示すものでは有りませんが、クラウド利用が認められる場合は適切に利用できているか？を確認することをお勧めします。", "company": "GMO Flatt Security", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "クラウド利用・リスク診断で要確認と出たが、これは何ですか？", "answer": "クラウド利用・リスク診断は、入力された内容内のドメインの名前解決結果などから、「クラウドサーバー（AWS・Google Cloud・Microsoft Azureのいずれかのパブリッククラウド）が利用されているか」を判定する診断です。疑似攻撃などは行っていないため、必ずしもセキュリティリスクを示すものでは有りませんが、クラウド利用が認められる場合は適切に利用できているか？を確認することをお勧めします。", "company": "GMO Flatt Security", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "クラウドを利用している、していない、と結果がでてきてもよくわからない", "answer": "クラウドを利用している場合は、クラウドを利用している場合、サービスの設定ミス（本来公開してはいけないデータを公開してしまう等）によるセキュリティリスクがある可能性があります。継続的にどんなデータ・サーバーがあるか、どんなアプリケーションが動いているかを管理しておくのが重要です。安全にシステム構築・運用できるよう、クラウド活用時のセキュリティベストプラクティスを学び、自社のシステムは大丈夫か？を確認してみてください。", "company": "GMO Flatt Security", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "クラウド利用・リスク診断がどこを診断をしているか、何をもって診断しているかがわからない", "answer": "クラウド利用・リスク診断は、入力された内容内のドメインの名前解決結果などから、「クラウドサーバー（AWS・Google Cloud・Microsoft Azureのいずれかのパブリッククラウド）が利用されているか」を判定する診断です。疑似攻撃などは行っていないため、必ずしもセキュリティリスクを示すものでは有りませんが、クラウド利用が認められる場合は適切に利用できているか？を確認することをお勧めします。", "company": "GMO Flatt Security", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "クラウドが何かわからない", "answer": "ここではAWS・Google Cloud・Azureのような、Web サイトの公開などに使えるサーバーを借りられるホスティングサービス（IaaS）や、それに類するサービスを指します。オフィス内に物理的なサーバーを置かずとも、インターネット上に好きなときに好きなだけサーバーを構築できる点が魅力です。反面、サーバーの運用はあくまで利用者の責任であり、セキュリティにも留意する必要があります。", "company": "GMO Flatt Security", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "「SPFレコードの設定｜DMARCレコード設定」のあり/なしが何を意味するかよくわからない", "answer": "① SPFレコード設定（送信元サーバーの正当性確認）\nSPFレコードは、「このドメインから送信してよいメールサーバー」を指定するための設定です。\nこれにより、受信側がメールの送信元が正当であるかどうかを判断できます。SPFを設定していないと、第三者が自社のドメインを不正に使ってメールを送信できてしまい、なりすましメールのリスクが高まります。\n適切に設定することで、不正な送信元からのメールをブロックし、より信頼性の高いメール環境を実現できます。\n\n② DMARCレコード設定（なりすましメールの処理方針）\nDMARCレコードは、SPFやDKIMの認証結果に基づいて「認証に失敗したメールをどう処理するか」を決める仕組みです。\nこれが設定されていないと、認証に失敗したメールがそのまま受信者に届いてしまう可能性があります。DMARCを設定することで、認証に失敗したメールを拒否したり、迷惑メールフォルダに振り分けたりすることができ、なりすまし対策がより強化されます。\nさらに、DMARCにはレポート機能もあり、不正な送信の状況を把握して、より適切なセキュリティ対策を講じることが可能になります。", "company": "GMOブランドセキュリティ", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "ランクの安全/要注意/危険/非常に危険がどんなものかわからない", "answer": "ランクは、あなたのブランドが「なりすまし被害にどれくらい弱いか」を示しています。\n\n安全 → なりすましリスクが低く、適切な対策ができている状態です。\n要注意 → 今のところ安全ですが、油断せす対策を考えましょう。\n危険 → いくつかの対策が不足しており、なりすましの危険性が高い状態です。早めの対策をしましょう。\n非常に危険 → なりすまし被害に遭いやすい状態です。早急に対策を行いましょう。", "company": "GMOブランドセキュリティ", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "「類似ドメイン数｜BIMI｜VMC｜ブランドTLD」とその数字やあり/なしが何を意味するかの説明がなく、よくわからない", "answer": "① 類似ドメイン数（なりすまされやすさ）\nあなたの会社のドメイン（例：example.com）と似たドメインがどれくらいあるかを示します。\n例えば、examp1e.com（数字の1が入る） や example.co（.coドメイン） などがあると、詐欺サイトを作られやすくなります。類似ドメインが多いほど、注意が必要です。\n\n② BIMIレコード設定（正規のロゴ表示）\nBIMI（Brand Indicators for Message Identification）は、メール送信者の公式ロゴを表示する仕組み です。\n設定がないと、公式のメールかどうか判断しにくくなり、偽のメールが信用されやすくなります。\nBIMIを設定すれば、なりすましメールを見破りやすくなります。\n\n③ VMC設定（公式ロゴの証明書）\nVMC（Verified Mark Certificate）は、BIMIのロゴが本物であることを証明する電子証明書 です。\nVMCがないと、BIMIのロゴを使えなかったり、なりすまし防止効果が十分でないこともあります。\nVMCを取得すると、メールの信頼性がさらにアップします。\n\n④ ブランドTLD（専用ドメインの強さ）\nブランドTLDとは、「.com」や「.jp」ではなく、企業専用のドメイン（例：.gmo）」を使うこと です。\n例えば、「.gmo」ドメインを持っていれば、他の人は取得できません。\n偽物サイトを作られにくくなり、なりすましのリスクを減らすことができます。", "company": "GMOブランドセキュリティ", "service": "GMOセキュリティ24", "type": "FAQ"}, {"question": "ランクを改善していくにはどうすればよい？", "answer": "ランクを改善するためには、なりすまし対策を強化することが重要です。具体的には、以下のような対策を行います。\n■ 類似ドメイン対策（なりすましサイト防止）\n 「example.com」というドメインを持っている場合、「example.net」や「examp1e.com（数字の1を使った偽装ドメイン）」など、攻撃者がよく利用する類似ドメインを先に登録しておくと安全です。また、ドメイン監視サービスを利用して、新しく作成された類似ドメインをチェックします。フィッシングイトが見つかった場合には、ドメインの削除依頼や停止手続きを迅速に行うことが大切です。\n\n■ SPFレコード設定（送信元メールサーバーの認証）\n SPFレコードは、「このドメインから送信してよいメールサーバー」を指定する設定す。これにより、受信側が正規の送信元かどうかを確認できます。SPFを設定していないと、第三者が勝手に自社のドメインを使ってメールを送ることができ、なりすましメールのリスクが高まります。設定することで、不正な送信元からメールをブロックし、信頼性の高いメール環境を維持できます。\n\n■ DMARCレコード設定（認証失敗メールの処理設定）\n DMARCレコードは、SPFやDKIMの認証結果を基に、「認証に失敗したメールをどう処理するか」を決めるルールです。これがないと、なりすましメールがそのまま届く可能性があります。DMARCを設定すれば、認証に失敗したメールを拒否したり、迷惑メールフォルダに振り分けたりでき、なりすまし対策が強化されます。また、レポート機能により、不正な送信の状況を把握し、より適切な対策を取ることが可能になります。\n\n■ BIMIレコードを設定（公式ロゴによる信頼性向上）\n BIMIをメール送信用のドメインに設定すると、公式のメールであることが視覚的にわかりやすくなり、偽物との区別が容易になります。BIMIを利用するためには、DMARCポリシーを「p=reject（拒否）」または「p=quarantine（隔離）」に設定する必要があります。BIMIに対応したメール環境を整備することで、メールに公式ロゴが表示されるようになります。\n\n■ VMCを取得（BIMIロゴの信頼性強化）\n BIMIで使用するロゴを正式に証明するため、VMC（Verified Mark Certificate）を取得します。これにより、メールの信頼性がさらに高まります。\n\n■ ブランドTLDを活用（なりすまし防止の根本対策）\n 例えば「.gmo」のような専用トップレベルドメイン（ブランドTLD）を取得すると、第三者が類似したドメインを取得することを防ぐことができ、安全性が大きく向上します。また、ブランドTLDを活用すると、検索結果でもリスク評価ランクが低くなりやすくなります。公式サイトのURLをブランドTLDに統一し、ユーザーに対して「このドメイン以外は公式ではありません」と周知徹底することも重要です。\nこれらの対策を進めることで、リスクラクをD（高リスク）からA（低リスク）へ改善することが可能になります。なりすまし被害を防ぐには、早期に対策を取ることが最も重要です。", "company": "GMOブランドセキュリティ", "service": "GMOセキュリティ24", "type": "FAQ"}]