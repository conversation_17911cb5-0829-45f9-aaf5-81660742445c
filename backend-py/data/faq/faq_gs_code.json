[{"question": "EVコードサイニング証明書と通常のコードサイニング証明書は何が違うのですか", "answer": "大きな違いは、EV コードサイニング証明書で署名されたプログラムは、Windows 8で採用されたSmartScreenの警告が表示されないようになることです。また、USBトークンに証明書・秘密鍵を格納することで盗難や漏洩から保護します。", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "コモンネームについて教えてください", "answer": "コードサイニング証明書のコモンネームは、お申し込みの形態により組織名や部門名または個人名が該当いたします。\n詳細はこちら（\nhttps://jp.globalsign.com/support/cert-management/common-name.html\n）\n", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "ルート証明書に関して", "answer": "詳細およびダウンロードはこちら（\nhttps://jp.globalsign.com/support/rootcertificates/\n）よりご確認ください。\n", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "PKCS#12とは", "answer": "Public Key Cryptography Standard#12の略です。公開鍵証明書および秘密鍵をパスワードベースの対象鍵で保護し、 安全に保存または転送するために使用されるファイル形式です。", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "タイムスタンプについて", "answer": "タイムスタンプにより、デジタル署名を実施した日時情報を署名に含めることができます。タイムスタンプシステムから取得した情報を利用することで、署名時の正確な日時を設定できます。この機能により、証明書の期限後もタイムスタンプの期限までは有効な署名であると認識されます。なお、タイムスタンプの有効期限は署名時から最低10年以上です。\n※デジタル署名の検証を行う環境の種類やバージョンにより、タイムスタンプに関する仕様が異なります。実際の挙動については、クライアント環境の提供元にご確認ください。タイムスタンプを利用せずにデジタル署名を実施した場合、証明書の期限が切れると署名も無効になります。そのため、証明書の期限後に署名の検証を行うと署名がされていない場合と同様のセキュリティ警告が表示されるようになります。", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "コードサイニング証明書のライセンスについて", "answer": "コードサイニング証明書は組織名に対して発行されます。同一組織内での使用であれば、複数のソフトウェアに対してご利用いただけます。署名したソフトウェアを配布するサーバ、および配布先PCの台数に制限はございません。一枚の証明書を複数端末で使用できますが、証明書自体はUSBトークンに格納されているため、利用時には署名を行うPC端末にトークンを接続する必要があります。※複数拠点や複数端末で同時に署名を行いたい場合は、同一情報の証明書を必要な数だけ購入いただく必要があります。", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "JAVAの実行環境は、どのバージョンで対応していますか", "answer": "以下バージョンにてご利用可能となっております。\n(署名ハッシュアルゴリズム SHA256の場合)\nJRE 1.8.0 以降\nJRE 1.7.0_76 以降\nJRE 1.6.0_38 以降\n\n関連リンクJava SE 6 Update Release Notes（\nhttp://www.oracle.com/technetwork/java/javase/6u3-137914.html\n）\n\nJava SE 6 Update Release Notes（\nhttp://www.oracle.com/technetwork/java/javase/6u7-136303.html\n）\n", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "コードサイニング証明書は個人でも利用可能ですか。", "answer": "コードサイニング証明書は組織名用と個人用によって異なる認証を行うため、個人の方でもご導入いただけます。※2018年12月10日（月）をもちまして、個人向けコードサイニング証明書の販売（新規・更新）を終了いたします。購入をご検討される際は十分にご注意ください。個人向けコードサイニング証明書販売終了のお知らせ（\nhttps://info-globalsign.com/news/1527211220\n）対象サービスMS\n Authenticode対応（ActiveXなどのMicrosoft用ソフトウェアへ）（\nhttps://jp.globalsign.com/service/codesign/authenticode.html\n）Object\n signing対応（JAVA対応のアプレット・ソフトウェアへ）（\nhttps://jp.globalsign.com/service/codesign/objectsigning.html\n）MS\n Office VBA対応（VBAやマクロといったコードへ）（\nhttps://jp.globalsign.com/service/codesign/vba.html\n）Adobe\n AIR 対応（署名が必須なAIRアプリケーションへ）（\nhttps://jp.globalsign.com/service/codesign/adobeair.html\n）\n", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "証明書の期限について教えてください", "answer": "証明書はお申し込みの年数によって、以下のような期限が設定されます。\n1年: 実利用日数が366（365 + 1）日間\n2年: 実利用日数が731（365 * 2 + 1）日間\n3年: 実利用日数が1096（365 * 3 + 1）日間たとえば、2008年4月1日に発行された1年契約の証明書は、2009年4月2日が有効期限となります。\n※更新いただいた場合、上記に更新ボーナス期間がプラスされます。", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "グローバルサインでは、証明書の発行が早くなりますか", "answer": "国内で証明書を発行できますので、以前より迅速、柔軟な対応が可能です。また、条件次第となりますが、後払いによる証明書発行も可能です。", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "Authenticodeで署名可能なファイルはどのような形式がありますか", "answer": "マイクロソフト社の署名ツールに依存する部分となりますが、exe、dll、ocx、cab、stl、cat に対応しております。詳細につきましては、以下のURLもご参照ください。\nhttp://msdn2.microsoft.com/en-us/library/ms537364.aspx（\nhttp://msdn2.microsoft.com/en-us/library/ms537364.aspx\n）\n", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "MS Authenticode対応コードサイニング証明書において、カーネルモード署名でもSHA256証明書で署名できますか", "answer": "2015年3月現在、Windows Vista環境において署名アルゴリズムSHA256証明書による64bitカーネルモード署名はサポートされておりません。署名アルゴリズムSHA-1証明書を利用する必要があります。関連リンクSigning a Driver for Public Release（\nhttp://msdn.microsoft.com/en-us/library/windows/hardware/hh967734%28v=vs.85%29.aspx\n）\n", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "CP・CPS、利用約款はどこにありますか。", "answer": "リポジトリ（\nhttps://jp.globalsign.com/repository/\n）をご参照ください。\n", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "署名されたアプリケーションを実行しようとすると、Windows Defender SmartScreenが表示されるのはなぜですか。", "answer": "中間CA証明書が変更された証明書で署名をした場合、証明書におけるMicrosoft側での評価がリセットされることが原因です。\n詳細はこちら（\nhttps://jp.globalsign.com/support/codesign/certificates/defender-smartscreen.html\n）\n", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "HSMとはなんですか", "answer": "一般的には電子証明書の暗号鍵と鍵管理に関する国際規格を取得しているデバイスを「HSM」と呼びます。グローバルサインでは上記基準に準拠した「HSM」を保持するユーザー様に対して、物理型、クラウド型を問わずHSMに対応した証明書を発行いたします。\n詳細はこちら（\nhttps://jp.globalsign.com/support/codesign/certificates/hsm.html\n）\n", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "USBトークンが端末で認識されない場合の対処法", "answer": "お客様の端末でUSBトークンが認識されていない場合は、下記をご確認ください。\n詳細はこちら（\nhttps://jp.globalsign.com/support/codesign/certificates/recognize-usb.html\n", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "証明書の購入時に初期費用はかかりますか？", "answer": "初期費用はかかりません。必要な費用は証明書代金のみになります。※銀行振り込み時の振り込み手数料はお客様負担となります。\n※税別価格表示時は、別途消費税が必要になります。", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "証明書の有効期限が切れた場合どうなりますか。", "answer": "タイムスタンプを付けることにより、タイムスタンプサーバの証明書の有効期限まで署名は有効となります。そのため、証明書の有効期限後もエラーは表示されません。タイムスタンプが無い場合は、証明書の有効期限が切れると署名も無効になります。\nR6ルート用タイムスタンプURLは下記でございます。\n\nhttp://timestamp.globalsign.com/tsa/r6advanced1\n\n※上記の情報は2024年5月時点のものです。", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "コードサイニング証明書の更新について", "answer": "GSパネルにログインいただき、証明書一覧を開き、該当の証明書の「更新申請」ボタンからお申し込みを進めていただきます。更新いただきますと、現在の証明書の残りの期間＋30日のボーナス期間が付与されます。更新時にディスティングイッシュネームの組織名（個人名）とメールアドレス以外は変更可能となっております。申し込み方法の詳細はこちら（\nhttps://jp.globalsign.com/codesigning/order/renew_codesign.html\n）をご覧ください。\n\n※Adobe AIR対応の証明書を更新される場合、証明書内の情報を前回と一致させるか、移行署名を行わないとAIRアプリケーションのアップデート（\nhttps://airsdk.harman.com/support\n）ができなくなります。\n\n※更新の場合、3年契約での申請はできません。※タイムスタンプを付与している場合、署名時のタイムスタンプサーバの期限まで署名が有効になります。※MS Office VBAでタイムスタンプを付けるには、レジストリの設定が必要です。\n※Object Signingの対応環境は、JRE1.7.0 以降、JRE1.6.0_10以降、1.5.0_16以降、1.4.2_18以降になります。", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "証明書の有効期限が切れてしまいましたが、更新できますか。", "answer": "有効期限が切れた後、2週間以内でしたら更新お手続き（\nhttps://jp.globalsign.com/codesigning/order/\n）が可能です。2週間以上経過されました場合は、恐れ入りますが新規の扱いとなりますため、GSパネルの「証明書発行」メニューより、新規でお申し込みください。\n", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "更新の際の通知はもらえるのでしょうか。", "answer": "更新のご案内は、有効期限の3ヶ月前よりお知らせしております。90・60・30・21・14・7・0日前と期限の7日後にメールを送信いたします。ご連絡は登録のメールアドレス宛になります。契約者と別に技術担当者を設定いただき、メール送信にチェックを入れている場合は2者に対しメールを送信いたします。メールアドレスが変更となりました場合は、GSパネルより変更手続きをお願いいたします。\nお客様情報の修正について（\nhttps://jp.globalsign.com/support/customer/edit-userinfo.html\n）\n\n※技術担当の変更は、証明書一覧 >> 編集ボタン よりお願いいたします。関連リンク更新案内の停止方法（\nhttps://jp.globalsign.com/support/cert-management/renew-info-stop.html\n）\n", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "証明書の再発行をしましたが、どれが最新かわかりません", "answer": "最新の証明書は、ステータスが「発行済み」となっています。GSパネルから再発行をお申し込みいただくと、元のご注文は「再発行済み」というステータスになります。この注文の他に再発行分のオーダーIDが新規に作成され、ステータスが「申請済み」から「発行済み」となりましたら発行完了です。発行にはおよそ10分ほどかかります。", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "再発行は無料ですか。", "answer": "グローバルサインのサーバ証明書は、管理画面から無料で即日再発行させていただきます。再発行のお手続き（\nhttps://jp.globalsign.com/support/cert-management/reissue.html\n）\n", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "まとめ買いのレベルはどのように変動しますか。", "answer": "まとめ買いによって価格レベルが決定しましたら、そのレベルは、デポジットを購入いただいた日から1年間有効です。1年以内でも、更に上のレベルでデポジットを購入いただくと、その時から1年間、レベルが上がることになります。詳しくは下記を参照ください。詳細はこちら（\nhttps://jp.globalsign.com/support/cert-management/deposit-level.html\n）\n", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "パートナーにならないとまとめ買いはできませんか。", "answer": "「まとめ買い」はどなたでもご利用いただけます。一定の価格を超えます場合は、ぜひ「まとめ買い」をご利用ください。「まとめ買い購入支援ツール」による割引購入のお手続き（\nhttps://jp.globalsign.com/support/cert-management/deposit-ssl.html\n）「まとめ買い直接購入」による割引購入のお手続き（\nhttps://jp.globalsign.com/support/cert-management/deposit.html\n）なお、企業認証SSLは、「契約者＝認証する企業」であることを確認するため、1つのアカウントで複数の企業のお申し込みを管理いただくことができません。パートナー契約を結んでいただきますと、1つのパートナーアカウントで複数のお客様の企業認証SSLを管理することが可能です。パートナープログラムについて（\nhttps://jp.globalsign.com/partner/\n）クイック認証SSLは、ドメイン管理者による承認をメールにて行うサービスであるため、承認いただける状態であれば発行可能です。ご登録内容に制限はありません。\n", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "パートナープログラムに関してのQ&A", "answer": "パートナープログラムに関するよくある質問をまとめました。下記をご参照ください。詳細はこちら（\nhttps://jp.globalsign.com/partner/faq.html\n）\n", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "契約者ではありませんが、お問い合わせは可能ですか。", "answer": "証明書の設定を代行されている方から、設定に関するお問い合わせをいただくことに問題はございません。ご利用状況を特定できる、コモンネームまたはオーダーIDをご用意のうえ、お問い合わせください。ご契約に関する個人的な情報は、いただいたお電話ではご案内いたしかねますので、その点はご了承ください。弊社の販売代理店を通してお申し込みの方は、契約上販売店でのサポートとなりますので、ご購入の業者様にお問い合わせくださいますようお願いいたします。", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "ネットワークに制限がある環境でも利用できますか", "answer": "ネットワークに制限がある環境で証明書をご利用いただく場合、下記の点にご注意ください。\n証明書発行のための審査時証明書・ドメインの審査にページ認証をご利用いただく場合、弊社システムが対象のページへアクセス可能である必要がございます。そのため、ご希望の場合には弊社システムのページ認証時に利用するアクセス元IPアドレスをお知らせいたしますので、別途お問い合わせください。\n関連リンク\nドメイン名所有者・使用権の確認について（\nhttps://jp.globalsign.com/support/cert-management/domain-confirmation.html\n）\n\n証明書の失効\n確認一般的に、証明書を利用する際には、証明書の有効性を確認するための失効確認が行われます。失効確認を行う環境が、弊社のシステムへアクセス出来ない環境の場合、弊社のシステムから失効情報を取得出来ないため、失効確認が完了せず、正常にアクセスできない可能性がございます。失効確認を正常に行うためには、失効確認を行う環境にて、対象の証明書・中間CA証明書の失効確認時に利用するURLへアクセス可能な状態を構築していただく必要がございます。なお、失効確認を行う環境はご利用いただく商材によって下記のように異なります。\n・SSLサーバ証明書証明書を設定したサーバに対して、アクセスを行うクライアント環境\n・クライアント証明書の提示を受ける、サーバ環境・コードサイニング証明書、文書署名用証明書署名済みプログラムの検証を行う環境\n関連リンクCRL/OCSPのURLの確認方法（\nhttps://jp.globalsign.com/support/cert-management/crl-ocsp-url.html\n）コードサイニング証明書、文書署名用証明書でのタイムスタンプ署名を行う際に、タイムスタンプを付与される場合、弊社システムへのアクセスが必要となります。タイムスタンプの付与はタイムスタンプURLを指定することで行うため、当該URLへアクセス可能な状態を構築していただく必要がございます。\n\n※タイムスタンプURLにつきましては、弊社署名マニュアルに記載がございます。関連リンク設定マニュアル（\nhttps://jp.globalsign.com/support/codesign/manual\n）\n", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}]