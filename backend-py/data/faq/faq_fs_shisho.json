[{"question": "Amazon Inspectorの様な機能はある？", "answer": "現時点では非対応です。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "CVEへの追従について", "answer": "現時点ではCVEに関しては、Log4jの様に「外部」から「容易に攻撃がなされる」脆弱性については緊急度に応じて対応を行っていく予定です。\n全ての既知の脆弱性に対応することは現時点では考慮しておりません。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "FaaSやコンテナへの対応(CWPP)", "answer": "(CWPPの話)現時点では対応できていない部分があります。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "トリアージの際のコメントは履歴として残るのか(意訳)\n(スレッド上に出せたりしませんか?)", "answer": "現時点ではコメントは残せますが、一覧として表示することはできません。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "どんなリクエストを投げてどんな項目を診断した結果の検出事項なのか、表示される？", "answer": "はい。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "MPA でも費用は変わらないか", "answer": "変わりません。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "報告書のエクスポートが可能か", "answer": "2024-10-11時点\n[お客様向け]\n・JSON 形式でのエクスポートには対応可能\n　・画面上の導線はなく、コマンドラインでユーザーに操作いただく必要がございます。\n　・手順書などは用意がないが、ご要望いただければ作成の上ご提出いたします", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "診断済みURLを確認できるか", "answer": "はい。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "スキャンの前に予測時間は出るのか？\n（時間指定できるのか？）", "answer": "いいえ。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "WordPressで作成したアプリも診断できるのか", "answer": "はい。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "CF + ALBみたいな構成で、CFの Managed Prefix Listのみに限定している場合は、御社のIPアドレスを許可する形になりますでしょうか", "answer": "はい。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "認証にCognitoを使っているけど診断できる？", "answer": "大抵の場合は診断できます。Shisho Cloud が自動でCognitoからIDトークンを取得した上で、アプリケーションにリクエストを送信します。\n(※Cognitoの使い方に関する脆弱性検査 (いわゆる Cognito 診断) ではない\n(※チャレンジレスポンスを利用しているなど特殊ケースは未対応)", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "認証にFirebase Authを使っているけど診断できる？", "answer": "メールアドレス+パスワードの認証方式であれば、大抵の場合は診断できます。Shisho Cloud が自動でFirebase AuthからIDトークンを取得した上で、アプリケーションにリクエストを送信します。\n(※以上の話はFirebase Authの使い方に関する脆弱性検査 (いわゆる Firebase Auth 診断) ではない。なおFirebase Auth 診断はWebスキャンとは別でCSPMの一部として提供している)\n(※Sign in with Google などソーシャルログインを使っている場合は未対応)", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "認証にAuth0を使っているけど診断できる？", "answer": "メールアドレス+パスワードの認証方式であれば、大抵の場合は診断できます。Shisho Cloud が自動でAuth0からIDトークンを取得した上で、アプリケーションにリクエストを送信します。\n(※以上の話はAuth0の使い方に関する脆弱性検査ではない。)", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "認証にソーシャルログインを使っているけど診断できる？", "answer": "ソーシャルログインが必要なページの診断は未対応です。\n(※ソーシャルログインを使わずにユーザー名+パスワード直接入力でもログインできるアプリケーションでは診断可能)", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "GraphQL の場合エンドポイントはどのように換算される？", "answer": "GraphQLスキーマを解析し、mutation の数、引数を持つフィールドの数、型の構造などを元にエンドポイント数に換算します。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "プライベートクラウド上にある Web サイトに対する診断は可能ですか？", "answer": "外部から疎通可能（弊社 IP アドレスから疎通可能）であれば診断可能です。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "Web scan において、本番環境への診断は可能ですか？", "answer": "こちらと同じ理由で、可能ではあるが、Web 環境については非推奨です。 \nhttps://flatt.notion.site/2874f87ef09a444084dfc0d56b91d3b6\n", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "Basic 認証をかけて公開しないまま検証をすることは可能でしょうか？", "answer": "可能です。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "Digest 認証をかけて公開しないまま検証をすることは可能でしょうか？", "answer": "未対応です。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "Web自動診断において対応している診断項目は？", "answer": "現時点の対応項目はこちらです。 \nhttps://shisho.dev/docs/ja/g/managed/providers/web/\n\n\nhttps://flatt.tech/assessment/web_app/\n", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "診断のスキャン頻度は？", "answer": "デフォルトでは、クラウドは1時間・Webは1日に設定されていますが、変更可能です。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "フロントエンドの診断も可能？", "answer": "はい、フロントエンドも診断できます。\n=以下社内向け=\nより詳細に書くと、\nMPA の場合: 特に懸念なし\nSPA の場合: フロントエンドの自動巡回 (フロントエンドのページを洗い出し、エンドポイントとして登録する) 精度は AeyeScan に劣ると思われますし、ミートする気もありません。ただし、お客様側で各ページをエンドポイントとして登録していただければ、それをもとに自動診断が可能です。またそのお客様側作業をせずとも、セキュリティヘッダーのチェックなど、トップページだけから判断できる項目は、問題なく診断できます。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "AI活用って具体的には？", "answer": "Web 診断機能の検知ルールの一部は、脆弱性に関する様々な公開資料をもとに AI を活用して生成し、エンジニアがレビュー・手直ししたものです。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "診断にどれくらい時間かかる？", "answer": "2024/09/03時点は↓です。\n\n弊社が用意したサンプルアプリ (30エンドポイント程度)は現状5分程度で診断できます。なお診断時間の短縮を今頑張っているところなので高速化の余地はあります。また、診断対象サーバーのレスポンスの速さや、診断対象のパラメータの多さなどによっても診断時間は変動します。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "診断のレートリミットや並列数", "answer": "アプリケーション単位で設定可能です", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "クラウド上のアプリの所有証明について、アプリケーションが載ってるパブリッククラウドの連携はやります。ただ、診断対象としてはELBのドメインじゃなくて、CNAMEでELBのドメインを参照する自社ドメインを診断対象としたいのだけど、自社ドメインでもCNAMEでELBドメインを参照していれば、クラウド連携で所有の証明になる？", "answer": "はい。アプリケーションのドメイン入力時に、ELBのドメインではなくCNAMEレコードでELBのドメインを参照するような独自ドメインをご入力いただいたとしても、Shisho Cloudは CNAMEレコードを辿ってELBドメインに辿り着くかどうかを検証しますので、問題なく所有証明ができます。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "Teams に通知してほしい", "answer": "Teams が持っている「メールを Teams に自動投稿する機能」と Shisho Cloud のメール通知機能を組み合わせれば実現可能ですが、Shisho Cloud の機能としては未対応です。\n\nhttps://support.microsoft.com/ja-jp/office/%E3%83%92%E3%83%B3%E3%83%88-%E3%83%81%E3%83%[…]%A1%E3%81%99%E3%82%8B-2c17dbae-acdf-4209-a761-b463bdaaa4ca\n", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "レポートをCSVでエクスポートできる？", "answer": "できません。JSON形式でのエクスポートには対応しておりますので、お手数っですがお客様側でCSV形式に加工していただければと思います。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "画面のないAPIは診断できますか？", "answer": "2024/09/06 診断自体は可能ですが、自動巡回が行えないため、API仕様書を Shisho Cloud に連携いただく必要があります。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "エンドポイントの手動登録は可能ですか？", "answer": "2024/09/06 エンドポイントの URL やパラメータ定義等を OpenAPI スキーマの形式で記述してアップロードすることで、手動での登録が可能です。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "パラメータごとに機能が異なる場合は、それぞれ 1 エンドポイント換算になりますか？", "answer": "2024/09/06 はい。それぞれの機能を診断したい場合は別エンドポイントとして登録いただく必要があるためです。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "パブリッククラウド上で運用しているシステムにおいて、ステージング環境が本番環境と同じサーバーに存在する場合、ステージング環境のみに診断することは可能ですか？", "answer": "2024/09/09 同じサーバー（インスタンス）にステージングと本番が存在している場合、ステージング環境にのみ攻撃をしたとしても本番環境に影響を与えるリスクがあるので、推奨していません。もし診断を実施される場合は、サーバーを分けていただくことをお勧めいたします。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "深刻度のカスタマイズは可能？", "answer": "2024/09/10 ワークフローでの書き換えにより、可能です。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "SPA（Javascript が API を叩くアプリケーション）の診断は可能ですか？", "answer": "SPA 自体ではなく SPA が呼び出すバックエンド API の診断であれば、OpenAPI スキーマ(Swagger) や GraphQLスキーマをアップロードいただくことにより、スキーマに定義されている API を診断できます。\nなお、自動巡回による洗い出しについては、JavaScript で制御されているボタン等の自動操作の精度の改善に取り組んでいる途中でして、現時点の自動巡回システムでは自動画面操作や API の洗い出しがうまくいかない場合がございます。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "Ajax があるアプリケーションの診断は可能ですか？", "answer": "アプリケーションが呼び出すバックエンド APIの診断であれば、OpenAPI スキーマや GraphQL スキーマを Shisho Cloud にアップロードいただくことにより、スキーマに定義されている API を診断できます。\nなお、現状の自動巡回システムはa タグやformタグを自動で辿ることができる一方で、JavaScript で制御されているボタン等の自動操作については改善途中でございます。現状の自動巡回システムですと、そのようなボタンを介してのみアクセスできるページや API をうまく発見できない場合がございます。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "利用規約 第7条5項「ユーザーは、対象システムが稼働する第三者事業者のデータセンター、ホスティングサービス、クラウドサービス等がある場合には、必要に応じて本サービス利用者自身の責任と費用において、予め、当該第三者事業者から脆弱性診断実施の承諾を書面若しくはそれに準ずる方法で当社が指定したものにて取得しなければなりません。」についての対応は必要ですか？", "answer": "AWS, Google Cloud の場合は以下が公式に明記されているため、明確な取得は不要です。\n【AWS】\n\nhttps://aws.amazon.com/jp/security/penetration-testing/\nhttps://support.google.com/cloud/answer/6262505?hl=ja\n", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "DNSのTXTレコードを利用してWebアプリの所有を証明することは可能か？", "answer": "現状行えません。また、対応予定もございません。\n理由: DNS レコードをお客様が設定可能であるとしても、必ずしも Web アプリケーションの所有権をお客様が有しているとは確認できないため。\n例: sample.example ドメインをお客様が所有しているとして、そのTXT レコードで所有権を確認いただいても、sample.example ドメインの A レコードの指す先の IP アドレス（や CNAME レコードによる先）は別のステークホルダにより管理されている可能性がある。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "Web診断は自動スケジュールせず、手動トリガーによる診断開始だけしたい", "answer": "可能です。こちらのドキュメントに沿ってご対応ください。\n診断スケジュールをカスタマイズする (任意)", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "Web診断の実行時間を変更できますか？", "answer": "可能です。こちらのドキュメントに沿ってご対応ください。\n診断スケジュールをカスタマイズする (任意)", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "ソースコード診断 for React, Vue.js, Angular, DOM XSS の対応言語は？", "answer": "TS/JS のみ対応（∵ 検査対象は現状 SPA コードに限るため）", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "Slack へのログインは robot 対策みたいなのがある？", "answer": "非対応です。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "診断ジョブ画面の\"Not Satisfied\" の表示は何？", "answer": "FQDN の所有権証明が未完了な状態。\n\nトライアル中 → Not satisfied であっても実施されます。\n所有権確認は、Shisho Cloud が不正アクセス禁止法等の諸法令に抵触する使われ方をしないよう、技術的な制約をかけるためのものでございます。\n他方、現在 B2B でのご提供に形態を限っております為、特に密に会話するトライアル時は必須とはいたしておりません。\n\n本番利用時 → 原則所有権確認をご実施いただくこととし、Not Satisfied の場合実施されません。\nどうしても所有権確認が困難である場合は、両社協議の上、診断をご実施いただけるように調整する場合もございます。\nhttps://flattt.slack.com/archives/C08384ZNJH2/p1733478846314519?thread_ts=1733473847.796999&cid=C08384ZNJH2\n\n\n", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "診断リクエスト送信の際にパラメータを指定することは可能？", "answer": "可能で、方法としては以下の2つ。\n(1) スキーマにパラメータ値が書いてあれば、人間の対応は不要で勝手にパラメータを挿入した状態でリクエスト送信がされる\n(2) スキーマに書いていない場合は、SC コンソール > アプリケーション > エンドポイント  更新タブで value フィールドを変更する", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "POST叩かないとDELETE叩けない、といったAPI(エンドポイント)の診断には、シナリオ作成が必須？スキーマからは読み取れない？", "answer": "スキーマがある場合でも、API1個1個のシナリオしか自動生成されない。2個以上のAPI同士の関係性をSCはスキーマから読み取れないため、シナリオ作成が必要となる。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}]