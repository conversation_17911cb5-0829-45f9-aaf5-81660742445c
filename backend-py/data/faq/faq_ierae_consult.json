[{"question": "クラウドサービスを利用する上でのリスクとしてどのようなものが考えられますか？", "answer": "クラウドサービスを利用することにより”追加される”リスクとしては以下のようなものが考えられます。\n１．情報流出\n　使い方や／設定ミスによる漏洩、クラウドサービス自体への攻撃\n２．クラウドからの攻撃\n　マルウェアを踏ませる経路／C2サーバへの通信経路としての利用\n　高度にクライアントと結びつくサービス経由の攻撃（ChromeRemoteDesktopのようなものを想定）\n３．クラウドを利用した攻撃の中継\n　アカウント乗っ取り（なりすまし）から攻撃の中継点となる事で、他者への攻撃に加担\n（メールサービスアカウントを乗っ取られ、不正メールの送信拠点化するようなパターン）\n４．クレデンシャル漏洩（アカパス使い回し）\n　クラウドサービスへ登録したクレデンシャル（アカウント／パスワード等）がクラウドサービス経由で漏洩して、他のシステムのクレデンシャルとして利用されるパターン。\n　例：\n　とあるクラウドサービスに会社のメールアカウントで登録していた\n　　↓\n　クラウドサービスが攻撃に遭い、メールアカウントとパスワードが漏洩\n　　↓\n　会社へのリモートアクセスで利用するVPNも同じメールアカウント／パスワードで利用していたら・・・\n５．情報発信系の炎上リスク", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "SSOサービスを利用するにあたり、SSO対象サービス側で事前にドメインの認証が必要です。\nHTTPSのサーバー証明書証明書による方法と、DNSへTXTレコードを追加する方法がありますが、後者の場合にリスクはありますか？", "answer": "まず、『ドメイン認証（ドメインの所有権の証明）の方法として、HTTPS（によるファイルの存在確認）とDNSのTXTレコードではどちらが安全性が高いか』という点ですが、HTTP方式が高く、DNS方式は若干低くなります。\n尚、これはHTTPSの証明書が証明局の発行した正規の証明書である場合であって、オレオレ証明書を利用した場合はDNSと同レベルかそれ以下となります。\n主な理由としては、HTTPS方式を突破する場合、「該当サービス側の」DNSを汚染しHTTPSサーバのIPアドレス変更する事に加えて、HTTPSサーバ内の秘密鍵を取得できなければ成立しないですが、DNS方式の場合はDNSサーバのIPアドレスをすり替える事さえできれば成立できます。\n（DNSSec＋DNSのTXTレコード参照であればHTTPS方式よりも若干強度は上ですが、サービス側のDNSSEC対応が不明です）\n一方で、DNS方式がセキュリティ強度的に問題か？　という観点から見た場合、「十分に強度はある」と考えて良いと思います。\n実際、メールのSPFも同じ機構でドメインの所有権の証明としていますし、「DNSサーバのIPアドレスをすり替える」≒「ドメイン乗っ取り」という（SSO突破どころではない）大きなインシデントとなりますのでこの文脈で論じてもあまり意味が無いと考えます。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "中国でビジネスを行う上でのリスクを整理したいのですが", "answer": "『中国』をキーワードとした脅威を分類すると\n１．中国国内法における脅威（主に中国市場への進出／撤退によるもの）\n２．米国による対中国の規制における脅威（中国企業との取引／提携によるもの）\n３．中国共産党から支援を受けたハッカーによるサイバー脅威\n４．技術の奪取を目的とした技術者へのコンタクト／技術情報への不正アクセスの脅威\n５．戦争／紛争の勃発\n以上の５つになるのではないかと考えています。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "外部の協力会社とのファイル共有のサービスを選定したいのですが、ポイントは？", "answer": null, "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "WAFの選定で考慮すべきポイントはありますか？", "answer": null, "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "クラウドサービスを選定するとき、サービス自体の安全性はどう考えればいいですか？", "answer": null, "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "インシデント対応をする人員/チーム/組織が無く、情報システム部門が担当しているが限界がある。何か良いやりかたはあるか？", "answer": "教育を受けるなどは挙げられるが、即効性があるものではない。ベンダへ相談や依頼ができるような体制を作っておくと良い。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "インシデントが起こったときに動けるかがわからない/自信がない。どうしたら良いか？", "answer": "インシデント対応フローの整備を行い、訓練を実施することでフローや対応の評価を行う", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "インシデントにどう対応したら良いかわからない", "answer": "インシデントと定義できる基準の作成、情報伝達フローや意思決定者を明確にする。自社で持っているツールやログなどを元に被害範囲と原因特定を目標にすると良い。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "従業員教育の良いやり方がわからない（他部門教育コンテンツとの差別化など）", "answer": "まずはセキュリティに興味を持ってもらうようなコンテンツを作成すると良い。従業員の生活的関心の高いもの（銀行系や配送業者系のフィッシングなど）を扱うなどすると良い。また、教育の目的をしっかりと定めて目標からブレないようにすることが大切（啓蒙ではなく実施することが目的になってしまうのは良くない）", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "ルールを策定しても従業員が理解してくれない", "answer": "禁止するルールを作ると抜け穴を探す人が出てくる。許容するルールを策定すると良い（この条件下でなら認める、など。例えば、新しいクラウドサービスを利用したい場合、アカウント作成管理のPWルールやMFA有効化、管理者の設定など）", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "CSIRTを設立したいが何から手を付けていいかわからない。", "answer": "まずは自組織の守るべき対象と起こりうるインシデントを明らかにしましょう。その対象資産を守るために必要なメンバーやツールを揃えるようにすると道筋が見えやすい。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "CSIRTを設立したが、何をしたら良いかわからない。", "answer": "まずは自組織の守るべき対象と起こりうるインシデントを明らかにしましょう。その対象資産を守るために必要なメンバーやツールを揃えるようにすると道筋が見えやすい。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "セキュリティ施策をどう考えたら良いかわからない。", "answer": "まずは現状のリスクの洗い出しと分析を行う。明らかになったリスクに対して防御/緩和できるかどうかを基準に考慮すると良い。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "CSIRT、SOC、CISOなど役割は作ったもののうまく連携できていない", "answer": "自社における情報セキュリティポリシーを元に組織連携を考えると良い。それぞれの役割が何を行うかを明らかにすると自ずと連携方法も見えてくる。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "インシデント対応後の再発防止策がわからない。", "answer": "インシデントの直接の原因の特定と、対応における課題点を抽出すると良い。インシデントの振り返りを行い、技術的にできていたこと、できなかったことを振り返ったり、技術によらない場面での辛かったことや課題に感じた点（心理面含む）もあわせて振り返ると改善策が見えてくる。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "新しいセキュリティツールの導入検討している", "answer": "まずは現状のツールで何ができるかを確認すべき。もしかしたら現状のツールで賄えているかもしれない。設定値やフィルタルールなどもあわせて見直すと良い。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "パスワードポリシーはどんなものが好ましい？", "answer": "JPCERT/CCは安全なパスワードの条件を以下のように紹介している\n・パスワードの文字列は、長めにする（12文字以上を推奨）\n・インターネットサービスで利用できる様々な文字種（大小英字、数字、記号）を組み合わせると、より強固になる\n・推測されやすい単語、生年月日、数字、キーボードの配列順などの単純な文字の並びやログインIDは避ける\n・他のサービスで使用しているパスワードは使用しない", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "CSIRT構築って何からすればいいの？", "answer": "組織によって「守るべき資産」「組織体制」「事業内容」は異なるため、組織によって何を行うべきなのかは異なります。\nまずは組織のことをきちんと把握するというところが重要になるかと思います。組織にとって重要な情報資産は何なのか、またそれらはどこに格納されているのか、それらを守るためには何が必要となるのかを考えるところから始めるのが良いかと思います。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "EDR入れていればセキュリティは心配ない？", "answer": "残念ながら、「これだけ入れておけば心配ない！」というソリューションはありません。\nあくまでもツールは何らかの困りごとを解決するための手段でしかありません。\n正しく運用されていない場合は効果を発揮しませんし、導入する目的が明確に定まっていなければそもそも導入する必要すら存在しない可能性もあります。\n安易な機器の導入は運用負担を増大させるだけで効果を伴わない可能性があるため、\n「攻撃ベクター」「守りたい資産」「想定されるリスク」などを洗い出して、最大限効果を発揮できるようなツールを選定することが重要です。\nEDRが得意とする領域は、エンドポイント端末の挙動監視による脅威の検出です。\nエンドポイントの行動をログとして収集し、そのログを解析することで不審なアクティビティやプログラムを検出します。\n勘違いされがちですが、EDRを導入したからといって、あらゆる攻撃やマルウェアからエンドポイントを守ることはできません。\nむしろ、EDRの守備範囲は侵入を許してしまった攻撃やマルウェアを早急に検出して封じ込めるという部分になります。\nまたEDRはその特性上誤検知が多く、導入時のチューニングや継続的な監視、検出された脅威情報の分析を実施する必要があり、運用には労力がかかります。\n潤沢な予算があればMSSという形で外部のセキュリティベンダーに運用を依頼することもできますが、それなりにコストがかかってしまうのが実情です。\nまた、似たような製品としてEPP（Endpoint Protection Platform）というものがありますが、\nこれはパターンマッチングなどによってマルウェアなどの脅威がエンドポイント端末に侵入することを未然に防ぐことを目的としています。\n理想としては、EPPによって侵入を未然に防ぐ＋EPPで防げなかった脅威をEDRで検知してハンドリングするといった運用が好ましいです。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "自社のサイトをコピーしたフィッシングサイトを見つけたのですが、どうしたらいいですか？", "answer": null, "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "環境をクラウドへ移行する際に、セキュリティ上で検討するべきことは何がありますか？", "answer": "利用者責任範囲での担保方法、現環境でのセキュリティ機能の置き換え可否、クラウドで備わっているセキュリティ機能の利用可否などを検討すべき。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "クラウド環境はセキュリティが担保されている認識でよいでしょうか？", "answer": "ユーザー側で担保すべきことは残るので、利用するサービス形態（SaaS,PaaS,IaaS）ごとの責任分界を理解し、責任範囲のなかでのセキュリティ担保を行う必要がある。もちろん、契約前にクラウド自体のセキュリティレベルも要確認。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "ユーザー側でクラウド事業者のセキュリティ状況はどのように確認すればよいでしょうか？", "answer": "事業者が提供しているホワイトペーパー、クラウドが取得している公的認証、非公開情報ならばサポート窓口への確認をとるなどで確認する。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "クラウドサービスを利用する際に遵守するべきセキュリティ基準は何がありますか？", "answer": "ISO27017、IPAや総務省などの公的機関が出している利用手引きが参考となる。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "申請なしにクラウドを利用している事例があったが、今後はどのように対策すべきでしょうか？", "answer": "CASBなどの接続ブローカー製品の導入、IPなどによる接続元制限による技術的対策のほか、社内教育や申請フローの徹底と浸透、運用との整合性などの組織と人的な対策を講じるべき。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "クラウド上に機密情報を置いてもよいでしょうか？", "answer": "機密情報の種類（高換金性情報なのか、特定機微個人情報なのか）や、格納先のセキュリティレベルで判断が異なる。\n社内環境や法令で定められているレベルと、同等のセキュリティ担保ができるならばOKだが、一部不足ありなどで不安ならばNGとしたほうが安心。クラウド側責任範囲のセキュリティ状況も要確認。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "ZoomなどのWeb会議ツールを選定する際のポイントはなんでしょうか？", "answer": "IPAが公開している手引き（ポイント）が参考となる。データの格納先、参加時のロビー機能、録音データの格納先、関係法令、運営主体などなど。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "テレワークを実施しているがリスクの洗い出しはどのように実施すればよいでしょうか？", "answer": "ある程度ポイントを絞るならば、総務省が出しているテレワークガイドに沿って確認するなど。ほか、フラットに洗い出すならば、テレワークのシステム構成図などの棚卸情報から確認（他システムのリスクアセスメントと同様）することも可能。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "テレワーク環境ならではの特徴的な脅威、リスクはありますか？", "answer": "固有の脅威やリスクはない。ただし、自宅WiFiや外部との接続地点（VPN機器）への攻撃は多い傾向。\nまた、公共施設（カフェやレンタルオフィス）においては、のぞき見や聞き耳、忘れ物などのリスクが高く、自宅環境でも家族や業者の立ち入りによるのぞき見や聞き耳などのリスクはある。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "現状の社内基準にてテレワークに対応可能か確認したいが、どうすればよいでしょうか？", "answer": "総務省のテレワークガイドに沿って基準の適合状況を確認、不足箇所や不透明箇所の洗い出しのほか、実運用の状況を確認することで、実態と基準のズレの有無を確認すべき。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "グループ会社のセキュリティレベルがバラバラですが、統制はどのようにかければよいでしょうか？", "answer": "アセスメント→ガバナンス検討が流れの一つ。\nシステムや情報の重要度などから、範囲、対象、項目を絞ったうえで統一的なアセスメントを実施、その活動を通じて、グループ会社として連帯強化しつつガバナンス体制や風土を醸成していくなど。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "グループ会社のセキュリティ状況を確認したいのですが、効率的な方法はありますか？", "answer": "業種、風土（資本関係による統制や力関係の整理）、規模などから、事前にグループ会社を仕分けしたうえで、さらに高リスクなシステムに対してチェックすることでおおよその傾向をつかむ。なお、調査様式としてExcelは要検討（回収、分析がしやすい方式がベター）", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "海外子会社のセキュリティ状況に不安がありますが、何をどのように確認するべきでしょうか？", "answer": "すでに海外会社用の基準があるならば、その順守状況を確認（現地確認、現地インタビュー、現地証跡などで担保）、存在しないならば、公的規約（NISTなど）でざっくり現状のセキュリティレベルを確認しつつ、現地法令や本社基準を考慮したうえでのルール策定を行い、そことの差分を把握してく。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "設定項目レベルでのセキュリティ推奨はありますでしょうか？", "answer": "CISベンチマークなどを利用、ほか公式のホワイトペーパー、存在しない場合は個別確認（設定項目の意味を把握してISOやNISTなどのセキュリティ基準と紐づけして整理するなど）", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "予算上、一部ソフトでは管理者権限をみんなで共有していますが、セキュリティ上で対策すべきことはなんでしょうか？", "answer": "作業台帳などで個人判別できるように管理する（時間や場所、単独作業は禁止など）、作業申請との紐づけ（管理者ユーザは使わず、申請許可時に対して管理者権限のあるユーザを都度貸与するなど）", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "リスクは把握できていますが、着手する優先度はどのように決めればよいでしょうか？", "answer": "資産価値が高いもの、脅威度合いが高いものなど、リスク値が高いものから優先着手していく。なおインシデントや予兆、社会的に流行するものなどは、差し込みで着手することも考えられる。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "リスクアセスメントは「定期的に実施」とされていますが、具体的な頻度やタイミングはどのようなものでしょうか。", "answer": "公的組織からの総括が更新される頻度である、1年に1回、もしくは大規模なシステム変更に合わせて実施していくのが妥当と思われる。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "対策優先度を検討したいのですが、今流行っている脅威はどのように把握すればよいでしょうか？", "answer": "IPA10大脅威、OWASP top10など、各ベンダーが年次で発表しているものが参考となる。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "セキュリティ対策がそのまま実施できない場合の「代替策」は何に気を付けて検討すればよいでしょうか？", "answer": "セキュリティ対策のポイント（どの問題の何を対策したいのか）を外さないように置き換える。解釈にブレが生じるので、本当に代替策となっているかは、現場や識者を交えてよく確認する必要がある。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "内部不正はどのように対策すればよいでしょうか？", "answer": "意図的なのか、偶発的なのかでも異なる　意図的な場合は不正のトライアングルなども参考とし、技術的対策のほか、組織や人といった観点での対策も講じる。罰則の厳格な適用と運用、アクセス権限のタイムリーなコントロール、法的な拘束なども検討していく。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "業務都合上、一部でUSBメモリの利用を許可していますが、安全なUSBメモリはなにがありますか？", "answer": "保存データの暗号化、開封パスワードの要求、パスワードミスでデータ消去のものなどがある。ただし、その場合でも業務見直しなどでUSBの段階廃止は検討するべき。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "国内で使用している機器が海外子会社では法令規制で使えないのですが、どうすればよいでしょうか？", "answer": "必要な機能を洗い出し、海外導入可能で要件を満たすものを探す。そのうえでベンダーから説明をうけて判断するべき。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "ソフトの対応上、一部で古いOSを利用しているが、インターネットには接続していないので安全と考えてよいでしょうか？", "answer": "なんらかのNWに接続しているならば、安全性は低下していると考えられる。また、外部媒体の利用状況にも左右されるので、アセスメントは必要。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "各機器でログは取っていますが、これを活用していくには今後なにをすればよいでしょうか？", "answer": "SIEM利用などでのログの集約化、ログの分析、ログからの予兆検出が考えられる。また、取得しているログが必要十分かの判断もあるので、PTやフォレンジック訓練を通じてログ取得の目的到達度合いを測ることも有用と思われる。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "古い機器が残存していますが、近いうちに撤去予定なので対策は不要と考えてよいでしょうか？", "answer": "古い機器のNW接続はリスクあり、電源ON放置もリスクあり、残存するならば電源OFFのうえ施錠できる場所が望ましい、残存データがあるならば削除は早めすべき。ただし、この資産重要度によって手当のレベルは異なる。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "OSSを利用する際に注意すべきことはなにがありますでしょうか？", "answer": "サポートの有無、マルコード混入の有無、利用規約の熟読などが必要。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "プライベートクラウド環境を構築する際に考慮すべきセキュリティは何がありますでしょうか？", "answer": "HWセキュリティ、仮想マシンのセキュリティ、NWセキュリティ、ハイパーバイザのセキュリティなど、システム構成要素とその接続において考慮が必要。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "工場での機器、設備のセキュリティはどのように担保すればよいでしょうか？", "answer": "OTセキュリティ規約、ベンダーによるセキュリティサポート、ソフトウェアの定期更新、NW分離、アクセス制御などが考えられる。このほか、組織や人的セキュリティも従業員や管理者のレベルに応じて調整が必要となる。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "機器、製品を物理的に配送、移動する際に考慮すべきことはなんでしょうか？", "answer": "セキュリティ配送業者の利用、格納データ暗号化、物理ロック、移送前、移送中、移送後の差分確認などが考えられる。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "ベンダーへフォレンジック調査を依頼する場合、どのような情報が必要となりますか？", "answer": "発生している事象にもよるが、基本的には\"持っている情報全て\"が必要となる。\n特に聞く機会が多い項目については以下の通り。\n・発生した事象についての情報（「いつ」「何が」「どこで（誰の端末で）」「どのように」発生したのか）\n・調査対象端末の種類（サーバー、ラップトップPC、デスクトップPC、モバイル端末などのいずれに該当するのか）\n・調査対象端末の情報（OSのバージョン、端末の型番、積んでいるストレージの種類、容量など）\n・調査対象端末の暗号化の有無（BitLockerやFileVault2など）\n・ネットワークやシステムの構成がわかる図\n・AD等の端末管理の有無\n・アンチウイルス、EDRの有無\n・アカウント権限の管理方法\n・業務の流れ（データフロー）\n・取得しているログの保管期間", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "ベンダーへフォレンジック調査を依頼する場合、どのように機器を選定すればいいですか？", "answer": null, "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "インシデントに備えて、ログはどこまで確保するのが望ましい？", "answer": "\"自組織において発生しうる脅威を後追いで調査できるログ\"を全て取得することが望ましい。\nそのため、どのようなログが必要となるかについてはまず自組織のネットワーク図やシステムの構成図より脅威を洗い出す必要がある。洗い出された脅威について、その経路上に存在する機器（プロキシ、FW、スイッチ、端末など）のログが調査に必要なログとなる。\nまた、可能であればログの保管期間は1年以上とすることが望ましい。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "CSIRTとPSIRTの棲み分けはどのようにするのが望ましいか", "answer": null, "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "インシデントが発生した際、プレスリリースにはどのような内容を記載することが望ましいか", "answer": "発生したインシデントの種別にもよるが、\n・発生日時を起点とするタイムライン\n・発生を確認した時点の時間情報\n・行った作業及び対応内容\n・流出の恐れがある情報\n・判明している事象\n・発生原因\n・今後の対応\n・被害にあわれた方への案内\n・問い合わせ窓口\nを記載することが望ましい。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "CSIRTを構築するにあたって、必要となるドキュメントにはどのようなものがあるか", "answer": "セキュリティ施策を進めていく上では下記のようなドキュメントが最低限必要となる。\n未整備である場合にはまずは整備するところから始めることが望ましい。\n・情報セキュリティポリシー\n・社内規定（特に情報システムに関するところ）\n・情報資産管理台帳\n・社内のネットワーク図\n・社内システムの構成図", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "脆弱性チェックツールはどのようなものがよく使われていますか？（できれば無償で使えるもの）", "answer": "コンテナのスキャンということであれば、下記のような商用ツールをご利用いただくのが理想的でございます。\n・Prisma cloud\n\nhttps://www.paloaltonetworks.jp/prisma/cloud\n\n・Aqua\n\nhttps://www.creationline.com/aqua\n\nまた、環境がパブリッククラウドということであれば、OrcaはEC2等の内部の脆弱性スキャンも実施可能となります。\n・Orca\n\nhttps://gmo-cybersecurity.com/service/orca-security/\n\n今回はご予算のご都合があるとのことですので、無償で利用することができるツールにつきましても紹介させていただきます。\n・Dockle\nコンテナ環境の設定をCIS Benchmarkなどに基づいてチェックするツールです。\n\nhttps://github.com/goodwithtech/dockle\n\n・Trivy\nコンテナ内部のライブラリパッケージ等が脆弱性を含んでいるかをチェックするツールです。\n\nhttps://github.com/aquasecurity/trivy\n\nOSSでの対応ということであれば、DockleとTrivyの併用がよろしいかと思います。\nユニットテストに関しましては、SAST/DASTなどを使用するのが一般的でございます。\n（無償で使用できるものをいくつかあげさせていただきます）\n・Github Codescanning\n\nhttps://github.co.jp/features/security\n\n・OWASP Benchmark\n\nhttps://owasp.org/www-project-benchmark/\n", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "採用予定が1名となる場合、どういった人材を優先すべきか。", "answer": "インシデントレスポンスの対応可能な人材を優先的に検討することが推奨されると考えている。\nインシデント発生頻度とインシデントレスポンダーとしての稼働率の懸念から、組織・開発面のセキュリティに関してある程度汎用的にレビューできる能力を持つ人材が望ましい。\nまた、採用者に対する権限・指揮系統を事前定義しておくことが望ましく、広報等の他部署との連携がとれる人材が推奨されると考えている。場合によっては、役員クラスのインシデントマネージャーを配置するなど、インシデント対応時の意思決定について強い権限を持たせることが重要となる。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "インシデントレスポンダーとしての経歴はどういったものが望ましいか", "answer": "1度でもインシデント対応の経験がある人材が望ましい。また、これに加えてクラウドに関する知見を持っていることも推奨される。例：AWS関連スキルを持っていて、AWS上の対応（停止すべきサービスなど）について判断ができる。尚、あくまでセキュリティ上の問題特定・判断・社内連携ができる人材が重要で、フォレンジック作業などのスキルについては外部利用でフォローすればよいと思われる。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "インシデントレスポンスに関する教育などは可能か。", "answer": "（アドバイザリー契約の場合）年間継続時においては、工数調整を通して対応範囲内として含めることも可能と考えている。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "採用対象の想定年収レンジはどの程度か。", "answer": "セキュリティ分野を広くカバーできる人材だと800～1200万程度のレンジになる見込み。\n※インシデントレスポンスのみなど1分野程度のみカバーできる人材だと600万～が目安となる想定。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "セキュリティ領域での副業は一般的か。", "answer": "実務的な副業は少ない印象。委員や発起人、講師などのスポットを掛け持ちする人はいる。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "S3にユーザーがファイルアップロードできるサービスを提供しているがウイルスチェックの観点では何が必要か", "answer": "広範囲になりがちなので、優先度をつけて考えるのが良い。例えば、ユーザに対して簡単に害を与えうるもの≒DL後ワンクリックで起動するものへの対処などで内容について大枠を決めた上で施策を考えていくなど。※アビューズについては一旦切り離して考えるのも一例。\n単純にチェック強化であればSandboxなどファイルの実行過程、結果を見るサービスが推奨できる。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "インシデントレスポンダーを前提にしたいと考えているが、ジェネラル人材（組織作りの要素）やプロダクト設計のできる人材も必要ではないか。", "answer": "プロダクト設計までできる人材は難しいと思われる。ジェネラルで採用し、そこを起点にリファラル採用を狙う（ツテを頼る）方が現実的かもしれない。オペレーションとアーキテクチャを担当できる人材をまずは採る方が望ましい。まずは状況整理できる人材が優先的に必要となる認識。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "スタートアップより年数ある企業がこういった条件提示で求人かけた場合、どの程度集まってきそうか。", "answer": "応募自体は来ると思うが、適切な人材か見抜くのが難しい。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "適切な人材を見抜くための質疑例などはあるか。", "answer": "IR（インシデントレスポンス）対応の詳細質問を当てた時の反応が重要であり、IR対応経験を語ってもらうことが有効と思われる。例えば、以下の定型的なIRの流れにおいて、「どのフェーズでどう対応したか、何がどう難しかったか、何をどうすると上手くいったか」などのヒアリングが考えられる。\n（IRの流れ：インシデント発生・発覚⇒初動対応⇒原因特定⇒被害範囲の究明⇒収束）", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "セキュリティエンジニアのスキルセットの確認においてどういったことを問いかけるべきか。", "answer": "他部門・外部組織などとの連携についての具体的経験やコミュニケーション実績を聞くことを推奨。\n関係各所とのコミュニケーションや、風評リスクの想定までできるスキルも求められる。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "セキュリティ担当として素直に全部語られると考えるものがあるのですが（機密範囲なので）語っていただく上で前提で何か用意した方が良いものはありますか？", "answer": "インシデント自体の詳細は回答してもらわなくてよい。大まかなインシデント種別（マルウェア、不正アクセスなど）と流れが把握できればよい。イエラエではフォレンジック人材の採用する際、ヒアリング時に企業名などの固有名詞を出してしまう人は除外している。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "実務経験・学習経験を聞く上で、セキュリティ学習PF（ハックザボックスなど）はやっていて当たり前なくらいが基準になるか", "answer": "そういった学習経験はあまり参考にならない可能性が高いと思われるが、SANSのGIACという資格のホルダーは有効な指標の1つとなると考えられる。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "足きり用にWeb試験などは有効か", "answer": "特に必要ない認識。プログラミングスキルは、IR実務担当にとってはあまり影響しない可能性が高い。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "面接官はどういった属性の人がやるべきか", "answer": "NW知識がある人が担当した方が望ましい。技術以外の要素を見る上では一般の人事担当などによる面接も必要", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "技術以外の要素としてはどういった点を見るのか（信頼性など）", "answer": "他部署への協力要請も多いので、連携力が特に重要。（経営陣・広報・インフラ担当・外部組織との折衝など）インシデントレスポンダーとしての素養の優先度としては、ヒューマンスキル＞技術力と考える。（技術力はある程度外部を頼ることができる。）ルール策定などで現場を考慮して進めることができないと内部に軋轢が生じる恐れがあり、正論だけでは進められないことを前提とした調整力が重要になる。\nまた、守れないルールの策定は内部不正の温床にもなり得る。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "FortiSandboxについて、これはサーバにUPされている範囲をこのSandboxでチェックするのか。", "answer": "S3上にあるものを参照してチェックする流れになると思われる。（常時マウントかは未確認）\nストレージゲートウェイを用いてSandbox(VM)にS3バケットをマウント、バケット内のデータをチェックすると読み取れる", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "FortiSandboxの契約方法はどうなっているか。", "answer": "契約方法は未確認だが、AWSマーケット上に固まったマシンイメージとして販売されており、実際のデプロイはAWS上でイメージを選択して構築すれば立ち上がるものと思われる。\nAMIはライセンスを含んでおり、仮想マシンの従量課金のみで別途に契約は不要な印象。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "外部URL遷移時の警告ページが必要な気がするが、遷移先のリスク判断を含め、これを解決できるSaaSは何かないか。", "answer": "把握している範囲だと無い認識。自社の責任範囲を明示するという目的においては、遷移先の危険性はあまり重要でなく、「ここから先は自社の責任ではない」ということを伝える意味で、自社内のコンテンツかどうかを切り分けることが重要と考える。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "クラウドの外部アクセス系の事故について\nS3への外部からのアクセスや、管理APIを叩くことが可能できるということをどのように突き止める？", "answer": "・先にIAMアカウントが抜かれて、そのIAMアカウントから存在するリソースのリストを手に入れる\n・コードが漏洩して見える\n・WebアプリのENVに入っている\nなどのパターンがある", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "フル診断でS3の設定を見て、機微な情報が入っているコンポーネントが\nパブリックアクセス可能とかはリスクレベルが高い指摘事項になるが、具体的に誰がどう見つける？", "answer": "何かのはずみでたまたま見つかることもあれば、チェックして回って（クローリングして）いる人もいるかもしれない。方法はいろいろあるが、インターネットの定石として\"公開になっていればいずれ見つかる\"というものがある。\n（非公開情報のページをアップだけしておいて後からリンクを張ろう⇒リンク張る前に見つかってしまう、等）\nなので診断のレベルでは\"家の玄関に鍵がかかってないから危ない\"という所で止めるのが正解", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "現在、セキュリティ事故が発生した場合に備え損害保険の加入を検討しています。\nWebで検索すると損保ジャパンのサイバー保険や東京海上のサイバーリスク保険がヒットします。\n過剰な保険や要件を満たさない保険に加入することがないよう、\n保険の種類や加入に際しての留意点をアドバイスいただけますか？", "answer": "３年ほど前に調べた内容なので、現在だと若干異なっている部分があるかもしれませんがご容赦ください。（大きく建付けは変わっていないと思います）また、保健の募集人資格を取得していないため、「この保険がお勧めです」という事は言えませんので、そちらもご容赦ください。サイバー保険の大枠としては、まず損害保険として\"サイバー事故の結果として他者から請求される損害をカバー\"するものとなります。そして、そこに付帯するものとして、\"事故対応に対する備え\"が付いている形になっています。（ですので、主たる損害保険の限度額と事故対応に対する限度額の間には大きく差が出てきます）そういった背景から、この\"事故対応に対する備え\"の部分は各社かなりの差異がある印象です。\n事故対応の補填として挙げられるものとして\n・事故調査を外部に委託した費用\n・内部で調査稼働した人員の残業代、宿泊費等\n・コールセンターを設置した場合の費用\n・ユーザーへのお詫び費用（クオカード購入や送付のコスト等）\n・正常な状態に戻す費用（修理費等）\n等がありますが、各保険の守備範囲はまちまちになりますので、確認の必要があります。\n（国内では取り扱いが無いと思いますが、最近ではランサムウェアでの攻撃に対し身代金を払う保険も存在しています）また、これらの補填の支払い条件も保険商品によって違いが大きく違いが出てくる部分です。一般に向けての公表が必要であるとか、警察への被害届が必要であるといった条件が付されていると思いますので個別で確認が必要ですが、その中でも２点、注目すべきポイントがあります。まず一つ目は、事故の\"疑い\"で調査を行った場合、その費用がでるのか？　という点です。実際に事故が起こっている事が分かっていれば問題ないですが、事故の疑いがあるという状態で調査を開始することもあると思います。\nその場合\n・もし事故は起こっていなかったという結果となった時にその費用が出ないのか／出るのか／一部出るのか。\n・出る場合はその前提条件は何か。\nという点が論点となりますので、そこまでのカバーを求める場合は事前に確認しておく必要があります。\n二つ目は、外部からの攻撃ではなく内的要因（内部不正）に対する調査を行った場合に費用が出るのか？　という点です。\nこちら出るといった場合でも、前述の\"一般に向けての公表\"や\"警察への被害届\"といった支払い条件があった場合、実質行使できない事になりえます。\n御社の環境ではメインの損害保険の部分で\"サイバー攻撃を発端とした工場停止時の損害\"をカバーするかしないかで条件等大きく変わる事が想定されますので、事故対応の部分のみを見ての選定にはならないとは思いますが、以上が\"事故対応\"の観点になります。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "「AiTMフィッシング」への対応はどのようにすべきか？", "answer": "認証手段としてはFIDO2（WebAuthn）を採用するのが現実的だと思われます。\nOTPを前提とした上で、AiTMフィッシングにある程度耐性を持たせたいといった場合は以下のような方法が考えられますが、いずれも効果は限定的です。\n・SMS認証を、モバイルブラウザ等での利用であれば、 \nhttps://web.dev/sms-otp-form/\n やWeb OTPなどを利用することで、ユーザーに入力させること自体を避ける。\n⇒SMS中にログインに利用するサイトのURLを含んでおり、補完先のサイトは正規サイトに限定される\n⇒但しユーザーが手で打ち込んでしまえば意味がないので、限定的\n・認証を実施している位置情報をIPアドレス等から導出し、OTPの送信経路上に表記することでユーザーが違和感を感じやすくする。\n⇒VPNとか使ってると意味がない\n・複数IPからの複数セッション数制限、等はある程度効果あり\n⇒限定的で回避も容易なうえ、副作用があるので推奨はしにくい", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "『ユーザー本人が正規に実施したログイン試行の中で発行されたワンタームトークンを別ユーザーが窃取して利用する』という攻撃シナリオの対応はどのようにすべきか？", "answer": "「正規のログイン試行」と「トークンの利用」の同一性をどのようにチェックするか？　という点になると思いますが、既に候補として挙げていただいている「IPアドレスの同一性」、「セッションの連続性（セッション識別子の同一性）」以外には、時間の連続性（コードの利用期限）が考えられます。\nが、OTPの利用である以上既に考慮済みではないかと思われます。\nコードの利用期限をさらに短縮（攻撃可能機会の減少）するという意味では、利用済みコードの無効化（ログインに成功したら利用したコードは無効化する／できれば失敗時にも無効化したい所ですが、逆にＤｏＳの脆弱性にもなりうるのでハンドリングが難しい）も追加してもいいかもしれません。\n他には場所の同一性（GPSデータの同一性）もありうるかもしれないですが、「ワンタームトークンの窃取方法」を想定すると効果は限定的、且つ、これだけの為にGPSの権限を持たせるのもリスクが高そうです。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "セキュリティエンジニアを募集する際の\nジョブディスクリプションはどのようなものか？", "answer": "以下、インシデントレスポンダー（開発/組織面でも対応可能な方の募集例）\nセキュリティエンジニア\n■業務内容\n・インシデントレスポンス対応\n・（平時）システム開発におけるセキュリティの社内QA対応\n■応募資格/経験\n・インシデントレスポンスの経験が1回以上ある方\n・AWSでのシステム開発経験\n・経営層および関係部署とのコミュニケーションスキル\n■歓迎スキル/経験\n・インフラ運用の経験（24/365システムにおける障害対応経験など）\n・システム開発におけるセキュリティ要件定義/設計/レビューの経験\n・CISSPなどのセキュリティ関連資格\n", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "ユーザーに対して簡単に害するもの（1クリックで起動するようなもの）への対策はどのようにすべきか？", "answer": "サンドボックス製品（※）による追加チェックをお勧めいたします。\n仮想的な環境で実際にファイルを実行し、そのふるまいをチェックするというもので、従来のウイルスチェックで見逃したものも検知できる可能性が高まります。\nユーザーに対して簡単に害するもの（1クリックで起動するようなもの）については、実際の挙動を見るという、この方式でのチェックが効果的です。\n（※）サンドボックス製品について、弊社では以下の支援が可能です。\n・製品検討の際のポイント整理\n・運用に係る関連ドキュメントの作成", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "ユーザーからアップロードされるファイルについて、すでにウイルスチェックは導入しており、追加でサンドボックスなどを導入せずにファイルチェックを厳格化する場合の推奨はなにか？", "answer": "以下2点をお勧めいたします。\n・アップロードファイルの制限\n危険性の高い「実行形式の拡張子（.exeや.batなど）」の制限をお勧めいたします。1クリックで起動するものへの対策として効果的です。\n・ダウンロード前の再チェック\nファイルがダウンロードされる直前に再チェックすることをお勧めいたします。\n更新されたパターンファイルにてウイルスチェックすることで、アップロード時では見逃されたウイルスが検知できるようになる可能性があります。\nファイルがアップロードされた後、ダウンロードされるまでに期間が空いた場合などで効果的です。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "サービスの提供先（ユーザー企業）から「セキュリティ監査」の実施状態を確認されるケースがあるが、「セキュリティ監査」は外部委託するべきものなのか？", "answer": "セキュリティ体制が確立するまでは、弊社のような外部業者をご利用いただくことをお勧めいたします。\n監査形態（自社による内部監査、他社による外部監査）の指定がない場合は、将来的には自身にてご対応いただくほうが、セキュリティ組織としての成熟度を高める意味でも望ましいと存じます。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "個人情報を含むDBアクセス制限を一般的にはどのようにすべきか", "answer": "セキュア開発での共通認識として「開発環境での本番データ利用はNG」がございます。\nダミーデータでの開発を前提とする原則です。\nこの原則を踏まえ、どうしても開発にて本番データ（個人情報）へのアクセスが必要となる箇所は、「リスクの緩和」もしくは「インシデントの早期の検知」を目的とした手当（権限者の限定、ログ監視など）を検討することをお勧めいたします。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "DBへのアクセス追跡の優先度は高いと思うが、どういった方法があるか。", "answer": "ある程度の踏み台サーバ・経路を限定してこの経路上で通信があるのか、だれが使ったのかを追う形で、クエリまでは取得するのが一般的である。\nリスク分析したうえで、これはアクセスがまずいと判断しているならば、即時通報も場合によっては視野に入る。（ただしリアルタイム検知は運用面で非現実的となる場合が多い）\n抑止効果などの観点から、ログ取得・抽出・調査ができる状態を構築・周知していくことが重要。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "どの段階・何を契機にセキュリティ組織は発足するケースが多いのか。", "answer": "一番多いのがインシデント発生後。\n早ければ早いほど良いというのは勿論であり、結局はセキュリティの問題は経営者の責任になる。\nセブンペイなどの実例から、記者会見での経営者の認識不足はまずいという風潮もある。\nセキュリティをおろそかにすると、会社としてのビジョンも守れないので、そこは能動的にやりたいという想いが発足のベースとなる。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "社内向けシステムに個人情報を置くことを許可したいが何を考慮すべきか", "answer": "既存の社内ポリシー、ガイドラインとの整合性を確認し、遵守すべき事項を洗い出す。\nそのうえで、ユーザー側で守るべきこと、システム提供側で守れていることを明示することで、\nそれぞれの責任範囲がどこまでであるかを明確にすべき。\n個人情報の取り扱いについての具体的な指針がない場合は、公的ガイドラインを参考とし、\n遵守事項として落とし込んだうえで、ユーザー側、システム側での分担ラインの検討に進めていくことをお勧めする。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "複数のルール、ガイドラインが乱立しているがどう整理すべきか", "answer": "まずは文書を漏れなく一覧化することから始める。部署ごとに独自文書を策定しているケースも多いので、統制部門だけでなく、開発や営業を含む実働部門からもメンバーを集めたうえで、既存状態の把握を進めていくべきと考える。\nそのうで、ポリシー、ガイドライン、マニュアル程度で各文書の属性を見極めていき、内容や順序性、想定読者、プロセスでの参照タイミングなどを考慮して、従属関係や統合に向けたグループ分けをしていく。会社の大本のポリシーからも、完全に独立した文書は逸脱や更新漏れの原因にもなるので、そのような立場の文書は廃止、統合、または大本ポリシーに新項目を追加、そこから派生させることが好ましい。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "親会社からセキュリティ対応指示が来ているが、セキュリティが分かる人材がいない、\n雇用をする余裕もないがどうすればいいのか", "answer": "当面は外部ベンダーでも良いので、相談先を確保することをお勧めする。\nそのうえで詳細内容は分からずとも、大まかにどのような課題があって、何を進めていくのかを、\n定例などで意思疎通をし、まったくの丸投げという事態は避けるべきと考える。\nセキュリティが分からずとも、システム構成は把握できていたり、保守ベンダーとの付き合いがある場合も多いので、このあたりの情報整理だけでも自力でできるか否かの見極めもできると良い。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "システム管理のほか、セキュリティ管理も担当1人で行っているが問題はあるか", "answer": "近日での離任や退職予定などがなければ、今すぐの問題はないと思われる。\nしかし、病気などの突発事象での離脱はありえるので、業務をマニュアル化していない場合は、\n少しずつでも、業務内容の整理と可視化をすることをお勧めする。\nまた、上司に相談のうえ、この整理やマニュアル作成は他担当者とともに実施し、分担することで、後進の育成や組織自体の強化にもつながるので、前向きに検討いただくと良い。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "仮想マシンのスナップショットはバックアップと考えてよいか", "answer": "バックアップと同一視はできない。スナップショットは性質上、変更点のみの保存となるので、\n大本の構成ファイルの破損などの復旧には利用ができないケースが多い。\nスナップショットは開発作業時の一時的な切り戻し程度の利用として、バックアップは別途に要件を整理、設計を行うことをお勧めする。\n例としては、停止状態で仮想マシンの構成ファイルを丸ごと取得し、別のストレージに保管する、\n仮想マシン自体にバックアップソフトウェアを導入し、その機能を使って通常の物理マシンと同じく\nシステムバックアップをしていくなどがある。\nいずれもベンダーごとに推奨方法や対応製品が異なるので検証は必須と考える。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "ベンダーから貸与された端末、回線を使ってそのベンダーのクラウドサービスを利用しているが、どのようなリスクが考えられるか", "answer": "まずは、リスク特定のため、社内チェックシートがあればそれを用いてアセスメントするべきと考える。\nまた、ベンダーに対してチェックシートの遵守状況を問い合わせることをお勧めする。\n一般的な構成例と同じく、端末、回線、クラウド、それぞれにリスクが存在するが、いずれも貸与のみで、対策のための環境変更などができない場合は、利用継続するかはリスクとの見合いとなる。\n外資ベンダーの場合は個別調整ができない場合も多いので、この場合は盗難防止や移動防止、\n隔離部屋による操作制限など物理的な処置が主となるほか、台帳による利用者の管理や複数人での作業誤り防止が想定できる。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "高換金性情報を扱う際の考慮事項は何があるのか", "answer": "クレジットカード情報が該当する場合、PCIDSS準拠相当を目指すべきと考える。\n基本的なセキュリティ事項のほか、要員管理や委託先管理、開発環境を含む堅牢化などがある。\nまた、監視カメラの設置、入退室管理システムの導入を含む、高度な物理的処置も盛り込まれているので、最新の基準内容を確認し、作業居室や機器設置場所のレイアウトを含めた早期段階からの検討をお勧めする。運用プロセスとリソースの兼ね合いもあるので、ある程度の長期スケジュールを確保するべき。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "脆弱性診断を発注・運用しているが、今後どのように運用していくべきか", "answer": "一般論としてリリースルールを作って診断を実施していくことが多い。\n都度考えるのではなく、診断を実施する条件を定めていく。\n例：機能追加のタイミングで実施\n理想としては、シフトレフトの考え方でセキュリティ要件を定めていくことが望ましい。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "過去リリースしたサービスの診断はどのようにすべきか。", "answer": "インシデント発生時のダメージ規模に準じて優先度を付けることが一般的。\nサービス内で機微情報を扱うものは実施した方がよい。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "セキュリティチームを発足する際に考慮することは何があるか。", "answer": "チーム発足時には問い合わせの集中や指揮系統の混乱などでコストが想定よりも掛かる場合が多い。\nセキュリティチームの発足を経営メッセージとして宣言するほか、セキュリティポリシー内にチーム発足及び役割に関する文言を盛り込んだ方がよい。\n事故対応系の機能を持つセキュリティチームにおいては、社内認知の浸透、及び有事の際にスムーズな通報が実行されることが重要となる。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "セキュリティをプロジェクトに根付かせるにはどうすべきか", "answer": "例として、プロジェクト定義書の必須項目に追加する、またはプロジェクト承認フローにセキュリティ有識者を追加する、など、まずは多少の強制力をもって浸透させるということが考えられる。\nただし労力が追加される事実には変わりがないので、事前の説明会、QAの準備、記入ガイドなどを用意しつつ、セキュリティの意義やプロジェクト評価の一要素となり得るという制度面での理解を現場の方々に求めることをお勧めする。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "機器を持ち出し、在宅で作業することを認めているが、現物確認はどのようにすべきか", "answer": "シリアルナンバーなど識別できるものを事前に控えておき、それを映す形で画像の提出が考えられる。\n識別できるものがない場合は、一旦は引き取り、付与したうえで改めて在宅許可を出すことをお勧めする。過去写真の使いまわしなどで、正確な現状が把握できないことも想定できるので、定期的な持ち帰りを必須とするか、精度は低くなるが会議カメラで映してもらい確認することも想定できる。\nいずれの場合も、在宅時点で紛失や盗難リスクは高まるので、機器自体または機器に内蔵する情報が\n会社または契約上の高レベルの機密性であった場合は、在宅作業は不許可とするのが望ましい。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "保守回線でベンダーから接続がある場合は何を考慮すべきか", "answer": "まずは、保守回線がどのようなものかを明確にすべき。インターネットを介さない専用線であれば、\n一定のセキュリティは担保できるが、それでもベンダー作業員の作業に起因するセキュリティリスクはあるので、作業内容、作業時間、作業体制、作業記録、作業契約などの関連要素をチェックすることをお勧めする。\n回線を独自に引くような専用回線でない場合、保守回線はインターネットを経由した接続と想定できるので、インターネット接続と同様に考えるべきであり、とくにVPN機器は攻撃を受けるケースが増加しているので、ここは重点的な確認が必要となる。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "VLAN（無線LAN・有線LAN）への接続のために、ユーザー認証をかけたいと考えています。ただし、プリンターやIoT機器などの接続も想定されるため、Captivate Portal（Web認証）方式だけでなく、802.1x証明書などの方式も検討しています。\nただしMACアドレス認証についてはクライアントが詐称する可能性があるため、有効にしたくありません。セキュリティ強度がありつつも、Web認証ができない端末に対して、どのように安全にネットワーク接続を行うと良いか、アイデアを頂けませんでしょうか。", "answer": "有線/無線を問わず学内NWへのログイン時にインタラクティブな認証、あるいは802.1xの証明書認証を行うことで良いかと思います。基本的には教職員・学生の使用するPCを対象として、ログイン時にユーザと接続元端末の情報を収集し、Accountabilityの確保を行われるということで理解しました。\n前述の802.1x認証が可能なプリンター等の機器であれば、前述の認証方式による接続を対策として、対応していない（できない）機器があった場合、仰る通りMACアドレス偽装は不可能ではないので、VLAN認証をスルー（≒従来のスタティックVLAN）になりますが、少数の機器だけと言うことであれば、一つのソリューションだけではなく重層対策、例えば当該ポートの監視強化（通常より一段階Severityを上げるなど）や、用途に応じたVLANの定義の追加などを施し、最終的なリスク低減の備えることではいかがでしょうか。また、IoT機器については多くが無線でsimを挿した上でデータ収集を行うことも多いかと思いますので、研究等で必要ということであれば、学内は経由するものの学内に干渉しない独立したVLANを準備されることも一つの手かと考えます。\nプリンターに関しては、IDaaSというよりは不随するAzureライセンスの機能になりますが（採用した場合ですが）、M365の機能の一つにUniversal Printがあり、こういった機能を活用にするのも一つの手かと考えます。\n\nhttps://www.microsoft.com/ja-jp/microsoft-365/windows/universal-print\n\n", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "ログ収集を強化する件について現在検討を進めているのですが、具体的にどのような内容のログを、どの程度の期間を収集する必要があるかについて、アドバイスを頂く事はできませんでしょうか。もし教育機関や自治体での設計例等がありましたら紹介頂けませんでしょうか。", "answer": "・レコードに記載されるべき内容ですが、明確な基準としてはPCI DSSなどが参考になるかと思います。v4.0では要件10.2.2で定義されているのですが、ユーザID, イベント種別, 日付時刻, 成功失敗の指標, イベントの発生元, 影響のあるデータ/システムコンポーネント/リソース/サービスのIDや名称などになります。すべてのデバイス/ソフトウェアで満たすことはログサイズや仕様上難しい可能性はありますが、少なくとも守るべき対象に近いシステムや、侵入地点になりうる個所では取得を推奨します。\n・「教育情報セキュリティポリシーに関するガイドライン」では校務系システムでは6カ月以上保存が望ましいとありますのでこちらがまずは目安となるのではないかと思います。\n・参考までにPCI DSS要件10.5.1では1年以上のアーカイブ保存、3カ月以上の即時閲覧可能な状態を求めています。\n・取得に加え、定期的なログレビューを並行して実施する必要がありますが、疑わしい事象に関しては分析の上、対処の必要性に応じて対処する必要があります。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "MFAと併用する場合のパスワードポリシーについてはどう考えるべきか", "answer": "MFA前提において「パスワードに対するユーザの自由度」はある程度確保できると考えます。\nパスワード長はNIST（SP800-63B）でも「最小8文字」を推奨しています※。\n※MFA前提なし、ユーザで決める場合は最小8文字、システム側で生成する場合は最小6文字\nパスワードが強固であるかは、パスワードの長さに依るところが大きいとされていますので、\n「ユーザは自分のパスワードを好きなだけ長くする」ことの奨励はお勧めいたします。\nパスワード長の制限例としては「最大64文字」です。\n長いパスワードは「パスフレーズ」を活用する例が代表的です。活用にあたってはユーザにガイダンスを提供することが重要です。\nパスワード複雑性は「オプション（利用できる）」とすることをお勧めいたします。\nNIST（SP800-63B）では「すべての特殊文字（スペース含む）を許可（使用は必須としない）」を推奨しています。オプションとすることで、ユーザが覚えやすいパスワードを作成することができます。\n複雑性の要件を必須とした場合、例えばユーザが「password」をパスワードとしたいと考えたとき、\n要件が数字であれば「password1」、記号も要求されれば「password1!」など予測可能なものを選択する傾向にある、という研究結果が示されています。認証におけるMFA前提も考慮すると、複雑性の強制はしなくても良いと判断します。\nMFA前提においても、パスワードの使いまわしは原則的には避けるべきです。利用シーンや業務効率を勘案したうえで、ルール無視の風潮が広がらないことに配慮できるということであれば、使いまわしを許容することは現実的と考えます。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "位置情報はMFA要素に入るか否か", "answer": "厳密な定義に照らすと、MFAには当たらないと判断します。NIST（SP800-63B）では位置情報は認証要素とはみなしていません。GPSによる位置情報、リモート操作制限による直接的な物理アクセスの立証、を揃えたとしても、これらはリスク評価の指標にはなりますが、MFAの要素にはなりません。\nMFAには当たらないものの、物理的なアクセスを理由としてリスク評価をし、MFAを使う場合と同等のリスク度合と判断するならば、単一認証（パスワードのみ）との併用は許容できると考えます。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "「パスワードのみのローカル認証」における主な脅威と対策はどのようなものか", "answer": "リスクは以下と仮定します。\n「PCに対して物理的にアクセスする環境でパスワード認証が突破されるリスク」\nこの場合、想定脅威は以下に大別できます。\n１）パスワードに対するブルートフォース攻撃\n２）組織内で同じパスワードを持つローカルアカウントの悪用\n３）ソーシャルエンジニアリング攻撃\n脅威の発生見込みは、物理的な環境制約により攻撃の試行が困難なため１）と２）は「低」と判断します。３）は物理的なアプローチでも攻撃可能であるため「中」と判断します。メモ書きの盗み見、操作の盗撮などの手口がそれに該当します。\n対策は１）はパスワードに複雑性の要件を盛り込む、試行回数の上限を設けるなどです。２）はアカウント、パスワードの使いまわしをルールで禁じる、注意喚起を行うなどです。３）はクリアデスク、クリアスクリーン、外出先では覗き見対策シールを貼る、壁を背にして座る、離席時には携帯するなどです。\nPCを盗難したうえで攻撃を試行するケースもあり得ますので、MDMなどのセキュリティ製品を導入し、盗難に気付いた後にリモートワイプ機能で情報消去する、PCローカル上のデータ持ち出しを制限するなども併用することもお勧めいたします。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "脆弱性診断の網羅性・診断漏れの対策", "answer": "以下の点を抑えることをお勧めいたします。\n診断漏れへの対策（依頼時に対象から漏れる可能性への対策）\n・自社システムを漏れなく一覧化する\n・診断対象を選択するための基準を作り、それに沿って一覧から診断対象を選択する\n網羅性への対策\n・診断対象の構成要素を把握する（ベースは？⇒クラウド、利用形態は？⇒IaaS、用途は？⇒Webサーバなど）\n・構成要素に対応する診断を選択する（SaaS利用⇒クラウド診断、IaaS利用⇒プラットフォーム診断＋クラウド診断など）\n・（余力があれば）PT実施で実効性を測定（”攻撃者の目的達成を防げるか”という総合的な観点）", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "インボイス制度対応で扱う個人情報の取り扱いで注意するべきこと", "answer": "「顧客からの登録番号などの秘匿情報」は、個人情報保護法上の\"要配慮個人情報\"には該当しない※1ものの、ユーザー情報と紐づく情報※2に当たりますので、漏洩／露出した際のビジネスリスクは大きいものと推察します。\nインボイス制度固有の管理要件が追加される可能性もありますが、まずは、貴社内での「ユーザー個人情報に類するもの」と同等のレベルで運用いただくことで良いと存じます。\n本件を含め、重要情報（秘密情報）の運用について、見直される場合の一般的な観点を以下に記載いたします。\n・保管の観点\n保管場所、保管方法、情報保護、暗号化、バックアップ\n・アクセスの観点\n権限、経路\n・監査の観点\nログ（作成、削除、操作）\n※1 法の趣旨的には、要配慮個人情報は差別や偏見を助長する情報の意味合いが強いものであり、登録番号はこれに合致するものではないと判断\n※2 登録番号は「国税庁インボイス制度適格請求書発行事業者公表サイト」で住所氏名に変換可能", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "「Security.txt」を検討しているが、温度感などはどうか", "answer": "■業界温度\n　規格化されたことが共有される程度で、積極的な事例公開や推進活動はあまりない印象です\n■一般的な課題例\n１）迷惑メールの増加\n自動診断ツールの結果と共に報奨金を要求するメール、製品/サービスのセールスメールなどが大量に届く\n２）「Security.txt」の改ざん\nファイル自体/内容が改ざんされ、通報者が異なる報告先に誘導される可能性がある\n■所感\n・通報内容をトリアージできる体制と仕組みがマストと考えますので、それらが整ってからご検討いただくことをお勧めします。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}, {"question": "医療関係事業者ですが、優先順位が分かりません。", "answer": "まず対応すべきリスクの優先順位という観点で回答します。\n質問者様の所属しておられる医療業界としては、まずは人命が優先されると思います。\nですので、最優先は医療活動が停止してしまうリスクへの対応でしょう。\n遠隔手術の機器等があればそれらを守る必要がありますが、そういったものが無くても電子カルテ等、停止してしまうと医療活動が停止してしまうシステムが存在すると思います。\nそれら医療活動を遂行する為に必要なシステムを把握し、防御とリカバリの両軸で対応の検討をしてください。\n次にセキュリティプロセスの優先順位という観点についてです。\nネットワークセキュリティプロセスの基本は『穴を探して手当てする事』になりますが、その基本になるのが”利用しているネットワークの把握”です。\nネットワークがどの様な形になっているのか？　どうつながっているのか？　サーバーは何台あるのか？　PCは何台あるのか？　誰が使っているのか？　どんな情報を扱っているのか？　等の情報が無ければ”穴”の存在にすら気づけません。\nもしこれらの情報の把握／整理ができていないようであれば、そこから手を付けてください。\n", "company": "GMOサイバーセキュリティ byイエラエ", "service": "セキュリティコンサルティング", "type": "FAQ"}]