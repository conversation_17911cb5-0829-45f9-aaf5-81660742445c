[{"question": "サーバ証明書の費用形態はどのようになっていますか？", "answer": "サーバ証明書の費用は、発行時に一括でお支払いいただく形態です。レンタルサーバのように月額費用が継続的に発生するわけではありませんので、イメージとしては初期費用に近い形です。ただし、契約プランや対応ドメイン数によって料金が変わる場合がありますので、複数ドメインに対応する場合は別途費用をご確認ください。", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "複数のドメインやサブドメインに対応させたい場合はどうすればよいですか？", "answer": "マルチドメイン対応（SAN）やワイルドカード証明書を活用することで複数のドメインやサブドメインをカバーすることができます。ただし、対応できるドメイン数やサブドメイン数には上限や追加費用が発生する場合があります。1つのライセンスで無制限に対応できるわけではありませんので、詳細はご利用のプランや購入元にご確認ください。", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "CSR（Certificate Signing Request）とは何ですか？", "answer": "CSRとは、サーバ証明書の発行を認証局に依頼するために作成する電子的な申請書です。作成時には「FQDN（Fully Qualified Domain Name）」「組織名」「都道府県名」「国名」などの情報を含めます。秘密鍵はCSRには含まれず、サーバ側で安全に保管する必要があります。", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "企業認証SSLやEV SSLの違いを教えてください。", "answer": "企業認証SSLは、組織の実在性を認証する証明書であり、ドメイン認証に加えて企業情報が証明書に含まれます。EV SSL（Extended Validation SSL）は、より厳格な審査を経て発行される証明書で、ブラウザのアドレスバーなどでより高い信頼性を示すことができます。組織情報をより詳細に証明書に反映したい場合は、企業認証SSLまたはEV SSLを検討ください。", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "サーバ証明書のインストール手順を教えてください。", "answer": "一般的なインストール手順は以下のとおりです。\n1. CSRの生成（証明書申請に必要）\n2. 証明書の申請と発行\n3. 証明書発行メールを受領後、発行された証明書と中間CA証明書をダウンロード\n4. CSRを作成したサーバに、秘密鍵（CSR作成時にサーバ上に保存されている）とあわせて証明書・中間CA証明書をインストール\nなお、代理店経由でご購入いただいた場合は、まず代理店サポート窓口へお問い合わせください。GMOグローバルサインの窓口情報を直接記載することはお控えください。", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "AATL（Adobe Approved Trust List）証明書はeIDASに対応していますか？", "answer": "AATL証明書はeIDASには対応していません。eIDASに対応したQualified Certificateは主に欧州のGMOグローバルサイン拠点からのみ提供されており、日本国内では販売されていません。", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "サーバ証明書の有効期限が切れるとどうなりますか？", "answer": "有効期限を過ぎると、ウェブサイトのSSL通信が保証されなくなり、ブラウザで警告が表示されるなどのリスクがあります。現在のサーバ証明書は最長397日（約13カ月）で、2年や3年といった長期プランは提供されていません。更新は有効期限の近くで行うことで、新しい証明書の有効期間が無駄なく開始されます。期限が切れる前に必ず更新手続きを行うようご注意ください。", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "CSRの作成方法を教えてください。", "answer": "CSRを作成するには、ウェブサーバの管理画面やOpenSSLなどのツールを使用します。入力情報としては、FQDN（www.example.comなどの完全修飾ドメイン名）、組織名、組織単位、所在地（都道府県、国名）などを指定します。作成後に生成される秘密鍵はサーバ上に安全に保管し、CSRファイルを認証局に提出します。", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "ディスティングイッシュネームとは何ですか", "answer": "ディスティングイッシュネームとは、CSRの作成時や、各証明書の申請時に必要な、サイトやサイト運営団体に関わる情報です。発行される証明書に記載される情報となりますので、お間違いのないようにお願いします。", "company": "GMOグローバルサイン", "service": "", "type": "FAQ"}, {"question": "SSLサーバ証明書のディスティングイッシュネームはどう入力すればいいですか", "answer": "以下の表の通りです。注意事項もご確認ください。\n```md\n### SSLサーバ証明書\n| 項目名 | 説明 | 入力例 |\n| :------------------------------ | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | :------------------------ |\n| CN(Common Name)※必須 | サイトのFQDN（グローバルIPオプションをご利用の場合はIPアドレス）です。この名前は完全一致する必要があります。URLが「https://www.globalsign.co.jp/」のサーバ証明書を申請する場合、CSRのコモンネームは「www.globalsign.co.jp」と指定しなければなりません。※ワイルドカード証明書をご利用の場合は、*（アスタリスク）を含めてください。(省略不可)※SANオプションで追加するFQDNは、申し込みフォームから設定をするため、CSRへの登録は不要になります。 | www.globalsign.co.jp |\n| O(Organization)※ | 組織の正式英文名称です。 | GlobalSign K.K. |\n| L(City or Locality)※ | 組織が置かれている市区町村です。 | Osaka-Shi |\n| S(State or Province)※ | 組織が置かれている都道府県です。 | Osaka |\n| C(Country)※ | 国を示す2文字のISO略語です。 | 日本-JP |\n```\n#### 注意事項\n- 都道府県、市区郡町村は頭文字のみ大文字またはすべて大文字にしてください。(すべて小文字のみの発行はできません)\n- 都道府県、市区郡町村を指す単語を入力する場合はハイフンまたは半角スペースでつないでください(例: Shibuya-ku, Shibuya ku)\n- ローマ字/英語表記はどちらか一方に統一(例: Osaka-shi, Kita-ku)", "company": "GMOグローバルサイン", "service": "SSLサーバ証明書", "type": "FAQ"}, {"question": "コードサイニング証明書のディスティングイッシュネームは？", "answer": "以下の表の通りです。必須項目にご注意ください。\n```md\n### コードサイニング証明書\n| 項目名 | 説明 | 入力例 |\n| :---------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------ |\n| CN(Common Name)※必須 | コモンネームは、組織名・企業名・法人名が該当いたします。 | GlobalSign K.K. |\n| O(Organization)※必須 | CNに入力された項目がOの値として登録されます。 | GlobalSign K.K. |\n| OU(Organization Unit) | 組織での部署名です。 | Marketing Dept |\n| L(City or Locality) | 組織が置かれている市区町村です。 | Shibuya |\n| S(State or Province) | コモンネームに記載される組織の所在地が日本国内の場合、都道府県は必須です。 | Tokyo |\n| C(Country)※必須 | 国を示す2文字のISO略語です。 | 日本-JP |\n| E(メールアドレス) | デジタル署名の検証時にエンドユーザーが参照する情報となります。 | <EMAIL> |\n```", "company": "GMOグローバルサイン", "service": "コードサイニング証明書", "type": "FAQ"}, {"question": "EVコードサイニング証明書のディスティングイッシュネームは？", "answer": "以下の表の通りです。必須項目にご注意ください。\n```md\n### EVコードサイニング証明書\n| 項目名 | 説明 | 入力例 |\n| :---------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------ | :------------------------ |\n| CN(Common Name)※必須 | コモンネームは、組織名・企業名・法人名が該当いたします。 | GlobalSign K.K. |\n| O(Organization)※必須 | CNに入力された項目がOの値として登録されます。 | GlobalSign K.K. |\n| OU(Organization Unit) | 組織での部署名です。 | Marketing Dept |\n| L(City or Locality)※必須 | 組織が置かれている市区町村です。 | Shibuya |\n| S(State or Province)※必須 | 日本国内の場合、都道府県は必須です。 | Tokyo |\n| C(Country)※必須 | 国を示す2文字のISO略語です。 | 日本-JP |\n| E(メールアドレス) | デジタル署名の検証時にエンドユーザーが参照する情報となります。 | <EMAIL> |\n```", "company": "GMOグローバルサイン", "service": "EVコードサイニング証明書", "type": "FAQ"}, {"question": "マネージドPKI Liteのディスティングイッシュネームはどう設定すればいいですか", "answer": "用途により2種類の中間CAがあります。\n```md\n#### アクセス認証用中間CA\n| 項目名 | 説明 | 入力例 |\n| :--------------------- | :----------------------------------------------------------------------------------------------------------------------------------- | :----------------------- |\n| CN(Common Name)※必須 | 任意の値 | <PERSON><PERSON> Yamada |\n| O(Organization)※必須 | 組織の正式名称。※プロファイル情報より自動入力 | GlobalSign K.K. |\n| OU(Organization Unit) | 組織での部署名。 | Sales Dept |\n| L(City or Locality) | 組織が置かれている市区町村。※プロファイル情報より自動入力 | Shibuya |\n| S(State or Province)※必須 | 組織が置かれている都道府県。※プロファイル情報より自動入力 | Tokyo |\n| C(Country)※必須 | 国を示す2文字のISO略語。※プロファイル情報より自動入力 | 日本-JP |\n| E(メールアドレス) | こちらに入力したメールアドレス情報は証明書に格納されます。 | <EMAIL> |\n\n#### S/MIME BR(Legacy)対応用中間CA\n| 項目名 | 説明 | 入力例 |\n| :--------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------ | :----------------------- |\n| CN(Common Name)※必須 | 担当者氏名あるいは自社・関連会社のメールアドレス | Taro Yamada |\n| O(Organization)※必須 | 組織の正式名称。※プロファイル情報より自動入力 | GlobalSign K.K. |\n| L(City or Locality) | 組織が置かれている市区町村。※プロファイル情報より自動入力 | Shibuya |\n| S(State or Province)※必須 | 都道府県。※プロファイル情報より自動入力 | Tokyo |\n| C(Country)※必須 | 国を示す2文字のISO略語。※プロファイル情報より自動入力 | 日本-JP |\n| RFC822name Email Address※必須 | 担当者の自社・関連会社のメールアドレス | <EMAIL> |\n| E(メールアドレス) | 同じメールアドレスを設定(オプション) | <EMAIL> |\n| OrganizationIdentifier(********)※必須 | 組織の法人登録番号(VAT/GOV)※プロファイル情報より自動入力 | 1011001040181 |\n```", "company": "GMOグローバルサイン", "service": "マネージドPKI Lite", "type": "FAQ"}, {"question": "文書署名用証明書のディスティングイッシュネームはどう入力すればいいですか", "answer": "以下の表の通りです。必須項目にご注意ください。\n```md\n### 文書署名用証明書\n| 項目名 | 説明 | 入力例 |\n| :--------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------- |\n| CN(Common Name)※必須 | 法人名・部門名・個人名など。 | GlobalSign.K.K / Marketing / Taro Yamada |\n| O(Organization) | 組織の正式名称。 | GlobalSign K.K. |\n| OU(Organization Unit) | 組織での部署名。 | Marketing Dept |\n| L(City or Locality) | 組織が置かれている市区町村。 | Shibuya |\n| S(State or Province) | 組織が置かれている都道府県。 | Tokyo |\n| C(Country)※必須 | 国を示す2文字のISO略語。(省略不可) | 日本-JP |\n| E(メールアドレス) | 証明書に格納されるメールアドレス。 | <EMAIL> |\n```", "company": "GMOグローバルサイン", "service": "文書署名用証明書", "type": "FAQ"}, {"question": "ディスティングイッシュネームの利用可能文字や文字数制限はありますか", "answer": "以下の表をご参照ください。\n```md\n## SSLサーバ証明書\n| 項目名 | 利用可能文字 | 文字数制限 |\n| :------------------ | :---------------------------------------------------------------- | :--------------------- |\n| CN(Common Name) | 半角英数、ハイフン[-]、ドット[.]、(ワイルドカード証明書は\"*\") | 64文字以内(半角) |\n| O(Organization) | 半角英数、半角スペース、半角記号 !#%&'()*+,-./:;=?@[]^_{|}~ | 64文字以内(半角) |\n| L(City or Locality) |  | 128文字以内(半角) |\n| S(State or Province) |  | 128文字以内(半角) |\n| C(Country) | 国を示す2文字のISO略語 | - |\n| E(メールアドレス) | 空白 | - |\n\n## コードサイニング証明書・EVコードサイニング証明書・文書署名用証明書\n| 項目名 | 利用可能文字 | 文字制限 |\n| :------------------ | :----------------------------------------------------------------------------------------------------------- | :--------------------- |\n| CN(Common Name) | 全角文字、全角スペース、半角英数、半角スペース、半角記号 :!#&'()*+,-./:=@[]^_{|} | 64文字以内(半角) |\n| O(Organization) | 全角文字、全角スペース、半角英数、半角スペース、半角記号 !#%&'()*+,-./:;=?@[]^_{|}~ | 64文字以内(半角) |\n| OU(Organization Unit) |  | 64文字以内(半角) |\n| L(City or Locality) |  | 128文字以内(半角) |\n| S(State or Province) |  | 128文字以内(半角) |\n| C(Country) | 国を示す2文字のISO略語 | - |\n| E(メールアドレス) | 標準のメールアドレス書式 | 128文字以内(半角) |\n\n## マネージドPKI Lite(アクセス認証用中間CA)\n| 項目名 | 利用可能文字 | 文字制限 |\n| :------------------ | :-------------------------------------------------------------------------------------------------- | :--------------------- |\n| CN(Common Name) | 全角(UTF-8対応)、全角スペース、半角英数、半角スペース、半角記号 #&'()*+,-./:=@[]^_{|} | 64文字以内(半角) |\n| O(Organization) |  | 64文字以内(半角) |\n| OU(Organization Unit) |  | 64文字以内(半角) |\n| L(City or Locality) |  | 128文字以内(半角) |\n| S(State or Province) |  | 128文字以内(半角) |\n| C(Country) | 国を示す2文字のISO略語 | - |\n| E(メールアドレス) | 標準のメールアドレス書式 | 128文字以内(半角) |\n\n## マネージドPKI Lite(S/MIME BR(Legacy)対応用中間CA)\n| 項目名 | 利用可能文字 | 文字制限 |\n| :------------------ | :-------------------------------------------------------------------------------------------------- | :--------------------- |\n| CN(Common Name) | 全角(UTF-8対応)、全角スペース、半角英数、半角スペース、半角記号 #&'()*+,-./:=@[]^_{|} | 64文字以内(半角) |\n| O(Organization) |  | 64文字以内(半角) |\n| L(City or Locality) |  | 128文字以内(半角) |\n| S(State or Province) |  | 128文字以内(半角) |\n| C(Country) | 国を示す2文字のISO略語 | - |\n| E(メールアドレス) | 標準のメールアドレス書式 | 128文字以内(半角) |\n\nRFC822name Email Address\n```", "company": "GMOグローバルサイン", "service": "", "type": "FAQ"}]