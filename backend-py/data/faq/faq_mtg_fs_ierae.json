[{"question": "Shisho Cloudサービスは具体的にどのような診断を行うのでしょうか？", "answer": "Shisho CloudサービスはAWS、GCP、Azureなどの主要クラウド環境における設定・権限の誤りを検出するための内部診断に特化したサービスです。サービスアカウントの認証情報を用いて、ストレージの公開範囲やIAMポリシーの設定ミス、権限過多などのリスクを洗い出します。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "GMOサイバー攻撃ネットde診断 ASMではどのような手法を使って脆弱性を見つけるのですか？", "answer": "GMOサイバー攻撃ネットde診断 ASMは、外部からの攻撃者目線でポートスキャンやWebアプリケーションへのペネトレーションテスト、ライブラリのバージョンチェックなどを実施します。SQLインジェクションやクロスサイトスクリプティングなど、一般的かつ重大な脆弱性も網羅的に検査可能です。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "GMOサイバー攻撃ネットde診断 ASM", "type": "FAQ"}, {"question": "Shisho CloudサービスとGMOサイバー攻撃ネットde診断 ASMは何が大きく違うのですか？", "answer": "Shisho Cloudサービスはクラウド環境の内部診断が中心で、認証情報を用いたIAMポリシーやストレージ設定などの誤りを詳細に確認します。一方、GMOサイバー攻撃ネットde診断 ASMは外部からの攻撃者視点で、公開サーバやWebアプリに対する脆弱性を検出するサービスです。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "クラウドのIAMやロールの設定ミスを見つけたい場合はどちらが最適ですか？", "answer": "クラウド環境のIAMポリシーやロール設定を重点的に調べたい場合はShisho Cloudサービスが最適です。内部から権限設定を検証し、過剰な権限や設定抜けがないかを詳細に確認します。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "外部公開されているWebサイトやサーバの脆弱性を調べたい場合はどちらを使うべきでしょうか？", "answer": "外部向けWebサーバやWebアプリの脆弱性（SQLインジェクション、XSSなど）を検出したい場合は、GMOサイバー攻撃ネットde診断 ASMが最適です。攻撃者視点でのポートスキャンやWebアプリテストが中心となります。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "GMOサイバー攻撃ネットde診断 ASM", "type": "FAQ"}, {"question": "両方のサービスを同時に利用する必要はありますか？", "answer": "クラウド内部の設定ミスと、外部公開サーバの脆弱性リスクの両方を把握したい場合は併用がおすすめです。Shisho Cloudサービスはクラウド内部の権限管理を、GMOサイバー攻撃ネットde診断 ASMは外部攻撃可能性をカバーし、総合的なセキュリティ強化が可能になります。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "システム全体を包括的に検査したい場合、まずどちらの診断を優先するのが良いのでしょうか？", "answer": "まずは公開サーバからの侵入経路を早期に把握するために、GMOサイバー攻撃ネットde診断 ASMを実施することをおすすめします。その後、クラウド内部設定にも不安があればShisho Cloudサービスを追加し、多角的にリスクを洗い出すと効果的です。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "GMOサイバー攻撃ネットde診断 ASM", "type": "FAQ"}, {"question": "Shisho CloudサービスやGMOサイバー攻撃ネットde診断 ASMの導入にあたって、必要な準備はありますか？", "answer": "Shisho Cloudサービスでは、クラウド認証情報や診断対象リソースの範囲を明確にしていただく必要があります。GMOサイバー攻撃ネットde診断 ASMの場合は、診断対象となるドメイン、IPアドレスをご用意ください。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "Shisho Cloudサービスの診断では、オブジェクトストレージの公開設定ミスも発見できますか？", "answer": "はい。S3やBlob Storageなど、クラウドのオブジェクトストレージの公開範囲や権限設定を詳細に確認し、意図せず外部に公開しているファイルやフォルダがないかを洗い出すことが可能です。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "開発者やインフラ担当者がShisho Cloudサービスを利用するメリットは何ですか？", "answer": "クラウド環境の設定不備や過剰な権限付与を早期に発見し、潜在的なリスクを低減できます。開発スピードを落とすことなく、セキュアな運用体制を維持するための指針を得られる点が大きなメリットです。", "company": "GMO Flatt Security", "service": "<PERSON><PERSON>o Cloudサービス", "type": "FAQ"}, {"question": "内部犯行や従業員のミスによる情報漏洩を防ぎたい場合、GMOサイバー攻撃ネットde診断 ASMで対処できますか？", "answer": "内部権限の乱用や設定ミスによる情報漏洩は外部からの視点だけでは十分に検知しづらい場合があります。クラウド内部のIAMやロール設定を包括的に確認するShisho Cloudサービスをあわせて利用することで、内部犯行やミスによるリスクにも対処しやすくなります。", "company": "GMOサイバーセキュリティ byイエラエ", "service": "GMOサイバー攻撃ネットde診断 ASM", "type": "FAQ"}]