[{"question": "どのようなコースと学習コンテンツを受講できますか？", "answer": "KENROでは学習コンテンツの集合である「コース」を受講し、目的に沿った学習を行うことができます。各学習コンテンツは「SQL Injection脆弱性」のような学習単元のことであり、テキストでの説明と演習によって構成されています。\n\n2022年5月現在、以下の4つのコースがあります。", "company": "GMO Flatt Security", "service": "KENROサービス", "type": "FAQ"}, {"question": "Web脆弱性基礎編コースでは何が学べますか？", "answer": "OWASP Top10に含まれる脆弱性を始め、Webアプリケーションにおける代表的な脆弱性を網羅的に学べるコースです。\n\n以下の10の脆弱性の原理・攻撃手法・対策を学ぶための学習コンテンツを含みます。\n\n- **SQL Injection 脆弱性**\n- **Cross-Site Request Forgery 脆弱性**\n- **Directory Traversal 脆弱性**\n- **Insecure Deserialization 脆弱性**\n- **Cross-Site Scripting (XSS) 脆弱性**\n- **XML External Entity 脆弱性**\n- **Clickjacking 脆弱性**\n- **OS Command Injection 脆弱性**\n- **Open Redirection 脆弱性**\n- **Header Injection 脆弱性**", "company": "GMO Flatt Security", "service": "KENROサービス", "type": "FAQ"}, {"question": "GraphQL コースでは何が学べますか？", "answer": "GraphQL の技術的背景と、使用時に持つべきセキュリティ観点を整理するコースです。\n\n以下の学習コンテンツを含みます。\n\n- **GraphQL の基礎とセキュリティ**\n  GraphQL の基礎知識と GraphQL API をセキュアに設計、実装する手法を学ぶための学習コンテンツです。", "company": "GMO Flatt Security", "service": "KENROサービス", "type": "FAQ"}, {"question": "JSON Web Token コースでは何が学べますか？", "answer": "JSON Web Token（JWT）の技術的背景と、使用時に持つべきセキュリティ観点を整理するコースです。\n\n以下の学習コンテンツを含みます。\n\n- **JSON Web Token (JWT) 前編 - JWT の基礎**\n  利用例をもとに、JSON Web Token (JWT) に関する初歩的な知識を学ぶための学習コンテンツです。\n- **JSON Web Token (JWT) 後編 - JWT のセキュリティ**\n  JSON Web Token (JWT) を利用する際のセキュリティ的上の注意点についてを学ぶための学習コンテンツです。", "company": "GMO Flatt Security", "service": "KENROサービス", "type": "FAQ"}, {"question": "WebSocket コースでは何が学べますか？", "answer": "WebSocket の技術的背景と、使用時に持つべきセキュリティ観点を整理するコースです。\n\n以下の学習コンテンツを含みます。\n\n- **WebSocket の基礎とセキュリティ**\n  WebSocket の基礎知識と、利用の際に持つべきセキュリティ観点についてを学ぶための学習コンテンツです。", "company": "GMO Flatt Security", "service": "KENROサービス", "type": "FAQ"}, {"question": "どのようなプログラミング言語に対応していますか？", "answer": "2021年10月現在、下記6つのプログラミング言語に対応しています。\n\n- **Java**\n- **Python**\n- **Go**\n- **<PERSON> (Rails)**\n- **PHP**\n- **C#**", "company": "GMO Flatt Security", "service": "KENROサービス", "type": "FAQ"}, {"question": "プログラミング言語はどのように利用できますか？", "answer": "【サンプルコード】\n学習コンテンツ中に含まれるサンプルコードの表示を複数の言語に切り替えることが可能です。一部のサンプルコードは説明を分かりやすくするため、PHPでのソースコードを用いることがあります。\n\n【動作のイメージ】\nサンプルコード切り替えのデモなどが行えます。\n\n【堅牢化演習】\n堅牢化演習では、複数のソースコードから任意のプログラミング言語を選択してダウンロードし、演習に取り組めます。", "company": "GMO Flatt Security", "service": "KENROサービス", "type": "FAQ"}, {"question": "請求方法・お支払い方法はどのようになりますか？", "answer": "【初回ご請求のタイミングについて】\n初回のお申し込みからご受講開始、お支払いまでの流れはこちらをご確認ください。\n\n【お支払い方法について】\n2021年10月現在は以下の1つの方法のみとなります。\n\n- 銀行振込み払い\n  株式会社Flatt Securityの指定口座へ商品代金をお支払頂くお支払い方法です。お申込み後にご請求書をメールにてお送りし、指定口座へお振込みいただきます（振込手数料はご負担ください）。\n\n【ご請求書について】\nお申し込み後3営業日以内にPDF形式にて送信いたします。", "company": "GMO Flatt Security", "service": "KENROサービス", "type": "FAQ"}, {"question": "初回申し込みからご請求までの流れを教えてください。", "answer": "1. お申し込みの意思を当社担当者にお伝えください。\n2. お申し込み用オンラインフォームを当社担当者よりお送りいたします。\n3. フォームに必要情報を入力・送信ください。\n4. 2営業日以内に管理者アカウントおよびご請求書を発行いたします。\n5. 管理画面にてユーザー招待・受講コースの登録を行い、受講開始となります。\n6. お申し込みの翌月末日に、銀行振込にてお支払いいただきます。", "company": "GMO Flatt Security", "service": "KENROサービス", "type": "FAQ"}, {"question": "学習コース登録はどのように行いますか？", "answer": "・初回のご購入時:\n  - 管理画面の発行と同時にご購入分のチケットを付与（利用期限はなし）\n  - お好きなタイミングでコースチケットをご受講者様に登録いただけます（登録日から12ヶ月間が受講期間）\n  - 管理画面発行時に、ご請求書を発行\n\n・追加のご購入時:\n  - 管理画面にてコースを登録した当日から12ヶ月間が受講期間となります\n  - 登録日の当月末締め翌月末のお支払い\n  - ご登録日の翌月頭にご請求書を送付", "company": "GMO Flatt Security", "service": "KENROサービス", "type": "FAQ"}, {"question": "管理者スタートガイドはどのような内容ですか？", "answer": "このドキュメントでは、お申込み完了後から管理画面にログインするまでの手順について説明しています。\n\n1. 招待メールを受け取る\n2. パスワードの設定\n3. パスワードの登録完了メールを受け取る\n4. 管理画面にログインする", "company": "GMO Flatt Security", "service": "KENROサービス", "type": "FAQ"}, {"question": "どのように招待メールを受け取るのですか？", "answer": "お申し込み書にご記入の受講開始日までに、管理者用メールアドレスに招待メールが届きます。メールに記載された「パスワードを設定する」ボタンをクリックし、パスワード設定画面を表示してください。\n\n※招待メール送信後4日でパスワード設定の有効期限が切れますのでご注意ください。", "company": "GMO Flatt Security", "service": "KENROサービス", "type": "FAQ"}, {"question": "パスワードはどのように設定しますか？", "answer": "設定したいパスワードを入力し、「パスワードのリセット」を押下することでパスワードを設定できます。\n\n※ここで設定したパスワードが受講画面・管理画面共通で適用されます。", "company": "GMO Flatt Security", "service": "KENROサービス", "type": "FAQ"}, {"question": "パスワードの登録完了メールはどのように受け取りますか？", "answer": "パスワードの設定に成功すると、登録完了メールが届きます。確認後は受講画面ではなく「システム管理者の方はこちら」のリンク (https://admin.kenro.flatt.tech) から管理画面にアクセスしてください。\n\nこの管理画面URLはブックマークしておくことをお勧めします。", "company": "GMO Flatt Security", "service": "KENROサービス", "type": "FAQ"}, {"question": "管理画面にはどのようにログインすればよいですか？", "answer": "正常にログインできれば、管理画面トップが表示されます。次はユーザーを招待し、学習コースを割り当てて受講を開始していただく流れになります。詳細は管理画面の利用方法ドキュメントをご参照ください。", "company": "GMO Flatt Security", "service": "KENROサービス", "type": "FAQ"}, {"question": "管理画面ではどのような操作ができますか？", "answer": "このドキュメントでは、管理画面でできることと各機能の説明について記載しています。\n\n【主な機能】\n1. グループの作成\n2. ユーザー招待\n3. 学習コース登録\n4. 受講状況の確認\n\nそれぞれをCSVで一括操作する方法や受講完了予定日の通知、提出履歴エクスポートなどの機能があります。", "company": "GMO Flatt Security", "service": "KENROサービス", "type": "FAQ"}, {"question": "CSVファイルを使って情報を一括操作できますか？", "answer": "グループやユーザーの登録・編集、学習コースをユーザーに紐付ける操作は、CSVファイルを用いて一括で行うことができます。詳細はヘルプドキュメントをご参照ください。", "company": "GMO Flatt Security", "service": "KENROサービス", "type": "FAQ"}, {"question": "受講完了予定日の通知はどのように行われますか？", "answer": "受講完了予定日を設定し、任意で設定したタイミングで事前にメール通知ができる機能です。学習コース画面で設定します。", "company": "GMO Flatt Security", "service": "KENROサービス", "type": "FAQ"}, {"question": "受講者の演習提出履歴をエクスポートできますか？", "answer": "受講者の演習提出履歴は受講状況画面よりCSV形式でエクスポートできます。分析やレポート作成にお役立てください。", "company": "GMO Flatt Security", "service": "KENROサービス", "type": "FAQ"}, {"question": "ご契約前によくある質問は何ですか？", "answer": "【ご契約について】\n- 初回契約〜受講開始までの日数は？\n  2営業日以内に管理画面発行→ユーザー招待・コース登録→登録日から12ヶ月が受講期間です。\n\n- 途中で追加登録したユーザーの受講期間は？\n  登録日から12ヶ月間受講できます。\n\n- 初回契約時に別コースを追加した場合…\n  コースごとに登録日から12ヶ月が有効です。\n\n- 発展コースのみの受講は可能？\n  可能です。\n\n【料金について】\n- 累積購入数にカウントされるのはWeb脆弱性基礎編コースのみ\n\n【管理者権限について】\n- 管理者の追加は担当者にご依頼ください。\n- 年間契約が終了しても管理画面ログインは可能。\n\n【ご請求・お支払いについて】\n- 一括払いのみ。初回は管理画面発行時、追加登録時は登録日の当月末締め翌月末払いです。", "company": "GMO Flatt Security", "service": "KENROサービス", "type": "FAQ"}, {"question": "管理権限を持つユーザーの招待リンクが無効になった場合はどうすればよいですか？", "answer": "招待メール送付後4日経過するとリンクは無効になります。担当者へ直接連絡、または問い合わせフォームよりご連絡ください。", "company": "GMO Flatt Security", "service": "KENROサービス", "type": "FAQ"}, {"question": "管理者を増やしたり権限を変更したい場合はどうすればいいですか？", "answer": "管理画面からは行えませんので、担当者へご依頼ください。", "company": "GMO Flatt Security", "service": "KENROサービス", "type": "FAQ"}, {"question": "コース登録したユーザーを取り消したい場合はどうすればいいですか？", "answer": "以下を両方満たす場合のみ対応可能です。\n1. ユーザーが演習に未着手 (アカウント未登録含む)\n2. コース登録から7日以内\n\n上記以外は対応できかねますのでご了承ください。", "company": "GMO Flatt Security", "service": "KENROサービス", "type": "FAQ"}, {"question": "ブラウザで正常に動作しない、または画面が表示されない場合はどうすればいいですか？", "answer": "下記PCブラウザで再度お試しください。\n- Google Chrome最新版\n- Firefox最新版\n- Microsoft Edge最新版(Windows)\n\nIEや古いEdgeでは正常動作しない場合がございます。\n\n上記でも解決しない場合はフォームからご連絡ください。", "company": "GMO Flatt Security", "service": "KENROサービス", "type": "FAQ"}, {"question": "演習環境が表示されない場合はどうすればいいですか？", "answer": "シークレットウィンドウでアクセスしない / Cookie関連の拡張機能をオフ / 画面をリロード などをお試しください。それでも解決しない場合はご連絡ください。", "company": "GMO Flatt Security", "service": "KENROサービス", "type": "FAQ"}]