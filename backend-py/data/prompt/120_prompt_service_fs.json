{"company": "GMO Flatt Security株式会社", "services": [{"id": "Shisho Cloud byGMO", "service_names": ["Shisho Cloud byGMO", "簡易脆弱性診断", "クラウド診断", "CSPM"], "info": "クラウド資産を連携するだけで、WebアプリケーションやAPI、クラウド設定を診断。 網羅的にアプリケーションのリスク管理ができます。", "url": "https://shisho.dev/ja"}, {"id": "高度脆弱性診断", "service_names": ["高度脆弱性診断", "Webペンテスト", "手動脆弱性診断", "CSPM"], "info": "ブラックボックス形式だけに頼らない独自の診断スタイル、高度な技術力、モダンな技術スタックへの対応、開発者目線の丁寧なレポーティングです。他社が追随できない開発者体験で、事業/開発のスピードを損なわないセキュリティの実践をサポートします。", "url": "https://flatt.tech/assessment"}, {"id": "ソースコード診断", "service_names": ["ソースコード診断"], "info": "セキュリティ診断業界では、外部から得られる情報だけを用いて攻撃を試みる「ブラックボックス診断」のみを行うケースが通例となっていますが、Flatt Securityの診断では都度ソースコードの参照も行う診断を標準プランとしており、追加料金もいただきません。いただくご予算の中で最大の網羅性を追求した効率的な診断スタイルが特徴です。", "url": "https://flatt.tech/assessment"}, {"id": "SPA診断", "service_names": ["SPA診断", "Firebase診断", "GraphQL診断"], "info": "本質的なセキュリティ対策は、プロダクトごとに異なることが少なくありません。AWS・Google Cloud・Firebase・SPA・GraphQLといった技術スタックや予算、その他制約に合わせてオーダーメイドの診断プランを提案します。積極的な提案は当社の強みです。", "url": "https://flatt.tech/assessment"}, {"id": "KENRO", "service_names": ["KENRO", "セキュアコーディング研修", "セキュア開発 e-learning", "開発者向けセキュリティ研修"], "info": "脆弱性の攻撃と修正で学ぶ開発者のためのセキュリティ学習サービス Web 開発に必須なセキュリティ技術を、豊富な実践演習で体系的に学べるクラウドサービスです。", "url": "https://flatt.tech/kenro"}]}