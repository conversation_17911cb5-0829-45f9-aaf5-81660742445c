import hashlib
import json
import os
import pickle
import pprint
import time
from datetime import datetime

import functions_framework
import vertexai
from flask import Response, stream_with_context
from fraud_protection import (
    extract_titles,
    filter_tainted_messages,
    is_partial_end_match,
    replace_buffer,
)
from google.cloud import aiplatform, storage
from news_generator import format_news_stream, generate_news_stream, has_news_response
from openai import AzureOpenAI, OpenAI
from rate_limiter import RateLimiter
from tenacity import retry, stop_after_attempt, wait_fixed
from vertexai.language_models import TextEmbeddingInput, TextEmbeddingModel

# # ストリームジェネレータ
# def generate_stream():
#     yield "data: Hello World\n\n"  # データとしてのイベントを生成
#     time.sleep(1)  # 模擬的な遅延
#     yield "data: Test Stream\n\n"  # さらにデータを追加
#     time.sleep(1)
#     yield "data: Another message\n\n"  # さらなるデータ
#     # ストリームの終わりを暗示せず、クライアントは自動的に終了をハンドリング

NG_WORDS = {
    "GMOグループ": "GMOインターネットグループ",
}

RESPONSE_FOR_FRAUD_JA = "\n\n大変申し訳ございませんが、\
インターネットのセキュリティに関するご質問しかお答えすることができません。\
お悩み・ご不安などがありましたらお気軽にお問い合わせください。"

RESPONSE_FOR_RATE_LIMIT_JA = (
    "ただいま混み合っております。しばらく時間をおいてから再度お試しください。"
)

CLOSING_MESSAGE_JA = "\n\n\n\n以上で、ご質問の回答になりましたでしょうか？さらにお知りになりたいことがあれば追加のご質問をお受けいたします。"

RESPONSE_FOR_FRAUD_EN = "\n\nWe’re very sorry, but we can only answer questions related to internet security. If you have any other questions, please feel free to ask."

RESPONSE_FOR_RATE_LIMIT_EN = "We’re currently experiencing heavy traffic. Please wait a moment and try again later."

CLOSING_MESSAGE_EN = "\n\n\n\nDoes this fully answer your question? If there’s anything else you’d like to know, please feel free to ask."

RESPONSE_FOR_FRAUD = RESPONSE_FOR_FRAUD_JA
RESPONSE_FOR_RATE_LIMIT = RESPONSE_FOR_RATE_LIMIT_JA
CLOSING_MESSAGE = CLOSING_MESSAGE_JA

PERMIT_IP_LIST = [
    "*************",
    "*************",
    "*************",
    "*************",
    "*************",
    "*************",
    "*************",
    "*************",
    "*************",
    "*************",
    "*************",
    "*************",
    "*************",
    "*************",
    "*************",
    "***************",
]

USER_FLOW_CHAT = "chat"
USER_FLOW_DIAGNOSIS = "diagnosis"
USER_FLOW_YOURBRAND = "yourbrand"

storage_client = storage.Client(project=os.environ.get("PROJECT_ID"))
bucket = storage_client.get_bucket(os.environ.get("PROMPT_BUCKET_NAME"))
is_local = os.environ.get("ENV") == "local"

rate_limiter = RateLimiter(PERMIT_IP_LIST)


def _get_closing_message(language_code: str):
    if language_code == "ja":
        return CLOSING_MESSAGE_JA
    elif language_code == "en":
        return CLOSING_MESSAGE_EN
    else:
        return CLOSING_MESSAGE_JA


def _get_response_for_fraud(language_code: str):
    if language_code == "ja":
        return RESPONSE_FOR_FRAUD_JA
    elif language_code == "en":
        return RESPONSE_FOR_FRAUD_EN
    else:
        return RESPONSE_FOR_FRAUD_JA


def _get_response_for_rate_limit(language_code: str):
    if language_code == "ja":
        return RESPONSE_FOR_RATE_LIMIT_JA
    elif language_code == "en":
        return RESPONSE_FOR_RATE_LIMIT_EN
    else:
        return RESPONSE_FOR_RATE_LIMIT_JA


def read_file(file):
    if is_local:
        with open(f"data/prompt/{file}", "r") as f:
            return f.read()
    return file.download_as_string().decode("utf-8")


def get_files():
    if is_local:
        return os.listdir("data/prompt/")
    return list(bucket.list_blobs(prefix="prompt/"))


def get_files_by_prefix(files, prefix):
    if is_local:
        return sorted([file for file in files if file.startswith(prefix)])
    return sorted(
        [file for file in files if file.name.startswith(f"prompt/{prefix}")],
        key=lambda file: file.name,
    )


def download_prompt():
    prefix_of_first_step = "0"
    prefix_of_second_step = "1"
    prefix_of_third_step = "2"

    files = get_files()

    content = ""

    first_files = get_files_by_prefix(files, prefix_of_first_step)
    for file in first_files:
        content += read_file(file) + "\n"

    content += "\n<context_GMOネットセキュリティのサービス>\n"

    second_files = get_files_by_prefix(files, prefix_of_second_step)
    for file in second_files:
        content += read_file(file) + "\n"

    content += "</context_GMOネットセキュリティのサービス>\n\n"

    third_files = get_files_by_prefix(files, prefix_of_third_step)
    for file in third_files:
        content += read_file(file) + "\n"

    prefix_of_chat = "condition_end_chat_ja"
    prefix_of_diagnosis = "condition_end_diagnosis_ja"
    prefix_of_yourbrand = "condition_end_yourbrand_ja"
    prefix_of_chat_en = "condition_end_chat_en"
    prefix_of_diagnosis_en = "condition_end_diagnosis_en"
    prefix_of_yourbrand_en = "condition_end_yourbrand_en"

    condition_end_chat_ja = read_file(get_files_by_prefix(files, prefix_of_chat)[0])
    condition_end_diagnosis_ja = read_file(
        get_files_by_prefix(files, prefix_of_diagnosis)[0]
    )
    condition_end_yourbrand_ja = read_file(
        get_files_by_prefix(files, prefix_of_yourbrand)[0]
    )
    condition_end_chat_en = read_file(get_files_by_prefix(files, prefix_of_chat_en)[0])
    condition_end_diagnosis_en = read_file(
        get_files_by_prefix(files, prefix_of_diagnosis_en)[0]
    )
    condition_end_yourbrand_en = read_file(
        get_files_by_prefix(files, prefix_of_yourbrand_en)[0]
    )
    condition_end_dict = {
        "ja": {
            "chat": condition_end_chat_ja,
            "diagnosis": condition_end_diagnosis_ja,
            "yourbrand": condition_end_yourbrand_ja,
        },
        "en": {
            "chat": condition_end_chat_en,
            "diagnosis": condition_end_diagnosis_en,
            "yourbrand": condition_end_yourbrand_en,
        },
    }

    ja_en_mappings = pprint.pformat(
        json.loads(read_file(get_files_by_prefix(files, "ja_en_mappings")[0])),
        width=200,
        sort_dicts=False,
    )

    return (
        content,
        condition_end_dict,
        ja_en_mappings,
    )


def create_df():
    document = os.environ.get("DOCUMENTS_DF_PKL")
    if is_local:
        with open(f"data/{document}", "rb") as f:
            return pickle.loads(f.read())
    return pickle.loads(bucket.blob(document).download_as_string())


def create_openai_client():
    client_mode = os.environ.get("OPENAI_CLIENT_MODE")
    if client_mode == "openai":
        print("Using OpenAI Client")
        return OpenAI(api_key=os.environ.get("SECRET_OPENAI_API_KEY"))
    if client_mode == "azure":
        print("Using Azure OpenAI Client")
        return AzureOpenAI(
            api_version="2024-12-01-preview",
            azure_endpoint="https://azure-openai-group-gmoig-jiken.openai.azure.com/",
            api_key=os.environ.get("SECRET_AZURE_OPENAI_API_KEY"),
        )
    print("[ERROR] Can not find OPENAI_CLIENT_MODE. Using OpenAI Client")
    return OpenAI(api_key=os.environ.get("SECRET_OPENAI_API_KEY"))


def get_chat_completion_model():
    client_mode = os.environ.get("OPENAI_CLIENT_MODE")
    if client_mode == "openai":
        return os.environ.get("GPT_MODEL")
    if client_mode == "azure":
        return os.environ.get("AZURE_GPT_MODEL")
    print("[ERROR] Can not find OPENAI_CLIENT_MODE. Using OpenAI Setting")
    return os.environ.get("GPT_MODEL")


df = create_df()

vertexai.init(project=os.environ.get("PROJECT_ID"), location=os.environ.get("REGION"))

embedding_model = TextEmbeddingModel.from_pretrained(
    os.environ.get("EMBEDDING_NEIGHBOR_MODEL")
)

my_index_endpoint = aiplatform.MatchingEngineIndexEndpoint(
    index_endpoint_name=os.environ.get("EMBEDDING_NEIGHBOR_INDEX_ENDPOINT"),
)

openai_client = create_openai_client()

base_prompt, condition_end_dict, ja_en_mappings = download_prompt()


def refresh_prompt():
    global base_prompt, condition_end_dict, ja_en_mappings
    base_prompt, condition_end_dict, ja_en_mappings = download_prompt()


def refresh(request):
    global embedding_model
    embedding_model = TextEmbeddingModel.from_pretrained(
        os.environ.get("EMBEDDING_NEIGHBOR_MODEL")
    )
    global my_index_endpoint
    my_index_endpoint = aiplatform.MatchingEngineIndexEndpoint(
        index_endpoint_name=os.environ.get("EMBEDDING_NEIGHBOR_INDEX_ENDPOINT"),
    )
    refresh_prompt()
    print("Index endpoint connection is refreshed.")
    return "ok"


@functions_framework.http
def chat(request):
    request_json = request.get_json(silent=True)

    if "refresh" in request_json and request_json["refresh"] == "true":
        return refresh(request)

    post = request_json["post"]
    messages = request_json["messages"]
    messages = filter_tainted_messages(messages)
    messages = _delete_closing_message(messages)

    user_flow = (
        request_json["user_flow"] if "user_flow" in request_json else USER_FLOW_CHAT
    )
    language_code = request_json["lang"] if "lang" in request_json else "ja"

    global RESPONSE_FOR_FRAUD, RESPONSE_FOR_RATE_LIMIT, CLOSING_MESSAGE
    RESPONSE_FOR_FRAUD = _get_response_for_fraud(language_code)
    RESPONSE_FOR_RATE_LIMIT = _get_response_for_rate_limit(language_code)
    CLOSING_MESSAGE = _get_closing_message(language_code)

    if language_code == "ja":
        language = "日本語"
    elif language_code == "en":
        language = "英語"
    else:
        language = "日本語"

    client_ip = request_json["client_ip"] if "client_ip" in request_json else None
    if client_ip is None:
        print("client_ip is None")
    else:
        now = datetime.now()
        if not rate_limiter.is_allowed_by_day(client_ip, now):
            print(f"Rate limit exceeded by day: {client_ip}")
            return Response(send_rate_limit_message(), mimetype="text/event-stream")
        if not rate_limiter.is_allowed_by_minute(client_ip, now):
            print(f"Rate limit exceeded by minute: {client_ip}")
            return Response(send_rate_limit_message(), mimetype="text/event-stream")
        print(f"Rate limit is allowed: {client_ip}")

    if is_local:
        refresh_prompt()

    prompt_hash = generate_hash()
    print(f"prompt_hash: {prompt_hash}")

    print("start")
    print(time.time())
    prompt = replace_prompt_hash(prompt_hash)
    prompt = prompt.replace(r"{language}", language)

    if user_flow == USER_FLOW_CHAT:
        condition_end = condition_end_dict[language_code]["chat"]
    elif user_flow == USER_FLOW_DIAGNOSIS:
        condition_end = condition_end_dict[language_code]["diagnosis"]
    elif user_flow == USER_FLOW_YOURBRAND:
        condition_end = condition_end_dict[language_code]["yourbrand"]
    else:
        condition_end = condition_end_dict[language_code]["chat"]

    if language_code == "en":
        condition_end = condition_end.replace(
            r"{condition_ja_en_mappings}",
            f"- When translating proper nouns (e.g., service names) into English, apply the following mapping:\n{ja_en_mappings}",
        )

    context = get_dense_embedding_neighbor(post)
    print("create_context")
    print(time.time())

    rag_context = [format_rag_text(ctx) for ctx in context if ctx["type"] == "FAQ"]
    blog_context = [format_rag_text(ctx) for ctx in context if ctx["type"] == "blog"]

    new_messages = createMessages(
        prompt, messages, rag_context, blog_context, condition_end, post, user_flow
    )
    print("create_messages")
    print(time.time())

    titles = extract_titles(
        "\n".join(
            map(
                lambda x: x["content"],
                filter(lambda x: x["role"] == "system", new_messages),
            )
        )
    )
    ng_words = {} | NG_WORDS
    ng_words[prompt_hash] = ""
    # titleは置換しない（最後まで残ると不正と検知されるため）
    for title in titles:
        ng_words[title] = title

    new_messages = add_hash_to_assistant_messages(new_messages, prompt_hash)

    response_gen = sendMessages(post, new_messages, ng_words, prompt_hash, language)
    return Response(stream_with_context(response_gen), mimetype="text/event-stream")


def generate_hash():
    current_time = int(time.time())
    quotient = current_time // 300
    quotient_str = str(quotient)
    salt = os.environ.get("SECRET_PROMPT_HASH_SALT")
    salted_str = quotient_str + salt
    hashed_value = hashlib.sha256(salted_str.encode()).hexdigest()[:10]
    return hashed_value


def replace_prompt_hash(prompt_hash):
    return base_prompt.replace("OtDC3k0LmE", prompt_hash)


@retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
def get_dense_embedding_neighbor(query_text):
    query_dense_emb = get_query_dense_embedding(query_text)

    response = my_index_endpoint.find_neighbors(
        deployed_index_id=os.environ.get("EMBEDDING_NEIGHBOR_DEPLOY_INDEX_ID"),
        queries=[query_dense_emb],
        num_neighbors=10,
    )

    context = []
    for idx, neighbor in enumerate(response[0]):
        question = df.question[int(neighbor.id)]
        answer = df.answer[int(neighbor.id)]
        company = df.company[int(neighbor.id)]
        service = df.service[int(neighbor.id)]
        type_ = df.type[int(neighbor.id)]
        # dense_dist = neighbor.distance if neighbor.distance else 0.0
        # text = f"<関連情報>question: {question:<9},  answer: {answer}, company: {company}, service: {service}</関連情報>"
        # context.append(text)
        context.append(
            {
                "type": type_,
                "question": question,
                "answer": answer,
                "company": company,
                "service": service,
            }
        )

    return context


def get_query_dense_embedding(text):
    input = TextEmbeddingInput(text=text, task_type="RETRIEVAL_QUERY")
    return embedding_model.get_embeddings([input])[0].values


def format_rag_text(context: dict):
    question = context["question"]
    answer = context["answer"]
    company = context["company"]
    service = context["service"]
    type_ = context["type"]

    if type_ == "FAQ":
        return f"<関連情報>question: {question:<9},  answer: {answer}, company: {company}, service: {service}</関連情報>"
    elif type_ == "blog":
        return answer
    else:
        raise ValueError


def createMessages(
    prompt, messages, context, blog_context, condition_end, post, user_flow
):
    context_FAQ = f"""
    <context_FAQ>
    {context}
    </context_FAQ>"""

    question = post.strip()

    user_count = [m["role"] for m in messages].count("user")
    condition_end = f"""
## **回答送信前の最終確認**
{condition_end}
以上の条件を理解し、厳密に遵守します。
""" + (
        "#### **注意** 1度目の応答なので、お客様から直接言及がない限りサービスを紹介してはいけません。"
        if user_count == 0 and user_flow == USER_FLOW_CHAT
        else ""
    )

    messages.insert(
        0,
        {"role": "system", "content": prompt.strip()},
    )
    messages.insert(
        1,
        {"role": "system", "content": f"## {context_FAQ.strip()}"},
    )
    messages.insert(
        2,
        {
            "role": "system",
            "content": f"## GMO技術ブログ：{blog_context}",
        },
    )
    messages.append({"role": "user", "content": f"{question}"})
    messages.append({"role": "assistant", "content": condition_end.strip()})

    return messages


def sendMessages(user_question, messages, ng_words, prompt_hash, language):
    starts_with_hash = False
    first_response = True
    buffer = ""

    with_news = not has_news_response(messages, language)
    news_retry_count = 0

    try:
        if with_news:
            news_stream = generate_news_stream(user_question, language)
        else:
            news_stream = None
        with openai_client.chat.completions.create(
            model=get_chat_completion_model(),
            messages=messages,
            temperature=0.3,
            stream=True,
            max_tokens=8192,
            stream_options={"include_usage": True},
        ) as stream:
            for chunk in stream:
                if hasattr(chunk, "choices") and len(chunk.choices) > 0:
                    delta = chunk.choices[0].delta
                    if hasattr(delta, "content"):
                        token = delta.content

                        if token is None:
                            continue

                        buffer += token

                        if buffer.startswith(prompt_hash):
                            starts_with_hash = True
                        if not starts_with_hash:
                            if len(buffer) > len(prompt_hash):
                                print("prompt hash is not found in buffer: ", buffer)
                                break
                            continue
                        buffer = replace_buffer(buffer, ng_words)
                        if is_partial_end_match(buffer, ng_words):
                            continue

                        if buffer != "":
                            if first_response:
                                print("first response")
                                print(time.time())
                                first_response = False
                            yield buffer
                            buffer = ""
                # tenacityでリトライするとOpenAIのレスポンスの返却が遅れるので，手動でリトライする
                if with_news and (
                    news_stream is None or news_stream.response.status_code != 200
                ):
                    if news_retry_count < 3:
                        print("news_stream response error (retry)")
                        news_stream = generate_news_stream(user_question, language)
                        news_retry_count += 1
                    print("news_stream response error")
        # ここでバッファにデータが残っている場合，不正な出力を検出して送信を止めていたということ。
        if buffer != "":
            yield RESPONSE_FOR_FRAUD
            return

        print("generate news")
        print(time.time())
        if with_news and news_stream is not None:
            news_retry_count = 0
            while news_retry_count < 3:
                try:
                    news_str = format_news_stream(news_stream, language)
                    if news_str != "":
                        print("send news")
                        print(time.time())
                        yield news_str
                    else:
                        print("news is empty")
                        print(time.time())
                    break
                except Exception as e:
                    print(f"Error in sendMessages(news): {e}")
                    news_retry_count += 1
                    news_stream = generate_news_stream(user_question, language)

        yield CLOSING_MESSAGE
    except Exception as e:
        print(e)
        yield buffer  # Ensure any remaining buffer is emitted in case of error
        raise e


def send_rate_limit_message():
    yield RESPONSE_FOR_RATE_LIMIT


def add_hash_to_assistant_messages(messages, hash):
    for message in messages:
        if message["role"] == "assistant":
            message["content"] = hash + message["content"]
    return messages


def _delete_closing_message(messages):
    for i, m in enumerate(messages):
        if m["role"] == "assistant" and CLOSING_MESSAGE in m["content"]:
            messages[i]["content"] = m["content"].replace(CLOSING_MESSAGE, "")
    return messages
