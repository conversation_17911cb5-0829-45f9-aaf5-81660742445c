import unittest
import datetime
from rate_limiter import RateLimiter


class RateLimiterTest(unittest.TestCase):
    def test_minute(self):
        ip = '*******'
        rate_limiter = RateLimiter([])
        d = datetime.datetime.strptime("202501010000", "%Y%m%d%H%M")
        self.assertTrue(rate_limiter.is_allowed_by_minute(ip, d))
        self.assertTrue(rate_limiter.is_allowed_by_minute(ip, d))
        self.assertTrue(rate_limiter.is_allowed_by_minute(ip, d))
        self.assertTrue(rate_limiter.is_allowed_by_minute(ip, d))
        self.assertTrue(rate_limiter.is_allowed_by_minute(ip, d))
        self.assertFalse(rate_limiter.is_allowed_by_minute(ip, d))
        self.assertFalse(rate_limiter.is_allowed_by_minute(ip, d))
        self.assertFalse(rate_limiter.is_allowed_by_minute(ip, d))
        d = datetime.datetime.strptime("202501010001", "%Y%m%d%H%M")
        self.assertTrue(rate_limiter.is_allowed_by_minute(ip, d))
        self.assertTrue(rate_limiter.is_allowed_by_minute(ip, d))
        self.assertTrue(rate_limiter.is_allowed_by_minute(ip, d))
        self.assertTrue(rate_limiter.is_allowed_by_minute(ip, d))
        self.assertTrue(rate_limiter.is_allowed_by_minute(ip, d))
        self.assertFalse(rate_limiter.is_allowed_by_minute(ip, d))
        self.assertFalse(rate_limiter.is_allowed_by_minute(ip, d))
        self.assertFalse(rate_limiter.is_allowed_by_minute(ip, d))

    def test_day(self):
        ip = '*******'
        rate_limiter = RateLimiter()
        d = datetime.datetime.strptime("202501010000", "%Y%m%d%H%M")
        for i in range(50):
            self.assertTrue(rate_limiter.is_allowed_by_day(ip, d))
        self.assertFalse(rate_limiter.is_allowed_by_day(ip, d))
        self.assertFalse(rate_limiter.is_allowed_by_day(ip, d))
        d = datetime.datetime.strptime("202501010001", "%Y%m%d%H%M")
        self.assertFalse(rate_limiter.is_allowed_by_day(ip, d))
        d = datetime.datetime.strptime("202501020000", "%Y%m%d%H%M")
        for i in range(50):
            self.assertTrue(rate_limiter.is_allowed_by_day(ip, d))
        self.assertFalse(rate_limiter.is_allowed_by_day(ip, d))
        self.assertFalse(rate_limiter.is_allowed_by_day(ip, d))

    def test_permit_ip_list(self):
        ip = '*******'
        rate_limiter = RateLimiter(['*******'])
        d = datetime.datetime.strptime("202501010000", "%Y%m%d%H%M")
        for i in range(10):
            self.assertTrue(rate_limiter.is_allowed_by_minute(ip, d))
        for i in range(100):
            self.assertTrue(rate_limiter.is_allowed_by_day(ip, d))
        ip = '*******'
        self.assertTrue(rate_limiter.is_allowed_by_minute(ip, d))
        self.assertTrue(rate_limiter.is_allowed_by_minute(ip, d))
        self.assertTrue(rate_limiter.is_allowed_by_minute(ip, d))
        self.assertTrue(rate_limiter.is_allowed_by_minute(ip, d))
        self.assertTrue(rate_limiter.is_allowed_by_minute(ip, d))
        self.assertFalse(rate_limiter.is_allowed_by_minute(ip, d))


if __name__ == '__main__':
    unittest.main()
