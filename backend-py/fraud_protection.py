import re


def replace_buffer(buffer, ng_words):
    res = buffer
    for word, replacement in ng_words.items():
        res = res.replace(word, replacement)
    return res


def is_partial_end_match(buffer, ng_words):
    # normalizeしたものに対して検索を行う
    # 置換はnormalizeを伴わないが，normalizeしてng_wordsということは
    # 悪意を持って入力されたものである可能性が高い
    # -> 検知だけして置換されずに残る -> ハンドリング可能
    normalized_buffer = normalize_text(buffer)
    for word in list(ng_words.keys()):
        if word in normalized_buffer:
            return True
        for i in range(1, min(len(buffer), len(word)) + 1):
            if normalized_buffer[-i:] == word[:i]:
                return True
    return False


def extract_titles(text):
    # 見出しを抽出するための正規表現パターン
    # ##、###、####で始まり、その後に番号とタイトルが続くパターンにマッチ
    pattern = r'^(#{2,3})\s*((?:\d+\.?\d*|\(\d+\))+)\s*(.+)$'
    titles = []
    # テキストを行ごとに処理
    for line in text.split('\n'):
        match = re.match(pattern, line)
        if match:
            number = match.group(2).strip()
            title = match.group(3).replace("#", "").strip()
            # Ensure no spaces between number and title
            full_title = f"{number}{title}"
            titles.append(full_title)
    return titles


def normalize_text(text):
    # Remove all asterisks
    text = re.sub(r'\*+', '', text)
    # Convert full-width spaces to half-width
    text = text.replace('　', ' ')
    # First completely remove all spaces
    text = ''.join(text.split())
    return text.strip()


def filter_tainted_messages(messages):
    keywords_lists = [
        ["申し訳ございません", "インターネットのセキュリティ", "お答えすることができません", "お気軽にお問い合わせください"],
        ["申し訳ございません", "要望に", "対応できません"],
        ["申し訳ございません", "質問に", "回答できません"],
    ]
    fraud_index = 0
    for index, m in enumerate(messages):
        if m["role"] != "assistant":
            continue
        content = m["content"]
        for keywords in keywords_lists:
            count = 0
            for keyword in keywords:
                if keyword in content:
                    count += 1
            if count == len(keywords):
                print(f"Tainted message detected: {content}")
                fraud_index = index + 1
    return messages[fraud_index:]
