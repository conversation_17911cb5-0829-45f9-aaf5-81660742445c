name: DEV - Frontend - Deploy

on:
  push:
    branches: [develop]
    paths:
      - 'frontend/**'

jobs:
  build-and-deploy-frontend:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./frontend

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js with Yarn cache
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "yarn"
          cache-dependency-path: "./frontend/yarn.lock"

      - name: Install dependencies
        run: yarn install --frozen-lockfile --prefer-offline

      - name: Setup gcloud CLI
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.DEV_GCP_SA_KEY }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: dev-net-security-marketing

      - name: Build and Deploy to DEV environment
        run: yarn dev-deploy
