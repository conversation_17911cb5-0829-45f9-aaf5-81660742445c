name: PROD - Backend-Py - Deploy & Upload

on:
  workflow_dispatch:
    inputs:
      action:
        description: 'Action to perform'
        required: true
        default: 'upload_prod'
        type: choice
        options:
          - upload_prod
          - deploy_prod

env:
  PROJECT_ID: net-security-marketing
  REGION: asia-northeast1

jobs:
  upload-prod:
    name: Upload Data to PROD
    runs-on: ubuntu-latest
    if: github.event.inputs.action == 'upload_prod'
    defaults:
      run:
        working-directory: ./backend-py
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup gcloud CLI
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.PROD_GCP_SA_KEY }}
      
      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ env.PROJECT_ID }}
      
      - name: Upload data to PROD
        run: make upload_prod

  deploy-prod:
    name: Deploy to PROD
    runs-on: ubuntu-latest
    if: github.event.inputs.action == 'deploy_prod'
    defaults:
      run:
        working-directory: ./backend-py
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup gcloud CLI
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.PROD_GCP_SA_KEY }}
      
      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ env.PROJECT_ID }}
      
      - name: Deploy to PROD
        run: make deploy_prod
