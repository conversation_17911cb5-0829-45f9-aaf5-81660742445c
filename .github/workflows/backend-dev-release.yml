name: DEV - Backend - Deploy

on:
  push:
    branches: [develop]
    paths:
      - 'backend/**'
  workflow_dispatch:
    
env:
  PROJECT_ID: dev-net-security-marketing

jobs:
  prepare:
    runs-on: ubuntu-latest
    outputs:
      functions: ${{ steps.functions.outputs.list }}
    steps:
      - name: Set functions list
        id: functions
        run: |
          FUNCTIONS_ARRAY='[
            "server",
            "bulk-check-api",
            "watch-hibp-api",
            "fetch-usd-rate",
            "check-email-reservation-subscriber",
            "check-fqdn-reservation-subscriber",
            "dead-letter-subscriber",
            "email-subscriber",
            "hibp-subscriber",
            "impersonation-subscriber",
            "nds-subscriber",
            "password-history-subscriber",
            "pre-check-email-subscriber",
            "pre-check-fqdn-subscriber",
            "prompt-subscriber",
            "static-subscriber",
            "recon-subscriber",
            "site-risk-history-subscriber",
            "slack-message-subscriber",
            "spreadsheet-subscriber"
          ]'
          FUNCTIONS_JSON=$(echo "$FUNCTIONS_ARRAY" | tr -d '\n' | tr -s ' ')
          echo "list=$FUNCTIONS_JSON" >> $GITHUB_OUTPUT

  build:
    needs: prepare
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./backend
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js with Yarn cache
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "yarn"
          cache-dependency-path: "./backend/yarn.lock"

      - name: Install dependencies
        run: yarn install --frozen-lockfile --prefer-offline

      - name: Cache build artifacts
        uses: actions/cache/save@v4
        with:
          path: |
            ./backend/node_modules
            ./backend/.yarn/cache
          key: build-cache-${{ github.sha }}-${{ hashFiles('./backend/yarn.lock') }}

  deploy:
    needs: [prepare, build]
    runs-on: ubuntu-latest
    strategy:
      matrix:
        function: ${{ fromJson(needs.prepare.outputs.functions) }}
      max-parallel: 20
      fail-fast: false
    defaults:
      run:
        working-directory: ./backend
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Restore build cache
        uses: actions/cache/restore@v4
        with:
          path: |
            ./backend/node_modules
            ./backend/.yarn/cache
          key: build-cache-${{ github.sha }}-${{ hashFiles('./backend/yarn.lock') }}

      - name: Setup gcloud CLI
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.DEV_GCP_SA_KEY }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ env.PROJECT_ID }}

      - name: Deploy function
        run: yarn dev-deploy-${{ matrix.function }}
