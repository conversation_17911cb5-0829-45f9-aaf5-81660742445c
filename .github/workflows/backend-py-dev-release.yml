name: DEV - Backend-Py - Deploy & Upload

on:
  push:
    branches: [develop]
    paths:
      - 'backend-py/**'

env:
  PROJECT_ID: dev-net-security-marketing
  REGION: asia-northeast1

jobs:
  deploy-dev:
    name: Deploy and Upload to DEV
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./backend-py
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup gcloud CLI
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.DEV_GCP_SA_KEY }}
      
      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ env.PROJECT_ID }}
      
      - name: Deploy and Upload to DEV
        run: |
          make upload_dev && make deploy_dev
