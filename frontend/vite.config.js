import path from 'path';
import react from '@vitejs/plugin-react';
import { defineConfig } from 'vite';

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  let base = '/';

  if (mode === 'dev') {
    base = 'https://dev-security-api.gmo.jp/';
  } else if (mode === 'prod') {
    base = 'https://security-api.gmo.jp/';
  }

  return {
    plugins: [react()],
    base,
    define: { __BUILD_TIMESTAMP__: Date.now() },
    build: {
      rollupOptions: {
        output: {
          entryFileNames: 'static/js/bundle.js',
          assetFileNames: (assetInfo) => {
            const extType = path.extname(assetInfo.name).substring(1);
            if (extType === 'json') {
              return 'static/locales/[name]-[hash][extname]';
            }
            if (
              extType === 'png'
              || extType === 'jpg'
              || extType === 'jpeg'
              || extType === 'gif'
              || extType === 'svg'
            ) {
              return 'static/img/[name]-[hash][extname]';
            }
            if (extType === 'css') {
              return 'static/css/[name][extname]';
            }
            if (
              extType === 'eot'
              || extType === 'svg'
              || extType === 'ttf'
              || extType === 'woff'
            ) {
              return 'static/fonts/[name]-[hash][extname]';
            }
            return '[name]-[hash][extname]';
          },
        },
      },
    },
    server: { port: 3000 },
  };
});
