$font-family: "Hiragino Kaku Gothic Pro", "ヒラギノ角ゴ Pro W3", <PERSON><PERSON>,
  "メイリオ", sans-serif;

/* 色 */
$color-black: #021725;
$color-white: #fff;
$color-gmo: #005bac;
$color-gray10: #f2f3f4;
$color-gray15: #e6e6e6;
$color-gray20: #dedede;
$color-gray30: #ccc;
$color-gray35: #c8c8c8;
$color-gray40: #acabab;
$color-gray50: #97a3a1;
$color-gray70: #5d5d5d;
$color-gray80: #5a5858;

$color-darkGreen: #0b4d4b;
$color-darkGreen10: #e6edec;
$color-green: #009080;
$color-lightGreen: #06f2b9;
$color-lightGreen10: #dbefee;
$color-yellow: #e5c100;
$color-yellow-20: #e0a300;
$color-blue: #4682b4;
$color-orange: #ffa500;
$color-orange-20: #e59400;
$color-error: #de4400;
$color-error10: #fcece5;
$color-error20: #f9dbce;

/* oss LP */
$color-oss-red: #d22550;
$color-oss-blue: #032273;
$color-oss-green: #209e90;

/* 透過 */
$color-white-alpha60: rgb(255 255 255 / 0.6);
$color-darkGreen-alpha10: rgb(11 77 75 / 0.1);
$color-darkGreen-alpha20: rgb(11 77 75 / 0.2);
$color-darkGreen-alpha5: rgb(11 77 75 / 0.05);
$color-lightGreen-alpha20: rgb(6 242 185 / 0.2);
$color-yellow-alpha10: rgb(229 193 0 / 0.1);
$color-blue-alpha10: rgb(70 130 180 / 0.1);
$color-orange-alpha10: rgb(255 165 0 / 0.1);
$color-green-alpha10: rgb(3 167 129 / 0.1);
$color-green-alpha20: rgb(3 167 129 / 0.2);
$color-black-alpha90: rgb(2 23 37 / 0.9);
$color-black-alpha20: rgb(2 23 37 / 0.2);

/* グラデーション */
$color-gradation: #13d4a4, $color-darkGreen;

/* ■ ブレークポイント
 PC: 1100px ~
 TB: 600px ~ 1099px
SP: 0 ~ 599px */
$window_pc_min: 1100px;
$window_tb_max: 1099px;
$window_tb_min: 600px;
$window_sp_max: 599px;

/* ドロップシャドウ */
$shadow-10: 10px 23px 26px rgb(0 0 0 / 0.04);
$shadow-15: 0 4px 20px rgb(0 0 0 / 0.1);
$shadow-20: 0 0 6px rgb(0 0 0 / 0.16);
$shadow-tooltip: 0 3px 6px rgb(0 0 0 / 0.16);
$shadow-10-green: 0 0 26px rgb(6 242 185 / 0.5);

/* 角丸 */
$border-radius-button: 6px;
$border-radius-button-l: 8px;
$border-radius-card: 10px;
$border-radius-card-half: 5px;
$border-radius-round: 50px;

/* z-index */
$z-index-1: 1;
$z-index-2: 2;
$z-index-modal: 999999999;
