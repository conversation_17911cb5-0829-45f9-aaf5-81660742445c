@charset "utf-8";
/* 呼び出し元のwpコンテンツのresetを使用 */
/*! creativereset.css v0.0.2 | MIT License | creative team at GMO Internet, Inc */

/*

  機能概要
  ==========================================================================
    - デフォルトのスタイルは維持
    - ブラウザごとの異なるスタイルやバグを修正
    - ユーザビリティを改善

  参考
  ==========================================================================
  ■ sanitize.css v6.0.0
      Chrome (last 3)
      Edge (last 3)
      Firefox (last 3)
      Firefox ESR
      Opera (last 3)
      Safari (last 3)
      iOS Safari (last 2)
      Internet Explorer 9+

  ■ normalize.css v8.0.0
      Chrome
      Edge
      Firefox ESR+
      Internet Explorer 10+
      Safari 8+
      Opera
*/

/* ドキュメント（Document）
 * ========================================================================== */

/**
 * box-sizingをすべての要素に適用（全てのブラウザ）
 */

*,
::before,
::after {
  box-sizing: border-box;
}

/**
 * 擬似要素は text-decoration, vertical-alignを継承（全てのブラウザ）
 */
::before,
::after {
  text-decoration: inherit;
  vertical-align: inherit;
}

/**
 * 1. 全てのブラウザでベースのline-heightを統一
 * 2. フォントサイズの調整を防止(Windows PhoneのIE,iOS)
 */

html {
  line-height: 1.15; /* 1 */
  -ms-text-size-adjust: 100%; /* 2 */
  -webkit-text-size-adjust: 100%; /* 2 */
}

/* セクション(Sections)
 * ========================================================================== */

/**
 * マージンを削除（全てのブラウザ）
 */

body {
  margin: 0;
}

/* グループコンテンツ(Grouping content)
 * ========================================================================== */

/**
 * 1. 高を修正(Firefox)
 * 2. オーバーフローを修正(Edge,IE)
 */

hr {
  height: 0; /* 1 */
  overflow: visible; /* 2 */
}

/**
 * displayの正しいプロパティを追加（IE）
 */

main {
  display: block;
}

/* テキストレベル（Text-level semantics）
 * ========================================================================== */

/**
 * 1. アクティブリンク時の背景グレイを削除(IE10)
 * 2. リンク下線のギャップを削除(iOS 8+ and Safari 8+ )
 */

a {
  background-color: transparent; /* 1 */
  -webkit-text-decoration-skip: objects; /* 2 */
}

/**
 * テキストデコレーションを修正（Edge,IE,Opera and Safari）
 */

abbr[title] {
  text-decoration: underline;
  text-decoration: underline dotted;
}

/**
 * フォントウェイトを修正（Chrome, Edge, and Safari）
 */

b,
strong {
  font-weight: bolder;
}

/**
 * 1. フォントサイズの継承とスケーリングを修正（全てのブラウザ）
 * 2. フォントの不規則なサイズを統一（全てのブラウザ）
 */

pre,
code,
kbd,
samp {
  font-family: monospace, monospace; /* 1 */
  font-size: 1em; /* 2 */
}

/**
 * フォントのサイズを修正（全てのブラウザ）
 */

small {
  font-size: 80%;
}

/**
 * subとsup要素が行の高さに与える影響を阻止（全てのブラウザ）
 */

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/* 埋め込みコンテント（Embedded content）
 * ========================================================================== */

/**
 * オーバーフローを修正（IE）
 */

svg:not(:root) {
  overflow: hidden;
}

/* 表形式のデータ（Tabular data）
 * ========================================================================== */

/**
 * テーブルのボーダーを削除
 */

table {
  border-collapse: collapse;
}

/* フォーム（Forms）
 * ========================================================================== */

input {
  border-radius: 0;
}

/**
 * マージンを削除（Firefox,Safari）
 */

button,
input,
optgroup,
select,
textarea {
  margin: 0;
}

/**
 * テキストのトランスフォームの継承を削除（Edge,IE,Firefox）
 */

button,
select {
  text-transform: none;
}

/**
 * クリック可能なタイプをスタイルできない問題を修正（iOS,Safari）
 */

button,
[type='button'],
[type='reset'],
[type='submit'] {
  -webkit-appearance: button;
}

/**
 * 前のルールで設定したフォーカスのスタイルを元に戻す(Firefox)
 */

button:-moz-focusring,
[type='button']:-moz-focusring,
[type='reset']:-moz-focusring,
[type='submit']:-moz-focusring {
  outline: 1px dotted ButtonText;
}

/**
 * インナーボーダー（-moz-focus-inner）とpaddingを削除（Firefox）
 */

button::-moz-focus-inner,
[type='button']::-moz-focus-inner,
[type='reset']::-moz-focus-inner,
[type='submit']::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

/**
 * padding修正（Firefox）.
 */

fieldset {
  padding: 0.35em 0.75em 0.625em;
}

/**
 * 1. テキスト周りを修正（IE）
 * 2. fieldsetからの色の継承を修正（IE）
 */

legend {
  color: inherit; /* 2 */
  display: table; /* 1 */
  max-width: 100%; /* 1 */
  white-space: normal; /* 1 */
}

/**
 * 1. displayの正しいプロパティを追加
 * 2. vertical-alignの正しい値を追加（Chrome, Firefox, and Opera）
 */

progress {
  display: inline-block; /* 1 */
  vertical-align: baseline; /* 2 */
}

/**
 * テキストのトランスフォームの継承を削除（Firefox）
 */

select {
  text-transform: none;
}

/**
 * 1. デフォルトの垂直スクロールバーを削除（IE10+）
 * 2. 縦方向のみリサイズ（すべてのブラウザ）
 */

textarea {
  overflow: auto; /* 1 */
  resize: vertical; /* 2 */
}

/**
 * 1. box-sizingを修正（IE 10-）.
 * 2. paddingを削除（IE 10-）
 */

[type='checkbox'],
[type='radio'] {
  box-sizing: border-box; /* 1 */
  padding: 0; /* 2 */
}

/**
 * 増減ボタンのカーソルのスタイルを修正（Chrome）
 */

[type='number']::-webkit-inner-spin-button,
[type='number']::-webkit-outer-spin-button {
  height: auto;
}

/**
 * 1. 不規則な外観を修正（Chrome,Safari）
 * 2. アウトラインのスタイルを修正（Safari）
 */

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/**
 * 外観を修正（Safari 8）
 */
[type='search']::-webkit-search-cancel-button,
[type='search']::-webkit-search-decoration {
  -webkit-appearance: none;
}

/**
 * 幅を修正（Firefox 36+）
 */

[type='number'] {
  width: auto;
}

/**
 * 内側のパディングを削除（macOSのChrome, Safari）
 */

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/**
 * 1. クリック可能なタイプをスタイルできない問題を修正（iOSとSafari）
 * 2. フォントのプロパティを「inherit」に変更（Safari）
 */

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/* インタラクティブ（Interactive）
 * ========================================================================== */

/*
 * displayの正しいプロパティを追加（Edge,IE）
 */

details {
  display: block;
}

/*
 * 正しいプロパティを追加（Edge,IE,Safari）
 */

dialog {
  background-color: white;
  border: solid;
  color: black;
  display: block;
  height: -moz-fit-content;
  height: -webkit-fit-content;
  height: fit-content;
  left: 0;
  margin: auto;
  padding: 1em;
  position: absolute;
  right: 0;
  width: -moz-fit-content;
  width: -webkit-fit-content;
  width: fit-content;
}

dialog:not([open]) {
  display: none;
}

/*
 * displayの正しいプロパティを追加（全てのブラウザ）
 */

summary {
  display: list-item;
}

/*
 * displayの正しいプロパティを追加（IE 10-）
 */

[hidden] {
  display: none;
}

/* スクリプト制御される要素（Scripting）
 * ========================================================================== */

/**
 * displayの正しいプロパティを追加（IE 9-）
 */

canvas {
  display: inline-block;
}

/**
 * displayの正しいプロパティを追加（IE）
 */

template {
  display: none;
}

/* ユーザーインタラクション（User interaction）
 * ========================================================================== */

/*
 * 1. タップ反応の遅延を削除（IE10）
 * 2. クリック可能な要素のタップ反応の遅延を削除（全てのブラウザ）
 */

a,
area,
button,
input,
label,
select,
summary,
textarea,
[tabindex] {
  -ms-touch-action: manipulation; /* 1 */
  touch-action: manipulation; /* 2 */
}
