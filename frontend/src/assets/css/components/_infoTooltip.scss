@charset "utf-8";

@use "../global/variables" as *;
@use "../global/mixin" as *;

.c-infoTooltip {
  position: relative;
  &:hover {
    .c-infoTooltip__content {
      opacity: 1;
      visibility: visible;
      bottom: 28px;
    }
  }
  &__icon {
    padding: 2px;
    cursor: pointer;
  }
  &__content {
    width: 320px;
    font-size: 10px;
    padding: 8px 10px;
    color: $color-white;
    background-color: $color-black-alpha90;
    border-radius: $border-radius-card;
    position: relative;
    position: absolute;
    left: 50%;
    bottom: 40px;
    transform: translateX(-50%);
    white-space: pre-wrap; // 表示テキストは改行を含む
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease 0s;
    box-shadow: $shadow-tooltip;
    &::after {
      content: "";
      position: absolute;
      bottom: -15px;
      left: 50%;
      transform: translateX(-50%);
      border-width: 8px;
      border-style: solid;
      border-color: $color-black-alpha90 transparent transparent transparent;
    }
  }
}

/* sp style */
@include for-device("sp") {
  .c-infoTooltip {
    &__content {
      width: 260px;
      left: inherit;
      right: -32px;
      transform: inherit;
      &::after {
        left: inherit;
        transform: inherit;
        right: 32px;
      }
    }
  }
}
