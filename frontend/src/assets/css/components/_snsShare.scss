@use "../global/variables" as *;
@use "../global/mixin" as *;

.c-snsShare {
  &__title {
    font-size: 14px;
    display: flex;
    align-items: center;
    &::before {
      content: "";
      height: 1px;
      flex-grow: 1;
      background-color: $color-black;
      margin-right: 1em;
      margin-left: 10px;
    }
    &::after {
      content: "";
      height: 1px;
      flex-grow: 1;
      background-color: $color-black;
      margin-left: 1em;
      margin-right: 10px;
    }
  }

  &__list {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
  }

  &__button {
    width: 40px;
    height: 40px;
    transition: opacity 0.3s ease;

    &:hover {
      opacity: 0.8;
    }
  }
  /* margin */
  .c-snsShare__title + .c-snsShare__list {
    margin-top: 20px;
  }
}
