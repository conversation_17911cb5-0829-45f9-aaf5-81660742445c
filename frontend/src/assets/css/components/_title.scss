@charset "utf-8";

@use "../global/variables" as *;
@use "../global/mixin" as *;

/* section title */
.c-title {
  font-size: 48px;
  line-height: 1.3;
  color: $color-black;
  padding: 80px 0;
  text-align: center;
  &--white {
    padding: 80px 0 32px;
    color: $color-white;
  }
  &--pdb20 {
    padding: 80px 0 20px;
  }
  &--pdt0 {
    padding: 0 0 32px;
  }
  &__sub {
    font-size: 18px;
    margin-top: 2px;
    color: $color-darkGreen;
    display: block;
    &--white {
      color: $color-white;
      opacity: 0.5;
    }
  }
  &__subhead {
    font-size: 30px;
    display: block;
    margin-bottom: 12px;
  }
}
/* sp style */
@include for-device("sp") {
  .c-title {
    font-size: 30px;
    padding: 60px 0 40px;
    span {
      display: block;
    }
    &--white {
      padding: 50px 0 30px;
    }
    &--pdb20 {
      padding: 60px 0 20px;
    }
    &__sub {
      font-size: 10px;
      margin-top: 15px;
    }
    &__subhead {
      font-size: 20px;
      margin-bottom: 4px;
    }
  }
}
