@charset "utf-8";

@use "../global/variables" as *;
@use "../global/mixin" as *;
@use "../global/animation" as *;

.c-periodicCheck {
  background-color: $color-white;
  border-radius: $border-radius-card;
  border: 1px solid $color-darkGreen;
  margin-top: 20px;
  margin-bottom: 20px;
  &__head {
    padding: 20px;
  }
  &__textWrap {
    display: flex;
    column-gap: 8px;
  }
  &__icon {
    margin-top: 2px;
  }
  &__text {
    font-weight: 600;
    font-size: 16px;
    color: $color-darkGreen;
  }
  &__list {
    display: inline-flex;
    align-items: center;
    column-gap: 8px;
  }
  &__date {
    background-color: $color-darkGreen-alpha5;
    padding: 10px;
    font-size: 12px;
    color: $color-darkGreen;
    text-align: center;
  }
}
.c-periodicCheckElement {
  padding: 4px 8px;
  border: 1px solid $color-gray20;
  border-radius: $border-radius-card-half;
  display: flex;
  align-items: center;
  column-gap: 10px;
  & span {
    font-size: 14px;
  }
}
/* margin */
.c-periodicCheck__textWrap + .c-periodicCheck__list {
  margin-top: 10px;
}

/* sp style */
@include for-device("sp") {
  .c-periodicCheck {
    &__list {
      display: grid;
      row-gap: 10px;
    }
  }
  .c-periodicCheckElement {
    & span {
      margin-right: auto;
    }
  }
}
