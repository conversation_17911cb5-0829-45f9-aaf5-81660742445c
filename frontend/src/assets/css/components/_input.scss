@charset "utf-8";

@use "../global/variables" as *;
@use "../global/mixin" as *;

/* 共通 */
.c-input {
  &__label {
    font-size: 12px;
  }
  &__required {
    font-size: 10px;
    color: $color-darkGreen;
    vertical-align: top;
  }
  &__input {
    padding: 16px 20px;
    border: 1px solid $color-gray20;
    border-radius: $border-radius-card;
    &::placeholder {
      color: rgb(0 0 0 / 20%);
    }
    &:focus {
      border-color: $color-lightGreen;
    }
  }
  &__error {
    color: $color-error;
    font-size: 12px;
  }
}
/* チェックボックス */
.c-inputCheckBox {
  appearance: none;
  -webkit-appearance: none;
  height: 20px;
  width: 20px;
  border: 1px solid $color-darkGreen;
  cursor: pointer;
  &:checked {
    &::before {
      content: "";
      display: block;
      position: relative;
      left: 5px;
      top: 1px;
      width: 8px;
      height: 12px;
      border: solid $color-darkGreen;
      border-width: 0 3px 3px 0;
      transform: rotate(45deg);
    }
  }
  &:focus-visible {
    outline: 2px solid #1d9bf0;
    outline-offset: 2px;
  }
}
.c-inputCheckBoxList {
  &__text {
    margin-bottom: 16px;
  }
  &__box {
    display: flex;
    flex-wrap: wrap;
    column-gap: 24px;
    & label {
      display: flex;
      align-items: center;
      column-gap: 6px;
      cursor: pointer;
    }
  }
}
.c-inputForm {
  &__column {
    display: grid;
    grid-template-columns: repeat(2, 49%);
    column-gap: 2%;
  }
  &__box {
    display: flex;
    flex-direction: column;
    row-gap: 8px;
  }
  &__columnBox {
    display: flex;
    flex-direction: column;
    row-gap: 8px;
  }
  &__button {
    width: 240px;
    margin: 40px auto 0;
  }
  &__note {
    font-size: 12px;
    color: $color-gray80;
    margin-top: 20px;
    margin-inline: auto;
    max-inline-size: max-content;
  }
}
/* 検索入力 */
.c-inputSearch {
  position: relative;
  &--brandTld {
    border-radius: 40px;
    box-shadow: $shadow-15;
  }
  &__input {
    width: 100%;
    font-size: 16px;
    padding: 12px 160px 12px 32px;
    border: 0;
    border-radius: 40px;
    transition: border-color 0.3s;
    &::placeholder {
      color: rgb(0 0 0 / 20%);
    }
    &--border {
      padding: 29px 160px 29px 32px;
      border: 1px solid $color-gray35;
      box-shadow: $shadow-15;
    }
    &--borderSizeM {
      padding: 16px 160px 16px 40px;
      border: 1px solid $color-gray35;
    }
  }
  &__error {
    font-size: 12px;
    color: $color-error;
    margin-top: 16px;
    max-inline-size: max-content;
    margin-inline: auto;
  }
  &__button {
    position: absolute;
    top: 50%;
    right: 16px;
    transform: translateY(-50%);
    &--sizeM {
      right: 10px;
    }
  }
}
/* セキュリティトップ検索入力 */
.c-inputSearchHome {
  &__input {
    width: 100%;
    font-size: 16px;
    padding: 12px 32px;
    border: 0;
    border-radius: 40px;
    transition: border-color 0.3s;
    &::placeholder {
      color: rgb(0 0 0 / 20%);
    }
  }
}
/* margin */
.c-inputForm__column + .c-inputForm__box,
.c-inputForm__box + .c-inputForm__column {
  margin-top: 20px;
}
.c-panel__info + .c-inputForm {
  margin-top: 20px;
}
.c-inputForm__box + .c-inputForm__box {
  margin-top: 20px;
}
/* sp style */
@include for-device("sp") {
  .c-input {
    &__input {
      padding: 10px 16px;
    }
  }
  .c-inputCheckBoxList {
    &__box {
      row-gap: 12px;
    }
  }
  .c-inputForm {
    &__column {
      display: grid;
      grid-template-columns: repeat(2, 48%);
      column-gap: 4%;
    }
    &__button {
      display: block;
      width: 100%;
    }
  }
  .c-inputSearch {
    &__input {
      font-size: 16px;
      padding: 12px 64px 12px 24px;
      &::placeholder {
        font-size: 12px;
      }
      &--border {
        padding: 24px 64px 24px 24px;
      }
      &--borderSizeM {
        padding: 16px 64px 16px 24px;
      }
    }
    &__button {
      width: 46px;
      right: 16px;
      &--sizeM {
        width: 40px;
        right: 10px;
      }
    }
    &__buttonText {
      display: none;
    }
  }
  .c-inputSearchHome {
    &__input {
      font-size: 16px;
      padding: 12px 24px;
      &::placeholder {
        font-size: 12px;
      }
    }
  }
}
