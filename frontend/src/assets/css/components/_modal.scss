@charset "utf-8";

@use "../global/variables" as *;
@use "../global/mixin" as *;

.c-modal {
  position: fixed;
  left: 0;
  top: 0;
  display: flex;
  width: 100%;
  height: 100%;
  overflow: auto;
  justify-content: center;
  align-items: center;
  padding: 20px;
  z-index: $z-index-modal;
  &__overlay {
    position: absolute;
    inset: 0;
    background-color: $color-black-alpha20;
  }
  &__content {
    position: relative;
    padding: 40px 10px;
    border-radius: $border-radius-card;
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 460px;
    max-height: 90vh;
    overflow: hidden;
    background-color: $color-white;
    box-shadow: $shadow-10;
    animation: animation-fadeIn 0.3s ease;
  }
  &__close {
    position: absolute;
    top: 20px;
    right: 20px;
  }
  &__close-container {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1000;
  }
}

/* modal内容 */
.c-modalBox {
  padding: 0 10px;
  overflow-y: auto;
  scrollbar-color: $color-gray30 $color-gray10;
  scrollbar-width: auto;
  &::-webkit-scrollbar-thumb {
    background: $color-gray30;
    width: 8px;
    border-radius: 4px;
  }
  &::-webkit-scrollbar {
    width: 10px;
    background-color: $color-gray10;
  }
  &__img {
    width: 120px;
    height: auto;
    margin: 0 auto;
  }
  &__title {
    font-size: 20px;
    font-weight: 600;
    color: $color-darkGreen;
    text-align: center;
  }
  &__text {
    text-align: center;
    white-space: pre-line;
  }
  &__button {
    width: 240px;
    margin: 40px auto 0;
  }
}

/* margin */
.c-modalBox__img + .c-modalBox__title {
  margin-top: 20px;
}
.c-modalBox__title + .c-modalBox__text {
  margin-top: 12px;
}
.c-modalBox__title + .c-modalBox__form,
.c-modalBox__title + .c-modalBox__item {
  margin-top: 20px;
}
.c-modalBox__item + .c-modalBox__text {
  margin-top: 20px;
}

/* sp style */
@include for-device("sp") {
  .c-modal {
    &__content {
      padding: 30px 10px;
      border-radius: 8px;
      max-height: 84vh;
    }
    &__close {
      top: 10px;
      right: 10px;
    }
  }
  .c-modalBox {
    &__title {
      font-size: 16px;
    }
    &__text {
      font-size: 14px;
    }
    &__button {
      display: block;
      width: 100%;
    }
  }
  /* margin */
  .c-modalBox__title + .c-modalBox__text {
    margin-top: 4px;
  }
}
