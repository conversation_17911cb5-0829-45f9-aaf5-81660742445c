@charset "utf-8";

@use '../global/variables' as *;

.p-chatMarkDown {
  font-size: 16px;
  line-height: 1.5;
  white-space: normal;
  overflow-wrap: anywhere;
  word-break: break-word;
  & h1 {
    font-size: 26px;
    line-height: 34px;
    font-weight: 600;
    &:not(:only-child) {
      margin-bottom: 1rem;
    }
    &:not(:first-child) {
      margin-top: 2rem;
    }
  }
  & h2 {
    font-size: 22px;
    line-height: 30px;
    font-weight: 600;
    &:not(:only-child) {
      margin-bottom: 1rem;
    }
    &:not(:first-child) {
      margin-top: 2rem;
    }
  }
  & h3 {
    font-size: 20px;
    line-height: 28px;
    font-weight: 600;
    &:not(:only-child) {
      margin-bottom: 1rem;
    }
    &:not(:first-child) {
      margin-top: 1rem;
    }
  }
  & h4 {
    font-size: 18px;
    line-height: 26px;
    font-weight: 600;
    &:not(:only-child) {
      margin-bottom: 0.75rem;
    }
    &:not(:first-child) {
      margin-top: 1rem;
    }
  }
  & h5 {
    font-size: 16px;
    line-height: 24px;
    font-weight: 600;
    &:not(:only-child) {
      margin-bottom: 0.5rem;
    }
    &:not(:first-child) {
      margin-top: 0.5rem;
    }
  }
  & h6 {
    font-size: 14px;
    line-height: 22px;
    font-weight: 600;
    &:not(:only-child) {
      margin-bottom: 0.5rem;
    }
    &:not(:first-child) {
      margin-top: 0.5rem;
    }
  }
  & p {
    &:not(:only-child) {
      margin-bottom: 1rem;
    }
    &:not(:first-child) {
      margin-top: 1rem;
    }
    & + :where(ol, ul) {
      margin-top: 0;
    }
  }
  & strong {
    font-weight: 600;
  }
  & img {
    max-height: 400px;
  }
  & ol {
    list-style-type: decimal;
    padding-left: 1.625em;
    margin-bottom: 1.25em;
    margin-top: 1.25em;
  }
  & ul {
    list-style: disc;
    padding-left: 1.625em;
    margin-bottom: 1.25em;
    margin-top: 1.25em;
  }
  & li {
    margin-bottom: 0.5em;
    margin-top: 0.5em;
  }
  & table {
    font-size: 14px;
    line-height: 22px;
    margin-bottom: 0.25rem;
    margin-top: 0.25rem;
    display: block;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 5px;
    border-left: 1px solid $color-gray20;
    overflow-x: auto;

    & th,
    td {
      font-size: 14px;
      line-height: 22px;
      text-align: left;
      min-width: 80px;
      padding: 0.25rem 0.5rem;
      border-bottom: 1px solid $color-gray20;
    }
    & th {
      background-color: $color-gray10;
      border-top: 1px solid $color-gray20;
      &:first-child {
        border-top-left-radius: 5px;
      }

      &:last-child {
        border-top-right-radius: 5px;
        border-right: 1px solid $color-gray20;
      }
      & + th {
        border-left: 1px solid $color-gray20;
      }
    }

    & td {
      background-color: $color-white-alpha60;
      & + td {
        border-left: 1px solid $color-gray20;
      }
      &:last-child {
        border-right: 1px solid $color-gray20;
      }
    }
    & tr:last-child td:first-child {
      border-bottom-left-radius: 5px;
    }
    & tr:last-child td:last-child {
      border-bottom-right-radius: 5px;
    }
  }
  & pre,
  samp {
    font-size: 14px;
    line-height: 22px;
    margin-top: 0.5rem;
    padding: 1rem;
    background-color: $color-gray10;
    border: 1px solid $color-gray20;
    border-radius: 5px;
    overflow-x: auto;
    width: 100%;
  }
  & code {
    font-size: 14px;
    line-height: 22px;
    font-weight: 500;
    padding: 0.15rem 0.3rem;
    background-color: $color-gray10;
    border-radius: 5px;
    overflow-x: auto;
  }
  & kbd {
    font-size: 14px;
    line-height: 22px;
    background-color: $color-gray10;
    border: 1px solid $color-gray20;
    padding: 0.15rem 0.3rem;
    border-radius: 5px;
  }
  & blockquote {
    font-size: 14px;
    line-height: 22px;
    border-left: 2px solid $color-gray20;
    padding-left: 1rem;
    margin: 0;
    padding-bottom: 0.5rem;
    padding-top: 0.5rem;
    overflow-x: auto;
    & p {
      font-size: 14px;
      line-height: 22px;
      margin: 0;
    }
  }
  & hr {
    border: none;
    height: 1px;
    background-color: $color-gray20;
    margin-top: 40px;
    margin-bottom: 40px;
  }
}
/* リストの調整 */
.p-chatMarkDown :where(ul > li),
.p-chatMarkDown :where(ol > li) {
  padding-left: 0.375em;
}
.p-chatMarkDown :where(ol, ul) > li > :first-child {
  margin-bottom: 0;
  margin-top: 0;
}
/* テキストリンク */
.p-chatMarkDown a {
  color: $color-gmo;
  @media (min-width: $window_tb_min) {
    &:hover {
      opacity: 0.8;
    }
  }
}
