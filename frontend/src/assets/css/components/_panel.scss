@charset "utf-8";

@use "../global/variables" as *;
@use "../global/mixin" as *;
@use "../global/animation" as *;

.c-panel {
  padding: 40px;
  background-color: $color-white;
  border-radius: $border-radius-card;
  box-shadow: $shadow-10;
  &--s {
    width: 800px;
    margin: 0 auto;
  }
  &--tab {
    border-radius: 0 0 $border-radius-card $border-radius-card;
  }
  &--upper {
    border-radius: $border-radius-card $border-radius-card 0 0;
  }
  &--bottom {
    padding: 32px 40px 40px;
    border-radius: 0 0 $border-radius-card $border-radius-card;
    border-top: 1px solid $color-gray15;
  }
  &__title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 40px;
    display: flex;
    column-gap: 12px;
    &--error {
      justify-content: center;
    }
    &--darkGreen {
      color: $color-darkGreen;
      justify-content: center;
    }
  }
  &__info {
    color: $color-darkGreen;
    background-color: $color-darkGreen-alpha5;
    border-radius: $border-radius-card;
    padding: 20px;
  }
  &__subTitle {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 20px;
  }
  &__content {
    display: grid;
    grid-template-columns: 240px 1fr;
    column-gap: 40px;
  }
  &__side {
    display: flex;
    row-gap: 20px;
    flex-direction: column;
  }
  &__rankImg {
    width: 160px;
    margin: 0 auto;
  }
  &__sideButton {
    &--pcNone {
      display: none;
    }
  }
  &__sideButtonText {
    font-size: 14px;
    display: flex;
    align-items: center;
    &::before {
      content: "";
      height: 1px;
      flex-grow: 1;
      background-color: $color-black;
      margin-right: 1em;
      margin-left: 10px;
    }
    &::after {
      content: "";
      height: 1px;
      flex-grow: 1;
      background-color: $color-black;
      margin-left: 1em;
      margin-right: 10px;
    }
  }
  &__snsShare {
    &--pcNone {
      display: none;
    }
  }
  &__button {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  &__body {
    margin-top: 40px;
  }
  &__text {
    margin-inline: auto;
    max-inline-size: max-content;
  }
  &__img {
    display: flex;
    justify-content: center;
    & img {
      width: 240px;
      height: 240px;
    }
  }
  &__emailAddress {
    display: inline-flex;
    align-items: center;
    background-color: $color-gray10;
    padding: 2px 12px;
    border-radius: $border-radius-card;
    margin: 0 4px;
  }
  &__note {
    font-size: 28px;
    font-weight: 600;
    padding: 8px 32px;
    border-radius: $border-radius-card;
    color: $color-white;
    background-color: $color-green;
    &--warning {
      background-color: $color-orange;
    }
    &--C {
      background-color: $color-orange;
    }
    &--D {
      background-color: $color-orange;
    }
    &--impB {
      background-color: $color-orange;
    }
    &--alert {
      background-color: $color-error;
    }
    &--E {
      background-color: $color-error;
    }
    &--impC {
      background-color: $color-error;
    }
    &--impD {
      background-color: $color-error;
    }
  }
  &__annotation {
    font-size: 12px;
    color: $color-gray80;
    margin-top: 16px;
    &--xs {
      font-size: 10px;
      margin-top: 40px;
    }
    &--center {
      margin-inline: auto;
      max-inline-size: max-content;
    }
    &--light {
      color: $color-gray50;
    }
  }
  &__result {
    font-size: 16px;
    font-weight: 600;
    margin-top: 32px;
    margin-bottom: 32px;
    &--note {
      font-size: 12px;
      font-weight: 300;
    }
  }
  &__sideButton {
    margin-top: 12px;
  }
  &__error {
    color: $color-error;
    background-color: $color-error10;
    padding: 20px;
    border-radius: $border-radius-card;
    display: flex;
    column-gap: 8px;
    & span {
      margin-top: 4px;
    }
  }
}
.c-panelList {
  display: grid;
  border: 1px solid $color-gray20;
  border-radius: $border-radius-card;
  & li + li {
    border-top: 1px solid $color-gray20;
  }
  &__noteWrap {
    &--center {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      padding-bottom: 80px;
    }
  }
  &__note {
    font-size: 14px;
    display: flex;
    column-gap: 8px;
    align-items: flex-start;
    & span {
      margin-top: 4px;
    }
  }
}
.c-panelBox {
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  background-color: $color-gray10;
  border-radius: $border-radius-card;
  padding: 20px;
  &__item {
    dt {
      font-size: 12px;
    }
    dd {
      font-weight: 600;
    }
  }
  &--border {
    border: 1px solid $color-gray20;
    background-color: $color-white;
    & dt {
      font-size: 16px;
      font-weight: 600;
    }
    & dd {
      font-size: 12px;
      font-weight: 300;
      color: $color-darkGreen;
    }
  }
}
/* margin */
.c-panel__info + .c-panel__subTitle {
  margin-top: 20px;
}
.c-panel__head + .c-panel__body {
  margin-top: 40px;
}
.c-panelSummary + .c-panelList {
  margin-top: 20px;
}
.c-panel__img + .c-panel__text {
  margin-top: 40px;
}
.c-panelList + .c-panelList__noteWrap {
  margin-top: 20px;
}
.c-panelList__noteWrap + .c-panelList__noteWrap {
  margin-top: 20px;
}
.p-verify__agreement + .c-panel__error {
  margin-top: 20px;
}
.c-panel__sideButtonText + .c-buttonAccent {
  margin-top: 20px;
}
.c-panelBox + .c-panelBox {
  margin-top: 20px;
}
.c-panel__sideButton + .c-panel__snsShare,
.c-panel__annotation + .c-panel__snsShare,
.c-panel__content + .c-panel__snsShare {
  margin-top: 32px;
}
.c-panel__sideButton > a + button {
  margin-top: 20px;
}

/* sp style */
@include for-device("sp") {
  .c-panel {
    padding: 40px 15px;
    &--s {
      width: 100%;
    }
    &--tab {
      border-radius: $border-radius-card;
    }
    &--bottom {
      padding: 32px 15px 40px;
    }
    &__title {
      margin-top: 20px;
      margin-bottom: 20px;
      &--error {
        font-size: 20px;
      }
    }
    &__info {
      padding: 20px 15px;
    }
    &__subTitle {
      font-size: 20px;
      font-weight: 600;
    }
    &__content {
      grid-template-columns: inherit;
      row-gap: 40px;
      &__side {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
      }
      &--gap20 {
        row-gap: 20px;
      }
    }
    &__sideButton {
      margin-top: 10px;
      &--bottom {
        margin-top: 40px;
        margin-bottom: 20px;
      }
      &--pcNone {
        display: block;
      }
      &--spNone {
        display: none;
      }
    }
    &__snsShare {
      &--pcNone {
        display: block;
      }
      &--spNone {
        display: none;
      }
    }
    &__img {
      & img {
        width: 160px;
        height: 160px;
      }
    }
    &__text {
      font-size: 12px;
    }
    &__error {
      font-size: 14px;
      padding: 8px 10px;
    }
    &__note {
      font-size: 24px;
      text-align: center;
      padding: 8px 16px;
    }
    &__annotation {
      &--xs {
        margin-top: 20px;
      }
    }
    &__result {
      margin-top: 20px;
      margin-bottom: 20px;
    }
    &__textBreak {
      display: block;
    }
  }
  .c-panelBox {
    grid-template-columns: inherit;
    row-gap: 10px;
    padding: 20px;
    justify-content: flex-start;
  }
  .c-panelList {
    &__noteWrap {
      &--center {
        padding-bottom: 0;
        height: auto;
      }
    }
    &__note {
      column-gap: 4px;
    }
  }
  /* margin */
  .c-panel__head + .c-panel__body {
    margin-top: 20px;
  }
  .c-panel__img + .c-panel__text {
    margin-top: 20px;
  }
}
