@charset "utf-8";

@use '../global/variables' as *;

.c-toggleSwitch {
  position: relative;
  display: inline-block;
  width: 26px;
  height: 14px;
  &__input {
    display: none;
  }
  &__label {
    display: block;
    width: 100%;
    height: 100%;
    border-radius: 7px;
    position: relative;
    cursor: pointer;
    transition: background-color 0.3s;
    &::before {
      content: '';
      position: absolute;
      width: 12px;
      height: 12px;
      background: $color-white;
      border-radius: 50%;
      top: 50%;
      transform: translateY(-50%);
      transition: 0.3s;
    }
    &--on {
      background-color: $color-darkGreen;
      &::before {
        left: 13px;
      }
    }
    &--off {
      background-color: $color-gray20;
      &::before {
        left: 1px;
      }
    }
  }
}
