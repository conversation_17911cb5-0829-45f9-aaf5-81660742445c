@charset "utf-8";

@use '../global/variables' as *;

.c-dropDownList {
  position: relative;
  &__select {
    position: relative;
    font-family: initial;
    font-size: 12px;
    background-color: $color-white;
    border: 1px solid $color-darkGreen;
    padding: 2px 24px 2px 10px;
    border-radius: $border-radius-card-half;
    &::before {
      position: absolute;
      top: 50%;
      right: 8px;
      transform: translateY(-50%);
      content: '';
      display: inline-block;
      vertical-align: middle;
      color: $color-darkGreen;
      line-height: 1;
      width: 6px;
      height: 6px;
      border: 1px solid $color-darkGreen;
      border-left: 0;
      border-bottom: 0;
      box-sizing: border-box;
      transform: translateY(-60%) rotate(135deg);
      transition: transform 0.3s;
    }
    &--show {
      &::before {
        transform: translateY(-35%) rotate(-45deg);
      }
    }
    &:disabled {
      pointer-events: none;
      opacity: 0.3;
    }
  }
  &__optionOverlay {
    position: fixed;
    inset: 0;
    width: 100vw;
    height: 100vh;
  }
  &__option {
    position: absolute;
    top: 24px;
    left: 0;
    width: 100%;
    margin-top: 4px;
    background-color: $color-white;
    border-radius: $border-radius-card-half;
    overflow: hidden;
    border: 1px solid $color-darkGreen;
    cursor: pointer;
    z-index: $z-index-1;
    & li {
      font-size: 12px;
      padding: 2px 8px;
      &:hover {
        background-color: $color-gray10;
      }
    }
    &:checked {
      background-color: $color-gray10;
    }
  }
}
