@charset "utf-8";

@use "../global/variables" as *;
@use "../global/mixin" as *;

.c-snackbar {
  display: grid;
  grid-template-columns: 20px 1fr;
  column-gap: 10px;
  color: $color-white;
  &__icon {
    margin-top: 3px;
  }
}
/* デフォルトのcss上書き */
.Toastify {
  &__toast-container {
    z-index: $z-index-2;
    &--top-right {
      top: 130px;
    }
  }
  &__toast {
    background: $color-black-alpha90;
    border-radius: $border-radius-card;
    padding: 12px 16px;
    min-height: 56px;
    margin-bottom: 10px;
  }
  &__close-button {
    &--light {
      color: $color-white;
      opacity: 0;
    }
  }
}
/* sp style */
@include for-device("sp") {
  /* デフォルトのcss上書き */
  .Toastify {
    &__toast-container {
      padding: 15px;
      &--top-right {
        top: 60px;
      }
    }
  }
}
