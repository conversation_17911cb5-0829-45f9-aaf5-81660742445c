@charset "utf-8";

@use "../global/variables" as *;
@use "../global/mixin" as *;

.c-contact {
  margin: 0 auto;
  &__text {
    font-size: 16px;
    text-align: center;
    color: $color-darkGreen;
    &--color-lightGreen {
      color: $color-lightGreen;
    }
  }
  &__form {
    margin-top: 40px;
    margin-bottom: 64px;
  }
  &__note {
    font-size: 12px;
    color: $color-white;
    margin-top: 20px;
    margin-bottom: 20px;
    margin-inline: auto;
    max-inline-size: max-content;
  }
}

/* sp style */
@include for-device("sp") {
  .c-contact {
    font-size: 16px;
    &__text {
      font-size: 10px;
    }
    &__form {
      margin-bottom: 40px;
    }
  }
}
