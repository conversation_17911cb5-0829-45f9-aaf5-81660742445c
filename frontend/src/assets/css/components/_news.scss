@charset "utf-8";

@use "../global/variables" as *;
@use "../global/mixin" as *;

.c-news {
  &__linkBox {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  &__sliderBox {
    width: 100%;
  }
}
.c-newsLink {
  position: relative;
  display: flex;
  align-items: center;
  text-align: center;
  font-size: 16px;
  color: $color-green;
  padding: 0 32px;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    right: 10px;
    width: 8px;
    height: 8px;
    margin: auto;
    border-top: 1px solid $color-green;
    border-right: 1px solid $color-green;
    transform: rotate(45deg);
  }
  @media (min-width: $window_tb_min) {
    &:hover {
      color: $color-green;
      text-decoration: none;
      opacity: 0.8;
    }
  }
  /* commonのaタグのdecoration指定消す */
  &:hover {
    text-decoration: none;
  }
  &:visited {
    color: $color-green;
    text-decoration: none;
  }
  &:focus {
    text-decoration: none;
  }
  &__icon {
    margin-right: 10px;
  }
  &__textOmit {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-all;
  }
  &__textEm {
    font-weight: 600;
  }
}

/* margin */
.c-news__linkBox + .c-news__sliderBox {
  margin-top: 16px;
}
.c-newsLink + .c-newsLink {
  margin-top: 16px;
}

/* sp style */
@include for-device("sp") {
  .c-newsLink {
    &__textOmit {
      -webkit-line-clamp: 2;
      line-clamp: 2;
    }
    &__pageLink {
      padding: 0 20px 0 0;
      text-align: center;
      &::before {
        right: 4px;
      }
    }
    &__textBreak {
      display: block;
    }
  }
}
