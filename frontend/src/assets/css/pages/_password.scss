@charset "utf-8";

@use "../global/variables" as *;
@use "../global/mixin" as *;

.p-passwordResult {
  &__summary {
    display: grid;
    justify-content: center;
    row-gap: 4px;
    margin-top: 30px;
  }
  &__detail {
    display: grid;
    margin-top: 30px;
  }
  &__count {
    font-size: 24px;
    font-weight: 600;
    text-align: center;
    &--error {
      color: $color-error;
    }
    &--safe {
      color: $color-green;
    }
  }
  &__note {
    background-color: $color-error10;
    border-radius: $border-radius-card;
    padding: 20px;
    &--pcNone {
      display: none;
    }
  }
  &__annotation {
    font-size: 10px;
    color: $color-gray80;
    text-align: center;
  }
}
.p-passwordResultNote {
  & dt {
    font-size: 12px;
    font-weight: 600;
    color: $color-error;
  }
  & li {
    font-size: 12px;
    font-weight: 300;
    color: $color-black;
    text-indent: -1em;
    padding-left: 1em;
  }
  &--normal {
    & dt {
      color: $color-black;
    }
    & dd {
      font-size: 12px;
    }
  }
}
.p-passwordDetailWrap {
  overflow-y: auto;
  max-height: 378px;
}
.p-passwordDetail {
  padding: 20px;
  &__title {
    font-weight: 600;
  }
  &__date {
    font-size: 12px;
    font-weight: 300;
  }
}
.c-passwordSite + .c-passwordSiteDate {
  margin-top: 4px;
}

.p-passwordResultNote + .p-passwordResultNote {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid $color-error20;
}
/* sp style */
@include for-device("sp") {
  .p-passwordResult {
    &__summary {
      p {
        font-size: 12px;
      }
    }
    &__note {
      &--pcNone {
        display: block;
        margin-bottom: 20px;
      }
      &--spNone {
        display: none;
      }
    }
    &__annotation {
      & span {
        display: block;
      }
    }
  }
}
