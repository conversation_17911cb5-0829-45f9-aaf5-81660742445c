@charset "utf-8";

@use "../global/variables" as *;
@use "../global/mixin" as *;

.p-home {
  background: $color-white;
  &__inner {
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
    padding: 144px 20px 40px;
  }
  &__header {
    & img {
      width: auto;
      height: 129px;
    }
  }
  &__text {
    font-size: 20px;
    font-weight: 600;
    margin-inline: auto;
    max-inline-size: max-content;
    margin-top: 64px;
  }
  &__tabs {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    padding: 10px 16px 0;
  }
  &__content {
    transition: opacity 0.3s;
    opacity: 0;
    &--active {
      transition: opacity 0.3s;
      opacity: 1;
    }
  }
  &__link {
    font-size: 12px;
    color: $color-gray50;
    margin-inline: auto;
    max-inline-size: max-content;
    & a {
      font-size: inherit;
      color: inherit;
      text-decoration: underline;
      transition: color 0.3s;
      @media (min-width: $window_tb_min) {
        &:hover {
          color: $color-lightGreen;
        }
      }
    }
  }
  &__error {
    font-size: 12px;
    color: $color-error;
    padding: 0 32px;
  }
  &__search {
    max-width: 820px;
    margin: 20px auto 0;
    border: 1px solid $color-gray35;
    border-radius: 40px;
    box-shadow: $shadow-15;
    padding: 16px 0 16px;
    overflow: hidden;
  }
  &__news {
    max-width: 820px;
    margin-left: auto;
    margin-right: auto;
  }
}
.p-homeHeader {
  text-align: center;
}
.p-homeTabs {
  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: max-content;
  column-gap: 8px;
  &__list {
    font-family: inherit;
    font-size: 14px;
    text-align: center;
    color: $color-gray70;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    padding: 4px 16px;
    border: 1px solid $color-gray35;
    border-radius: $border-radius-round;
    @media (min-width: $window_tb_min) {
      &:hover {
        color: $color-green;
        border-color: transparent;
        background-color: $color-lightGreen-alpha20;
        & span {
          color: $color-green;
        }
      }
    }
    & span {
      margin-right: 4px;
    }
    &--active {
      color: $color-green;
      background-color: $color-lightGreen-alpha20;
      border: 1px solid transparent;
    }
    &--arrow {
      position: relative;
      padding: 4px 22px 4px 16px;
      &::before {
        content: "";
        position: absolute;
        top: 0;
        bottom: 0;
        right: 12px;
        width: 6px;
        height: 6px;
        margin: auto;
        border-top: 1px solid $color-gray70;
        border-right: 1px solid $color-gray70;
        transform: rotate(45deg);
      }
      &:hover {
        text-decoration: none;
        &::before {
          border-top: 1px solid $color-green;
          border-right: 1px solid $color-green;
        }
      }
    }
  }
}
/* margin */
.p-home__search + .p-home__news {
  margin-top: 80px;
}
.p-home__news + .p-home__link {
  margin-top: 30px;
}

/* sp style */
@include for-device("sp") {
  .p-home {
    &__inner {
      max-width: 100%;
      padding: 108px 16px 40px;
    }
    &__header {
      & img {
        height: 76px;
      }
    }
    &__tabs {
      max-width: 100%;
      padding: 6px 16px 0;
    }
    &__text {
      font-size: 16px;
      text-align: center;
      margin-top: 80px;
      & span {
        display: block;
      }
    }
    &__error {
      padding: 0 20px;
    }
    &__search {
      margin-top: 24px;
      padding: 12px 0 16px;
    }
    &__searchButton {
      width: 40px;
    }
    &__searchButtonText {
      display: none;
    }
  }
  .p-homeTabs {
    column-gap: 4px;
    &__list {
      font-size: 12px;
    }
  }
  /* margin */
  .p-home__news + .p-home__link {
    margin-top: 24px;
  }
}
