@charset "utf-8";

@use "../global/variables" as *;
@use "../global/mixin" as *;

.p-siteRiskSummary {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  &__list {
    display: grid;
    grid-template-rows: subgrid;
    grid-row: span 2;
    justify-content: center;
    row-gap: 8px;
    & + li {
      border-left: 1px solid $color-gray20;
    }
  }
  &__count {
    justify-self: center;
    font-size: 24px;
    font-weight: 600;
    &--error {
      color: $color-error;
    }
  }
  &__text {
    font-size: 12px;
  }
}
.p-siteRiskSummaryImp {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  row-gap: 20px;
  &__list {
    display: grid;
    grid-template-rows: subgrid;
    grid-row: span 2;
    justify-content: center;
    row-gap: 8px;
    & + li {
      border-left: 1px solid $color-gray20;
    }
    & + li:nth-child(3n + 1) {
      border-left: 0;
    }
  }
  &__count {
    justify-self: center;
    font-size: 24px;
    font-weight: 600;
    &--text {
      font-size: 20px;
      align-self: center;
    }
    &--error {
      color: $color-error;
    }
  }
  &__text {
    font-size: 12px;
  }
}
.p-siteRiskShadow {
  padding: 20px;
  box-shadow: $shadow-10;
  border-radius: $border-radius-card;
  &__head {
    display: grid;
    grid-template-columns: 1fr auto;
    column-gap: 12px;
    align-items: center;
    border-radius: $border-radius-card;
    transition: all 0.3s;
    font-weight: 600;
  }
  &__body {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid $color-gray20;
    position: relative;
  }
  &__text {
    font-size: 14px;
    font-weight: 600;
    &--error {
      color: $color-error;
    }
  }
  &__note {
    font-size: 12px;
    color: $color-gray80;
    margin-top: 4px;
  }
}
.p-siteRiskDetail {
  display: grid;
  grid-template-columns: 1fr auto;
  column-gap: 10px;
  padding: 20px;
  font-size: 14px;
  &__text {
    font-size: 14px;
    display: flex;
    flex-wrap: wrap;
  }
  &__badge {
    align-self: center;
  }
  &__alert {
    color: $color-error;
  }
}
.p-siteRiskToggle {
  &__button {
    display: flex;
    align-items: center;
    width: 100%;
    min-height: 60px;
  }
  &__arrowTop {
    content: "";
    display: inline-block;
    vertical-align: middle;
    color: $color-darkGreen;
    line-height: 1;
    width: 8px;
    height: 8px;
    border: 1px solid currentColor;
    border-left: 0;
    border-bottom: 0;
    box-sizing: border-box;
    transform: translateY(25%) rotate(-45deg);
  }
  &__arrowBottom {
    content: "";
    display: inline-block;
    vertical-align: middle;
    color: $color-darkGreen;
    line-height: 1;
    width: 8px;
    height: 8px;
    border: 1px solid currentColor;
    border-left: 0;
    border-bottom: 0;
    box-sizing: border-box;
    transform: translateY(-25%) rotate(135deg);
  }
}
.p-siteRiskAccordionList {
  display: grid;
  border: 1px solid $color-gray20;
  border-radius: $border-radius-card;
  & > li + li {
    border-top: 1px solid $color-gray20;
  }
  &--hiddden {
    overflow-y: hidden;
  }
  &--all {
    overflow: visible;
    max-width: 100%;
  }
}
.p-siteRiskAccordion {
  border-radius: $border-radius-card;
  cursor: pointer;
  &__head {
    display: grid;
    grid-template-columns: 1fr auto auto;
    column-gap: 12px;
    align-items: center;
    padding: 20px;
    border-radius: $border-radius-card;
    transition: all 0.3s;
    &::-webkit-details-marker {
      display: none;
    }
    & h3 {
      font-size: 14px;
      display: flex;
      column-gap: 8px;
      & span {
        margin-top: 2px;
      }
    }
    @media (min-width: $window_tb_min) {
      &:hover {
        opacity: 0.7;
      }
    }
  }
  &__body {
    padding: 20px;
    background-color: $color-gray10;
    animation: animation-fadeIn 0.5s ease;
    border-top: 1px solid $color-gray20;
  }
}
//アコーディオンのarrow
.p-siteRiskAccordion:not([open]) .p-siteRiskAccordion__head::after {
  content: "";
  display: inline-block;
  vertical-align: middle;
  color: $color-gray80;
  line-height: 1;
  width: 12px;
  height: 12px;
  border: 1px solid currentColor;
  border-left: 0;
  border-bottom: 0;
  box-sizing: border-box;
  transform: translateY(-25%) rotate(135deg);
}
.p-siteRiskAccordion[open] .p-siteRiskAccordion__head::after {
  content: "";
  display: inline-block;
  vertical-align: middle;
  color: $color-gray80;
  line-height: 1;
  width: 12px;
  height: 12px;
  border: 1px solid currentColor;
  border-left: 0;
  border-bottom: 0;
  box-sizing: border-box;
  transform: translateY(25%) rotate(-45deg);
}
.p-siteRiskListWrap {
  position: relative;
  &::before {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100px;
    background: linear-gradient(transparent 0%, #fff 100%);
  }
}
.p-siteRiskList {
  display: grid;
  row-gap: 10px;
  overflow-y: auto;
  padding-right: 8px;
  max-height: 360px;
}
.p-siteRiskCard {
  font-weight: 600;
  border: 1px solid $color-gray20;
  border-radius: $border-radius-card;
  padding: 20px;
  overflow-wrap: anywhere;
  word-break: break-word;
}
.p-siteRiskCardGray {
  background-color: $color-gray10;
  border-radius: $border-radius-card;
  padding: 20px;
  &__dt {
    font-size: 12px;
    font-weight: 300;
  }
  &__dd {
    font-weight: 600;
  }
  &__text {
    font-size: 12px;
  }
}
.p-siteRiskLink {
  padding: 8px 0;
}
.p-siteRiskDl {
  &__text--em {
    font-weight: 600;
  }
  dt {
    font-size: 14px;
    color: $color-darkGreen;
  }
  dd {
    font-size: 12px;
    margin-top: 4px;
    margin-left: 1.2em;
  }
}
.p-siteRiskImg {
  width: 240px;
}
.p-siteRiskDomainList {
  display: flex;
  flex-wrap: wrap;
  &__list {
    font-size: 14px;
  }
  & li {
    &::after {
      content: "/";
      color: $color-gray20;
      display: inline-block;
      padding-left: 6px;
      padding-right: 6px;
    }
    &:last-child::after {
      content: none;
    }
  }
}
.p-siteRiskFreeSsl {
  padding: 20px;
  background-color: $color-error10;
  border-radius: $border-radius-card;
  &__head {
    font-size: 20px;
    font-weight: 600;
    color: $color-error;
    padding-bottom: 16px;
  }
  &__body {
    border-top: 1px solid $color-error20;
    padding-top: 20px;
  }
  &__img {
    max-width: 284px;
    height: auto;
  }
  &__detail {
    margin-top: 20px;
    & p {
      font-size: 12px;
    }
    & p + p {
      margin-top: 8px;
    }
  }
  &__bold {
    font-weight: 600;
  }
}
.p-siteRiskPrice {
  display: flex;
  justify-content: center;
  align-items: baseline;
  background-color: $color-white;
  border: 1px solid $color-gray20;
  border-radius: $border-radius-card;
  padding: 16px;
  font-size: 20px;
  font-weight: 600;
  &__amount {
    margin-inline: auto;
    max-inline-size: max-content;
    overflow-wrap: anywhere;
  }
  &__unit {
    font-size: 18px;
    margin-left: 4px;
  }
}

//margin
.p-siteRiskSummary + .p-siteRiskToggle,
.p-siteRiskSummaryImp + .p-siteRiskToggle,
.p-siteRiskSummary + .p-siteRiskList {
  margin-top: 32px;
}
.c-panelList + .p-siteRiskShadow {
  margin-top: 20px;
}
.p-siteRiskDl + .p-siteRiskDl {
  margin-top: 20px;
}
.p-siteRiskCardGray + .p-siteRiskDl {
  margin-top: 20px;
}
.p-siteRiskImg + .p-siteRiskDl {
  margin-top: 20px;
}
.p-siteRiskToggle + .p-siteRiskCardGray {
  margin-top: 20px;
}
.c-panel__sideButton + .p-siteRiskSummaryImp,
.c-panel__sideButton + .p-passwordResult,
.c-panel__sideButton + .p-siteRiskSummary,
.c-panel__sideButton + .p-siteRiskToggle {
  margin-top: 20px;
}
.p-siteRiskFreeSsl + .c-panelList {
  margin-top: 20px;
}
/* sp style */
@include for-device("sp") {
  .p-siteRiskSummaryImp {
    grid-template-columns: repeat(2, 1fr);
    row-gap: 20px;
    &__list {
      & + li {
        border-left: 0;
      }
      & + li:nth-child(3n + 1) {
        border-left: 1px solid $color-gray20;
      }
      &:nth-child(even) {
        border-left: 1px solid $color-gray20;
      }
    }
  }
  .p-siteRiskAccordion {
    &__head {
      column-gap: 10px;
    }
  }
  .p-siteRiskLink {
    padding: 16px 0 0 20px;
  }
  .p-siteRiskImg {
    width: 100%;
  }
  .p-siteRiskFreeSsl {
    &__img {
      max-width: 100%;
    }
  }
}
