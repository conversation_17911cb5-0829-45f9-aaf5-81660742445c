window.addEventListener('scroll', function () {
  // スクロール量を取得
  const scroll = window.scrollY;
  // 画面の高さを取得
  const windowHeight = window.innerHeight;
  // オフセットを動的に設定
  const offset = windowHeight * 0.5; // ビューポートの高さの0.5倍
  // すべての.p-parallaxObjを取得
  const boxes = document.querySelectorAll('.p-parallaxObj');

  boxes.forEach(function (box) {
    // p-parallaxObjまでの高さを取得
    const distanceToBox = box.offsetTop;

    // 条件が成り立つときだけp-parallaxObjにis-alreadyクラスを付与する
    if (scroll + windowHeight - offset > distanceToBox) {
      box.classList.add('is-already');
    }
  });
});
