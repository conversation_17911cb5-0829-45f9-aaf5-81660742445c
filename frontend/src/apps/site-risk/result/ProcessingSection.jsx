import { useTranslation } from 'react-i18next';

function ProcessingSection() {
  const { t } = useTranslation('check_site-risk');
  return (
    <div className="c-loaderContetnt">
      <div className="c-loaderContetnt__icon">
        <span className="c-loader" />
      </div>
      <p className="c-loaderContetnt__text">
        {t('診断を実施中です。結果が準備でき次第、メールでお知らせいたします。')}
      </p>
      <div className="c-loaderContetnt__note">
        {t('※診断には数日かかる場合があります')}
      </div>
    </div>
  );
}

export default ProcessingSection;
