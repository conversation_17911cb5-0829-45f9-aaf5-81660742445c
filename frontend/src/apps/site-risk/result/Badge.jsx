import PropTypes from 'prop-types';

function Badge({ status, message }) {
  if (!message) return;
  if (status === 'alert') {
    return (
      <div className="c-badge">
        <span className="icon-base icon-sec-caution icon-size12 icon-color-white" />
        {message}
      </div>
    );
  }
  if (status === 'high') {
    return <div className="c-badge c-badge--high">{message}</div>;
  }
  if (status === 'warning') {
    return <div className="c-badge c-badge--warning">{message}</div>;
  }
  if (status === 'low') {
    return <div className="c-badge c-badge--low">{message}</div>;
  }
  return <div className="c-badge c-badge--safe">{message}</div>;
}

Badge.propTypes = {
  status: PropTypes.string.isRequired,
  message: PropTypes.string,
};

export default Badge;
