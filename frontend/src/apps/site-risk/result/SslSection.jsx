import PropTypes from 'prop-types';
import { useContext } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import rankAlert from '../../../assets/img/rank_alert.png';
import rankSafe from '../../../assets/img/rank_safe.png';
import rankWarning from '../../../assets/img/rank_warning.png';
import sslFreeAlertImage from '../../../assets/img/ssl_free_alert.png';
import Button from '../../../common/components/Button';
import SnsShareContainer from '../../../common/components/SnsShareContainer';
import { SiteRiskContext } from '../../../common/context/SiteRiskContext';
import { calculateRemainder, formatDatetime } from '../../../common/utils';
import { generateSiteRiskShareContent } from '../../../common/utils/shareUtils';
import Badge from './Badge';
import NotFoundSection from './NotFoundSection';
import ProcessingSection from './ProcessingSection';

const SSL_FREE_ALERT_IMAGE = sslFreeAlertImage;

const STATUS_IMAGES = {
  safe: rankSafe,
  warning: rankWarning,
  alert: rankAlert,
};

function SslSection({ ssl, onClickedContact }) {
  const { t } = useTranslation('check_site-risk');
  const { worstStatus } = useContext(SiteRiskContext);
  const shareText = generateSiteRiskShareContent(worstStatus);

  const STATUSES = { safe: t('低リスク'), warning: t('中リスク'), alert: t('緊急') };

  if (!ssl) {
    return (
      <section>
        <div className="c-panel gtm-view c-panel--tab">
          <h2 className="c-panel__title">
            <span className="icon-base icon-sec-ssl icon-size40 icon-color-darkGreen" />
            {t('実在証明・盗聴防止（SSL）診断')}
          </h2>
          <ProcessingSection />
        </div>
      </section>
    );
  }

  if (ssl.status && ssl.status === 'error') {
    return (
      <section>
        <div className="c-panel gtm-view c-panel--tab">
          <h2 className="c-panel__title">
            <span className="icon-base icon-sec-ssl icon-size40 icon-color-darkGreen" />
            {t('実在証明・盗聴防止（SSL）診断')}
          </h2>
          <NotFoundSection />
        </div>
      </section>
    );
  }

  const now = new Date();
  const remainingDays = Math.floor(
    (new Date(ssl.details[0].expires) - now) / (24 * 60 * 60 * 1000),
  );

  let status = 'safe';
  let message = t('ご利用中のSSLは有効期限が十分に残っております。');
  let note = t('安心です');

  let component;

  if (!ssl.summary.hostnameVerification) {
    status = 'alert';
    message
      = t('使用中の証明書がドメインと一致していません。早急に設定を見直してください。');
    note = t('不一致');
    component = (
      <div className="p-siteRiskDetail">
        {t('ドメインと異なる証明書')}
        <div className="p-siteRiskDetail__badge">
          <Badge status="alert" message={STATUSES['alert']} />
        </div>
      </div>
    );
  } else if (!ssl.summary.certVerification) {
    status = 'alert';
    message
      = t('証明書が無効になっています。早急に再設定または更新を行ってください。');
    note = t('無効');
    component = (
      <div className="p-siteRiskDetail">
        {t('無効な証明書')}
        <div className="p-siteRiskDetail__badge">
          <Badge status="alert" message={STATUSES['alert']} />
        </div>
      </div>
    );
  } else {
    const after30Days = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    if (ssl.summary.freeSslProvider) {
      window.ssl = { isFree: true };
      if (new Date(ssl.summary.earliestExpires).getTime() < now.getTime()) {
        status = 'alert';
        message
          = t('信頼性の低い無料SSLをご利用中です。変更を推奨します。');
        note = t('変更推奨');
      } else if (
        new Date(ssl.summary.earliestExpires).getTime() < after30Days.getTime()
      ) {
        status = 'warning';
        message
          = t('信頼性の低い無料SSLをご利用中です。変更を推奨します。');
        note = t('変更推奨');
      } else {
        status = 'warning';
        message
          = t('信頼性の低い無料SSLをご利用中です。変更を推奨します。');
        note = t('変更推奨');
      }
    } else {
      if (new Date(ssl.summary.earliestExpires).getTime() < now.getTime()) {
        status = 'alert';
        message = t('今すぐSSLを更新してください。');
        note = t('変更推奨');
      } else if (
        new Date(ssl.summary.earliestExpires).getTime() < after30Days.getTime()
      ) {
        status = 'warning';
        message = t('早急に有効期限をご確認ください。');
        note = t('変更推奨');
      } else {
        status = 'safe';
        message = t('ご利用中のSSLは有効期限が十分に残っております。');
        note = t('安全です');
      }
    }
    const expiresDate = ssl.details[0].expires.split('T')[0];
    component = (
      <div className="p-siteRiskDetail">
        {remainingDays >= 0 && (
          <p className="p-siteRiskDetail__text">
            <Trans
              ns="check_site-risk"
              i18nKey="有効期限：<sp>{{date}}まで（{{remainder}}）</sp>"
              values={{ date: expiresDate, remainder: calculateRemainder(new Date(ssl.details[0].expires)) }}
              components={{ sp: <span /> }}
            />
          </p>
        )}
        {remainingDays < 0 && (
          <p className="p-siteRiskDetail__text">
            <Trans
              ns="check_site-risk"
              i18nKey="有効期限：<alert>{{date}}まで（期限切れ）</alert>"
              values={{ date: expiresDate }}
              components={{ alert: <span className="p-siteRiskDetail__alert" /> }}
            />
          </p>
        )}
        <div className="p-siteRiskDetail__badge">
          <Badge status={status} message={STATUSES[status]} />
        </div>
      </div>
    );
  }

  const isVisibleContactButton = !(
    ssl.summary.certVerification
    && ssl.summary.hostnameVerification
    && !ssl.summary.freeSslProvider
  );

  return (
    <section>
      <div id="section_ssl" className="c-panel gtm-view c-panel--tab">
        <h2 className="c-panel__title">
          <span className="icon-base icon-sec-ssl icon-size40 icon-color-darkGreen" />
          {t('実在証明・盗聴防止（SSL）診断')}
        </h2>
        <div className="c-panel__content c-panel__content--gap20">
          <div className="c-panel__side">
            <div className="c-panel__rankImg">
              <img src={STATUS_IMAGES[status]} alt="" />
            </div>
            {/* pcのみ表示 */}
            {isVisibleContactButton && (
              <div className="c-panel__sideButton c-panel__sideButton--spNone">
                <Button
                  id="contact_ssl"
                  onClick={onClickedContact}
                  variant="accent"
                  widthSize="full"
                >
                  <Trans ns="check_site-risk" i18nKey="今すぐwebを安全に<br />テストSSL（無料）" components={{ br: <br /> }} />
                </Button>
              </div>
            )}
            <div className="c-panel__snsShare c-panel__snsShare--spNone">
              <SnsShareContainer
                id="ssl_sns"
                text={shareText}
                isShowFacebook={false}
                isShowInstagram={false}
                isShowYouTube={false}
                isShowTikTok={false}
              />
            </div>
            {/* pcのみ表示 end */}
          </div>
          <div className="c-panel__main">
            <p className={`c-panel__note c-panel__note--${status}`}>{note}</p>
            <p className="c-panel__result">{message}</p>
            {ssl.details.length === 0 && (
              <div className="c-panelList">
                <p className="c-panelList__note">
                  <span className="icon-base icon-sec-attention icon-size16 icon-color-darkGreen" />
                  {t('SSL証明書が見つかりませんでした')}
                </p>
              </div>
            )}
            {ssl.summary.freeSslProvider && (
              <div className="p-siteRiskFreeSsl">
                <div className="p-siteRiskFreeSsl__head">{t('無料SSLの企業利用はご注意ください')}</div>
                <div className="p-siteRiskFreeSsl__body">
                  <div className="p-siteRiskFreeSsl__img">
                    <img src={SSL_FREE_ALERT_IMAGE} alt={t('フィッシング審査なし 保証・サポートなし')} />
                  </div>
                  <div className="p-siteRiskFreeSsl__detail">
                    <p className="p-siteRiskFreeSsl__bold">{t('フィッシングサイトの95%以上は無料SSLを利用しています')}</p>
                    <p>
                      {t('無料SSLは手軽に取得できるため、多くのフィッシングサイトが悪用しています。')}
                      <br />
                      {t('企業が利用すると、信頼性の低さ・更新漏れ・サポートなしといったリスクがあります。')}
                      <br />
                      {t('企業サイトには、有償のSSLを推奨します。「無料」で選ぶのではなく、信頼性で選ぶことが重要です。')}
                    </p>
                  </div>
                </div>
              </div>
            )}
            <ul className="c-panelList">
              {ssl.summary.freeSslProvider && (
                <li>
                  <div className="p-siteRiskDetail">
                    {ssl.summary.freeSslProvider}
                    {t('（無料SSL）を利用中')}
                  </div>
                </li>
              )}
              <li>{component}</li>
            </ul>
            {/* spのみ表示 */}
            {isVisibleContactButton && (
              <div className="c-panel__sideButton c-panel__sideButton--bottom c-panel__sideButton--pcNone">
                <Button
                  id="contact_ssl_sp"
                  onClick={onClickedContact}
                  variant="accent"
                  widthSize="full"
                >
                  <Trans ns="check_site-risk" i18nKey="今すぐwebを安全に<br />テストSSL（無料）" components={{ br: <br /> }} />
                </Button>
              </div>
            )}
            {/* spのみ表示 end */}
          </div>
        </div>
        {ssl.summary.createdAt && (
          <p className="c-panel__annotation c-panel__annotation--xs">
            <Trans
              ns="check_site-risk"
              i18nKey="本診断結果は、{{date}}時点のものです。診断結果は、<btn>利用規約</btn>をご確認のうえ、最終的にはユーザーの皆様のご判断においてご利用ください。"
              values={{ date: formatDatetime(ssl.summary.createdAt) }}
              components={{
                btn: (
                  <Button
                    as="a"
                    href="https://www.gmo.jp/security/check/agreement/"
                    target="_blank"
                    rel="noopener"
                    variant="textXs"
                    referrerPolicy="strict-origin-when-cross-origin"
                  />
                ),
              }}
            />
          </p>
        )}
        {/* spのみ表示 SNS */}
        <div className="c-panel__snsShare c-panel__snsShare--pcNone">
          <SnsShareContainer
            id="ssl_sns_sp"
            text={shareText}
            isShowFacebook={false}
            isShowInstagram={false}
            isShowYouTube={false}
            isShowTikTok={false}
          />
        </div>
        {/* spのみ表示 SNS end */}
      </div>
    </section>
  );
}

SslSection.propTypes = {
  ssl: PropTypes.shape({
    status: PropTypes.string,
    details: PropTypes.arrayOf(PropTypes.shape({ expires: PropTypes.string })),
    summary: PropTypes.shape({
      hostnameVerification: PropTypes.bool,
      certVerification: PropTypes.bool,
      freeSslProvider: PropTypes.string,
      earliestExpires: PropTypes.string,
      createdAt: PropTypes.string,
    }),
  }),
  onClickedContact: PropTypes.func.isRequired,
};

export default SslSection;
