import { useTranslation } from 'react-i18next';

function NotFoundSection() {
  const { t } = useTranslation('check_site-risk');
  return (
    <div className="c-loaderContetnt">
      <div className="c-loaderContetnt__icon">
        <span className="icon-base icon-sec-error icon-color-darkGreen icon-size40" />
      </div>
      <p className="c-loaderContetnt__text">
        {t('対象ドメインのIPアドレスが確認できないため、診断を実施できませんでした。')}
        <span className="c-loaderContetnt__subText">
          {t('DNS設定を確認のうえ、再度診断を実施してください。')}
        </span>
      </p>
    </div>
  );
}

export default NotFoundSection;
