import PropTypes from 'prop-types';
import { useContext } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import bimiImage from '../../../assets/img/img_bimi.png';
import brandTldImage from '../../../assets/img/img_brand-tld.png';
import dmarcImage from '../../../assets/img/img_dmarc.png';
import spfImage from '../../../assets/img/img_spf.png';
import vcmImage from '../../../assets/img/img_vmc.png';
import Button from '../../../common/components/Button';
import RankImage from '../../../common/components/RankImage';
import SnsShareContainer from '../../../common/components/SnsShareContainer';
import { SiteRiskContext } from '../../../common/context/SiteRiskContext';
import { formatDatetime } from '../../../common/utils';
import { generateSiteRiskShareContent } from '../../../common/utils/shareUtils';
import Badge from './Badge';
import NotFoundSection from './NotFoundSection';
import ProcessingSection from './ProcessingSection';

const VCM_IMAGE = vcmImage;
const BIMI_IMAGE = bimiImage;
const SPF_IMAGE = spfImage;
const DMARC_IMAGE = dmarcImage;
const BRANDTLD_IMAGE = brandTldImage;

function ImpersonationSection({ impersonation }) {
  const { t } = useTranslation('check_site-risk');
  const { worstStatus } = useContext(SiteRiskContext);
  const shareText = generateSiteRiskShareContent(worstStatus);

  const RANKS = {
    A: t('問題ありません！この状態をキープしましょう。'),
    B: t('今のところ安全ですが、油断せず対策を考えましょう。'),
    C: t('このままだと危険です！早めに対策しましょう。'),
    D: t('非常に危険です！早急に対策しましょう。'),
  };

  const RANKSNOTE = {
    A: t('安全です'),
    B: t('要注意'),
    C: t('危険'),
    D: t('非常に危険'),
  };

  if (!impersonation) {
    return (
      <section>
        <div className="c-panel gtm-view c-panel--tab">
          <h2 className="c-panel__title">
            <span className="icon-base icon-sec-impersonation icon-size40 icon-color-darkGreen" />
            {t('なりすまし診断')}
          </h2>
          <ProcessingSection />
        </div>
      </section>
    );
  }

  if (impersonation.status && impersonation.status === 'error') {
    return (
      <section>
        <div className="c-panel gtm-view c-panel--tab">
          <h2 className="c-panel__title">
            <span className="icon-base icon-sec-impersonation icon-size40 icon-color-darkGreen" />
            {t('なりすまし診断')}
          </h2>
          <NotFoundSection />
        </div>
      </section>
    );
  }

  return (
    <section>
      <div id="section_impersonation" className="c-panel gtm-view c-panel--tab">
        <h2 className="c-panel__title">
          <span className="icon-base icon-sec-impersonation icon-size40 icon-color-darkGreen" />
          {t('なりすまし診断')}
        </h2>
        <div className="c-panel__content">
          <div className="c-panel__side">
            <div className="c-panel__rankImg">
              <RankImage
                rank={impersonation.summary.rank}
                type="impersonation"
              />
            </div>
            {/* pcのみ表示 */}
            <div className="c-panel__sideButton c-panel__sideButton--spNone">
              <Button
                id="impersonation_link_detail"
                as="a"
                href="https://www2.brandsecurity.gmo/l/959492/2025-07-14/85bc4"
                target="_blank"
                rel="noopener"
                variant="accent"
                widthSize="full"
                referrerPolicy="strict-origin-when-cross-origin"
              >
                {t('詳細に診断する')}
              </Button>
            </div>
            <div className="c-panel__snsShare c-panel__snsShare--spNone">
              <SnsShareContainer
                id="impeersonation_sns"
                text={shareText}
                isShowFacebook={false}
                isShowInstagram={false}
                isShowYouTube={false}
                isShowTikTok={false}
              />
            </div>
            {/* pcのみ表示 end */}
          </div>
          <div className="c-panel__main">
            <p
              className={`c-panel__note c-panel__note--imp${impersonation.summary.rank}`}
            >
              {RANKSNOTE[impersonation.summary.rank]}
            </p>
            <p className="c-panel__result">
              {RANKS[impersonation.summary.rank]}
            </p>
            {/* spのみ表示 */}
            <div className="c-panel__sideButton c-panel__sideButton--pcNone">
              <Button
                id="impersonation_link_detail_upper_sp"
                as="a"
                href="https://www2.brandsecurity.gmo/l/959492/2025-07-14/85bc4"
                target="_blank"
                rel="noopener"
                variant="accent"
                widthSize="full"
                referrerPolicy="strict-origin-when-cross-origin"
              >
                {t('詳細に診断する')}
              </Button>
            </div>
            {/* spのみ表示 end */}
            <ul className="p-siteRiskSummaryImp">
              <li className="p-siteRiskSummaryImp__list">
                <span
                  className={`${!impersonation.summary.vmc
                    ? 'p-siteRiskSummaryImp__count p-siteRiskSummaryImp__count--text p-siteRiskSummaryImp__count--error'
                    : 'p-siteRiskSummaryImp__count p-siteRiskSummaryImp__count--text'}`}
                >
                  {impersonation.summary.vmc ? t('あり') : t('なし')}
                </span>
                <p className="p-siteRiskSummaryImp__text">{t('VMC発行申請')}</p>
              </li>
              <li className="p-siteRiskSummaryImp__list">
                <span
                  className={`${!impersonation.summary.bimi
                    ? 'p-siteRiskSummaryImp__count p-siteRiskSummaryImp__count--text p-siteRiskSummaryImp__count--error'
                    : 'p-siteRiskSummaryImp__count p-siteRiskSummaryImp__count--text'}`}
                >
                  {impersonation.summary.bimi ? t('あり') : t('なし')}
                </span>
                <p className="p-siteRiskSummaryImp__text">{t('BIMIレコード設定')}</p>
              </li>
              {impersonation.summary.spf !== null && (
                <li className="p-siteRiskSummaryImp__list">
                  <span
                    className={`${!impersonation.summary.spf
                      ? 'p-siteRiskSummaryImp__count p-siteRiskSummaryImp__count--text p-siteRiskSummaryImp__count--error'
                      : 'p-siteRiskSummaryImp__count p-siteRiskSummaryImp__count--text'}`}
                  >
                    {impersonation.summary.spf ? t('あり') : t('なし')}
                  </span>
                  <p className="p-siteRiskSummaryImp__text">{t('SPFレコード設定')}</p>
                </li>
              )}
              {impersonation.summary.dmarc !== null && (
                <li className="p-siteRiskSummaryImp__list">
                  <span
                    className={`${!impersonation.summary.dmarc
                      ? 'p-siteRiskSummaryImp__count p-siteRiskSummaryImp__count--text p-siteRiskSummaryImp__count--error'
                      : 'p-siteRiskSummaryImp__count p-siteRiskSummaryImp__count--text'}`}
                  >
                    {impersonation.summary.dmarc ? t('あり') : t('なし')}
                  </span>
                  <p className="p-siteRiskSummaryImp__text">{t('DMARCレコード設定')}</p>
                </li>
              )}

              <li className="p-siteRiskSummaryImp__list">
                <span
                  className={`${!impersonation.summary.brandTld
                    ? 'p-siteRiskSummaryImp__count p-siteRiskSummaryImp__count--text p-siteRiskSummaryImp__count--error'
                    : 'p-siteRiskSummaryImp__count p-siteRiskSummaryImp__count--text'}`}
                >
                  {impersonation.summary.brandTld ? t('あり') : t('なし')}
                </span>
                <p className="p-siteRiskSummaryImp__text">{t('ブランドTLD利用')}</p>
              </li>
              <li className="p-siteRiskSummaryImp__list">
                <span
                  className={
                    impersonation.summary.count > 0
                      ? 'p-siteRiskSummaryImp__count p-siteRiskSummaryImp__count--error'
                      : 'p-siteRiskSummaryImp__count'
                  }
                >
                  {impersonation.summary.count.toLocaleString()}
                </span>
                <p className="p-siteRiskSummaryImp__text">{t('類似ドメイン数量')}</p>
              </li>
            </ul>
            <div className="p-siteRiskToggle">
              <ul className="p-siteRiskAccordionList">
                <li>
                  <details className="p-siteRiskAccordion">
                    <summary
                      id={`accordion_impersonation_vmc_${impersonation.summary.vmc}_summary`}
                      className="p-siteRiskAccordion__head"
                    >
                      <h3
                        id={`accordion_impersonation_vmc_${impersonation.summary.vmc}_h3`}
                      >
                        {t('VMC発行申請')}
                      </h3>
                      <Badge
                        status={impersonation.summary.vmc ? 'safe' : 'alert'}
                        message={impersonation.summary.vmc ? t('あり') : t('なし')}
                      />
                    </summary>
                    <div className="p-siteRiskAccordion__body">
                      <div className="p-siteRiskImg">
                        <img src={VCM_IMAGE} alt={t('VMC発行申請のイメージ')} />
                      </div>
                      <dl className="p-siteRiskDl">
                        <dt>{t('・VMC発行申請（公式ロゴの証明書）とは')}</dt>
                        <dd>
                          <Trans
                            ns="check_site-risk"
                            i18nKey="VMC（Verified Mark<br />Certificate）はブランドロゴをメールやウェブに表示するための証明書で、なりすまし対策のためにブランド視認性を高める仕組みです。"
                            components={{ br: <br /> }}
                          />
                          <br />
                          {t('VMCがないと、BIMIのロゴを使えなかったり、なりすまし防止効果が十分でないこともあります。')}
                          <br />
                          {t('VMCを取得すると、メールの信頼性がさらにアップします。')}
                        </dd>
                      </dl>
                    </div>
                  </details>
                </li>
                <li>
                  <details className="p-siteRiskAccordion">
                    <summary
                      id={`accordion_impersonation_bimi_${impersonation.summary.bimi}_summary`}
                      className="p-siteRiskAccordion__head"
                    >
                      <h3
                        id={`accordion_impersonation_bimi_${impersonation.summary.bimi}_h3`}
                      >
                        {t('BIMIレコード設定')}
                      </h3>
                      <Badge
                        status={impersonation.summary.bimi ? 'safe' : 'alert'}
                        message={impersonation.summary.bimi ? t('あり') : t('なし')}
                      />
                    </summary>
                    <div className="p-siteRiskAccordion__body">
                      <div className="p-siteRiskImg">
                        <img
                          src={BIMI_IMAGE}
                          alt={t('BIMIレコード設定のイメージ')}
                        />
                      </div>
                      <dl className="p-siteRiskDl">
                        <dt>{t('・BIMIレコード設定（正規のロゴ表示）とは')}</dt>
                        <dd>
                          <Trans
                            ns="check_site-risk"
                            i18nKey="BIMI（Brand Indicators for Message<br />Identification）は、メール送信者の公式ブランドロゴを表示する仕組みです。設定がないと、公式のメールかどうか判断しにくくなり、偽のメールが信用されやすくなります。"
                            components={{ br: <br /> }}
                          />
                          <br />
                          {t('BIMIを設定すれば、なりすましメールを見破りやすくなります。')}
                        </dd>
                      </dl>
                    </div>
                  </details>
                </li>
                {impersonation.summary.spf !== null && (
                  <li>
                    <details className="p-siteRiskAccordion">
                      <summary
                        id={`accordion_impersonation_spf_${impersonation.summary.spf}_summary`}
                        className="p-siteRiskAccordion__head"
                      >
                        <h3
                          id={`accordion_impersonation_spf_${impersonation.summary.spf}_h3`}
                        >
                          {t('SPFレコード設定')}
                        </h3>
                        <Badge
                          status={impersonation.summary.spf ? 'safe' : 'alert'}
                          message={impersonation.summary.spf ? t('あり') : t('なし')}
                        />
                      </summary>
                      <div className="p-siteRiskAccordion__body">
                        <div className="p-siteRiskImg">
                          <img
                            src={SPF_IMAGE}
                            alt={t('SPFレコード設定のイメージ')}
                          />
                        </div>
                        <dl className="p-siteRiskDl">
                          <dt>{t('・SPFレコード設定とは')}</dt>
                          <dd>
                            {t('SPFレコードは、「このドメインから送信してよいメールサーバー」を指定する設定です。これにより、受信側が正規の送信元かどうかを確認できます。SPFを設定していないと、第三者が勝手に自社のドメインを使ってメールを送ることができ、なりすましメールのリスクが高まります。設定することで、不正な送信元からのメールをブロックし、信頼性の高いメール環境を維持できます。')}
                          </dd>
                        </dl>
                      </div>
                    </details>
                  </li>
                )}
                {impersonation.summary.dmarc !== null && (
                  <li>
                    <details className="p-siteRiskAccordion">
                      <summary
                        id={`accordion_impersonation_dmarc_${impersonation.summary.dmarc}_summary`}
                        className="p-siteRiskAccordion__head"
                      >
                        <h3
                          id={`accordion_impersonation_dmarc_${impersonation.summary.dmarc}_h3`}
                        >
                          {t('DMARCレコード設定')}
                        </h3>
                        <Badge
                          status={
                            impersonation.summary.dmarc ? 'safe' : 'alert'
                          }
                          message={
                            impersonation.summary.dmarc ? t('あり') : t('なし')
                          }
                        />
                      </summary>
                      <div className="p-siteRiskAccordion__body">
                        <div className="p-siteRiskImg">
                          <img
                            src={DMARC_IMAGE}
                            alt={t('DMARCレコード設定のイメージ')}
                          />
                        </div>
                        <dl className="p-siteRiskDl">
                          <dt>{t('・DMARCレコード設定とは')}</dt>
                          <dd>
                            {t('DMARCレコードは、SPFやDKIMの認証結果を基に、「認証に失敗したメールをどう処理するか」を決めるルールです。これがないと、なりすましメールがそのまま届く可能性があります。DMARCを設定すれば、認証に失敗したメールを拒否したり、迷惑メールフォルダに振り分けたりでき、なりすまし対策が強化されます。また、レポート機能により、不正な送信の状況を把握し、より適切な対策を取ることが可能になります。')}
                          </dd>
                        </dl>
                      </div>
                    </details>
                  </li>
                )}

                <li>
                  <details className="p-siteRiskAccordion">
                    <summary
                      id={`accordion_impersonation_brandtld_${impersonation.summary.brandTld}_summary`}
                      className="p-siteRiskAccordion__head"
                    >
                      <h3
                        id={`accordion_impersonation_brandtld_${impersonation.summary.brandTld}_h3`}
                      >
                        {t('ブランドTLD利用')}
                      </h3>
                      <Badge
                        status={
                          impersonation.summary.brandTld ? 'safe' : 'alert'
                        }
                        message={
                          impersonation.summary.brandTld ? t('あり') : t('なし')
                        }
                      />
                    </summary>
                    <div className="p-siteRiskAccordion__body">
                      <div className="p-siteRiskImg">
                        <img src={BRANDTLD_IMAGE} alt={t('ブランドTLDのイメージ')} />
                      </div>
                      <dl className="p-siteRiskDl">
                        <dt>{t('・ブランドTLDとは')}</dt>
                        <dd>
                          {t('ブランドTLDとは、「.com」や「.jp」ではなく、企業専用のドメイン（例：.gmo）」を使うことです。')}
                          <br />
                          {t('例えば、「.gmo」ドメインを持っていれば、他の人は取得できません。')}
                          <br />
                          {t('偽物サイトを作られにくくなり、なりすましのリスクを減らすことができます。')}
                        </dd>
                      </dl>
                    </div>
                  </details>
                </li>
                <li>
                  <details className="p-siteRiskAccordion">
                    <summary
                      id={`accordion_impersonation_count_${impersonation.summary.count}_summary`}
                      className="p-siteRiskAccordion__head"
                    >
                      <h3
                        id={`accordion_impersonation_count_${impersonation.summary.count}_h3`}
                      >
                        {t('類似ドメイン数量')}
                      </h3>
                      <Badge
                        status={
                          impersonation.summary.count === 0 ? 'safe' : 'alert'
                        }
                        message={
                          impersonation.summary.count === 0
                            ? '0'
                            : impersonation.summary.count.toLocaleString()
                        }
                      />
                    </summary>
                    <div className="p-siteRiskAccordion__body">
                      {impersonation.summary.count > 0 && (
                        <dl className="p-siteRiskDl">
                          <dt>
                            {t('・見つかった類似ドメイン（10件のみ表示しています）')}
                            {' '}
                          </dt>
                          <dd>
                            <ul className="p-siteRiskDomainList">
                              {impersonation.details
                                .slice(0, 10)
                                .map((detail, index) => (
                                  <li
                                    className="p-siteRiskDomainList__list"
                                    key={index}
                                  >
                                    {detail.fqdn}
                                  </li>
                                ))}
                            </ul>
                          </dd>
                        </dl>
                      )}
                      <dl className="p-siteRiskDl">
                        <dt>{t('・類似ドメイン数量とは')}</dt>
                        <dd>
                          {t('あなたの会社のドメイン（例：example.com）と似たドメインがどれくらいあるかを示します。')}
                          <br />
                          {t('例えば、examp1e.com（数字の1が入る） や example.co（.coドメイン）などがあると、詐欺サイトを作られやすくなります。類似ドメインが多いほど、注意が必要です。')}
                        </dd>
                      </dl>
                    </div>
                  </details>
                </li>
              </ul>
            </div>
            {/* spのみ表示 */}
            <div className="c-panel__sideButton c-panel__sideButton--bottom c-panel__sideButton--pcNone">
              <Button
                id="impersonation_link_detail_sp"
                as="a"
                href="https://www2.brandsecurity.gmo/l/959492/2025-07-14/85bc4"
                target="_blank"
                rel="noopener"
                variant="accent"
                widthSize="full"
                referrerPolicy="strict-origin-when-cross-origin"
              >
                {t('詳細に診断する')}
              </Button>
            </div>
            {/* spのみ表示 end */}
          </div>
        </div>
        {impersonation.summary.createdAt && (
          <p className="c-panel__annotation c-panel__annotation--xs">
            <Trans
              ns="check_site-risk"
              i18nKey="本診断結果は、{{date}}時点のものです。診断結果は、<btn>利用規約</btn>をご確認のうえ、最終的にはユーザーの皆様のご判断においてご利用ください。"
              values={{ date: formatDatetime(impersonation.summary.createdAt) }}
              components={{
                btn: (
                  <Button
                    as="a"
                    href="https://www.gmo.jp/security/check/agreement/"
                    target="_blank"
                    rel="noopener"
                    variant="textXs"
                    referrerPolicy="strict-origin-when-cross-origin"
                  />
                ),
              }}
            />
          </p>
        )}
        {/* spのみ表示 SNS */}
        <div className="c-panel__snsShare c-panel__snsShare--pcNone">
          <SnsShareContainer
            id="impeersonation_sns_sp"
            text={shareText}
            isShowFacebook={false}
            isShowInstagram={false}
            isShowYouTube={false}
            isShowTikTok={false}
          />
        </div>
        {/* spのみ表示 SNS end */}
      </div>
    </section>
  );
}

ImpersonationSection.propTypes = {
  impersonation: PropTypes.shape({
    status: PropTypes.string,
    summary: PropTypes.shape({
      rank: PropTypes.string,
      vmc: PropTypes.bool,
      bimi: PropTypes.bool,
      spf: PropTypes.bool,
      dmarc: PropTypes.bool,
      brandTld: PropTypes.bool,
      count: PropTypes.number,
      createdAt: PropTypes.string,
    }),
    details: PropTypes.array,
  }),
  onClickedContact: PropTypes.func.isRequired,
  isProcessing: PropTypes.bool,
};

export default ImpersonationSection;
