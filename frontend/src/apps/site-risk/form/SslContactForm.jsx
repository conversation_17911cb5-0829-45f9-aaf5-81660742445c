import PropTypes from 'prop-types';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import Button from '../../../common/components/Button';
import { validateSslContactForm } from '../../../common/validators';

function SslContactForm({ onSubmit }) {
  const { t } = useTranslation('check_site-risk');
  const [isProcessing, setIsProcessing] = useState(false);
  const [formState, setFormState] = useState({
    lastName: '',
    firstName: '',
    telephone: '',
  });
  const [errors, setErrors] = useState({
    lastName: '',
    firstName: '',
    telephone: '',
  });

  const { lastName, firstName, telephone } = formState;
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormState(prevState => ({
      ...prevState,
      [name]: value,
    }));
    setErrors(prevState => ({
      ...prevState,
      [name]: '',
    }));
  };

  const validateFormData = () => {
    const { isValid, errors } = validateSslContactForm.validate(formState);

    if (!isValid) {
      const errorObj = {};
      errors.forEach((error) => {
        const fieldName = error.instancePath.substring(1);
        errorObj[fieldName] = error.message;
      });
      setErrors(prev => ({
        ...prev,
        ...errorObj,
      }));

      return false;
    }

    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateFormData()) {
      return;
    }

    setIsProcessing(true);
    await onSubmit({
      target: 'ssl',
      telephone,
      fullname: `${lastName} ${firstName}`,
    });
    setIsProcessing(false);
  };

  return (
    <div className="c-modalBox">
      <h2 className="c-modalBox__title">{t('45日間無料お試しSSLサーバ証書')}</h2>
      <div className="c-modalBox__form">
        <form onSubmit={handleSubmit} className="c-inputForm">
          <div className="c-inputForm__column">
            <div className="c-inputForm__columnBox">
              <label htmlFor="lastName" className="c-input__label">
                {t('姓')}
                <span className="c-input__required">{t('※')}</span>
              </label>
              <input
                id="lastName"
                name="lastName"
                type="text"
                placeholder={t('安全')}
                className="c-input__input"
                value={lastName}
                onChange={handleChange}
                required
              />
              <div className="c-input__error">{errors.lastName}</div>
            </div>
            <div className="c-inputForm__columnBox">
              <label htmlFor="firstName" className="c-input__label">
                {t('名')}
                <span className="c-input__required">{t('※')}</span>
              </label>
              <input
                id="firstName"
                name="firstName"
                type="text"
                placeholder={t('守')}
                className="c-input__input"
                value={firstName}
                onChange={handleChange}
                required
              />
              <div className="c-input__error">{errors.firstName}</div>
            </div>
          </div>
          <div className="c-inputForm__box">
            <label htmlFor="phone" className="c-input__label">
              {t('電話番号')}
              <span className="c-input__required">{t('※')}</span>
            </label>
            <input
              id="phone"
              name="telephone"
              type="tel"
              placeholder={t('電話番号を入力してください')}
              className="c-input__input"
              value={telephone}
              onChange={handleChange}
              required
            />
            <div className="c-input__error">{errors.telephone}</div>
          </div>
          <div className="c-inputForm__button">
            <Button
              id="sslContactSubmit"
              type="submit"
              variant="primary"
              widthSize="full"
              disabled={isProcessing}
              isLoading={isProcessing}
            >
              {t('申し込み')}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}

SslContactForm.propTypes = { onSubmit: PropTypes.func.isRequired };

export default SslContactForm;
