import PropTypes from 'prop-types';
import { useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import Button from '../../../common/components/Button';

const PRICING_CONFIG = {
  A: 11000,
  B: 11000,
  C: 27500,
  D: 27500,
  E: 27500,
};

function NdsContactForm({ onSubmit, rank = 'C' }) {
  const [isProcessing, setIsProcessing] = useState(false);
  const { t } = useTranslation('check_site-risk');

  const handleSubmit = async () => {
    setIsProcessing(true);
    try {
      await onSubmit({ target: 'nds' });
    } catch (error) {
      console.error('NDS contact form submission error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="c-modalBox">
      <h2 className="c-modalBox__title">{t('お客様の診断結果での概算見積')}</h2>
      <div className="c-modalBox__item">
        <div className="p-siteRiskPrice">
          <div className="p-siteRiskPrice__amount">
            <Trans
              ns="check_site-risk"
              i18nKey="¥{{price}}<unit>程度</unit>"
              values={{ price: PRICING_CONFIG[rank].toLocaleString() }}
              components={{ unit: <span className="p-siteRiskPrice__unit" /> }}
            />
          </div>
        </div>
      </div>
      <p className="c-modalBox__text">
        <Trans ns="check_site-risk" i18nKey="プロが最新のセキュリティ対策を<br />迅速に代行いたします" components={{ br: <br /> }} />
      </p>
      <div className="c-modalBox__button">
        <Button
          id="nds_contact_submit"
          onClick={handleSubmit}
          type="submit"
          variant="primary"
          widthSize="full"
          disabled={isProcessing}
          isLoading={isProcessing}
        >
          {t('申し込み')}
        </Button>
      </div>
    </div>
  );
}

NdsContactForm.propTypes = {
  onSubmit: PropTypes.func.isRequired,
  rank: PropTypes.string,
};

export default NdsContactForm;
