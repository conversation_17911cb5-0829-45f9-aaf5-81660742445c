import PropTypes from 'prop-types';
import { Trans, useTranslation } from 'react-i18next';
import Button from '../../../common/components/Button';
import { openWithLanguage } from '../../../common/utils/navigationUtils';
import SiteRiskPeriodicCheckup from '../SiteRiskPeriodicCheckup';
import OverviewCard from './OverviewCard';
import OverviewGrid from './OverviewGrid';

const NdsIcon = () => (
  <span className="icon-base icon-sec-security icon-size20 icon-color-darkGreen" />
);
const CloudIcon = () => (
  <span className="icon-base icon-sec-cloud icon-size20 icon-color-darkGreen" />
);
const SslIcon = () => (
  <span className="icon-base icon-sec-ssl icon-size20 icon-color-darkGreen" />
);
const ImpersonationIcon = () => (
  <span className="icon-base icon-sec-impersonation icon-size20 icon-color-darkGreen" />
);

function ExpiredOverview({ fqdn, nds, cloud, ssl, impersonation, code, result }) {
  const { t } = useTranslation('check_site-risk');

  const NDS_RANKS = {
    A: t('安全です'),
    B: t('安全です'),
    C: t('要対策'),
    D: t('要対策'),
    E: t('要緊急対応'),
  };

  const CLOUD_TEXTS = {
    要確認: t('要確認'),
    利用なし: t('利用なし'),
  };

  const SSL_TEXTS = {
    安全です: t('安全です'),
    不一致: t('不一致'),
    無効: t('無効'),
    変更推奨: t('変更推奨'),
  };

  const IMPERSONATION_RANKS = {
    A: t('安全です'),
    B: t('要注意'),
    C: t('危険'),
    D: t('非常に危険'),
  };

  const sections = [
    {
      id: 'anchorLinker_nds',
      targetId: 'anchorLink_nds',
      title: (
        <span>
          {t('Webサイト脆弱性診断')}
        </span>
      ),
      icon: NdsIcon,
      status: nds.status,
      rank: nds.rank,
      type: 'nds',
      text: NDS_RANKS[nds.rank],
    },
    {
      id: 'anchorLinker_cloud',
      targetId: 'anchorLink_cloud',
      title: (
        <span>
          {t('クラウド利用・リスク診断')}
        </span>
      ),
      icon: CloudIcon,
      status: cloud.status,
      type: 'cloud',
      text: CLOUD_TEXTS[cloud.text],
    },
    {
      id: 'anchorLinker_ssl',
      targetId: 'anchorLink_ssl',
      title: (
        <span>
          {t('実在証明・盗聴防止（SSL）診断')}
        </span>
      ),
      icon: SslIcon,
      status: ssl.status,
      type: 'ssl',
      text: SSL_TEXTS[ssl.text],
    },
    {
      id: 'anchorLinker_impersonation',
      targetId: 'anchorLink_impersonation',
      title: t('なりすまし診断'),
      icon: ImpersonationIcon,
      status: impersonation.status,
      rank: impersonation.rank,
      type: 'impersonation',
      text: IMPERSONATION_RANKS[impersonation.rank],
    },
  ];

  const handleReDiagnostic = () => {
    localStorage.setItem('state', JSON.stringify({ diagnosticValue: fqdn }));
    openWithLanguage('/security/');
  };

  return (
    <div className="c-overview c-overview--bg">
      <div className="c-overview__head c-overview__head--gray">
        <dl>
          <dt>{t('診断対象のURL')}</dt>
          <dd>{fqdn}</dd>
        </dl>
        <div className="c-overview__button">
          <Button
            id="another_url"
            onClick={handleReDiagnostic}
            external
            exIcon="small"
            variant="secondary"
          >
            {t('再度診断する')}
          </Button>
        </div>
      </div>
      <div className="c-overview__check">
        <SiteRiskPeriodicCheckup
          code={code}
          nextCheckedAt={result?.configuration?.nextCheckedAt}
          isRegularly={result?.configuration?.isRegularly}
          interval={result?.configuration?.interval}
          isNotification={result?.configuration?.isNotification}
          createdAt={result?.overview?.createdAt}
        />
      </div>
      <div className="c-overview__nav c-overview__nav--noAnchor">
        <OverviewGrid>
          {sections.map(section => (
            <OverviewCard
              key={section.id}
              {...section}
              className="c-overviewCard"
              onClick={() => {
                const element = document.getElementById(section.targetId);
                if (element) {
                  element.scrollIntoView({ behavior: 'smooth' });
                }
              }}
            />
          ))}
        </OverviewGrid>
      </div>
      <div className="c-overview__annotation">
        <Trans
          ns="check_site-risk"
          i18nKey="※各項目の詳細を確認できる期限が過ぎています。<sp>ご確認いただくには<btn>再度認診</btn>を行ってください。</sp>"
          components={{
            sp: <span />,
            btn: <Button variant="textXs" onClick={() => handleReDiagnostic()} />,
          }}
        />
      </div>
    </div>
  );
}

ExpiredOverview.propTypes = {
  fqdn: PropTypes.string.isRequired,
  nds: PropTypes.shape({
    status: PropTypes.string.isRequired,
    rank: PropTypes.string.isRequired,
  }).isRequired,
  cloud: PropTypes.shape({
    status: PropTypes.string.isRequired,
    text: PropTypes.string.isRequired,
  }).isRequired,
  ssl: PropTypes.shape({
    status: PropTypes.string.isRequired,
    text: PropTypes.string.isRequired,
  }).isRequired,
  impersonation: PropTypes.shape({
    status: PropTypes.string.isRequired,
    rank: PropTypes.string.isRequired,
  }).isRequired,
  code: PropTypes.string,
  result: PropTypes.shape({
    configuration: PropTypes.shape({
      nextCheckedAt: PropTypes.string,
      isRegularly: PropTypes.bool,
      interval: PropTypes.number,
      isNotification: PropTypes.bool,
    }),
    overview: PropTypes.shape({ createdAt: PropTypes.string }),
  }),
};

export default ExpiredOverview;
