import { useState, useEffect } from 'react';
import { useMediaQuery } from 'react-responsive';
import mailImage from '../../assets/img/illust_mail.png';
import brandTldCorpCanon from '../../assets/img/yourbrand_canon.png';
import brandTldCorpLead from '../../assets/img/yourbrand_corp_lead.png';
import brandTldCorpDnp from '../../assets/img/yourbrand_dnp.png';
import brandTldCorpGmo from '../../assets/img/yourbrand_gmo.png';
import brandTldCorpHitachi from '../../assets/img/yourbrand_hitachi.png';
import brandTldImage01 from '../../assets/img/yourbrand_image01.png';
import brandTldImage02 from '../../assets/img/yourbrand_image02.png';
import brandTldImage03 from '../../assets/img/yourbrand_image03.png';
import brandTldCorpKddi from '../../assets/img/yourbrand_kddi.png';
import brandTldLogo from '../../assets/img/yourbrand_logo.svg';
import brandTldCorpRicoh from '../../assets/img/yourbrand_ricoh.png';
import brandTldCorpSharp from '../../assets/img/yourbrand_sharp.png';
import brandTldCorpToray from '../../assets/img/yourbrand_toray.png';
import brandTldCorpToyota from '../../assets/img/yourbrand_toyota.png';
import brandTldCorpYodobashi from '../../assets/img/yourbrand_yodobashi.png';
import Button from '../../common/components/Button';
import VerticalSlider from '../../common/components/VerticalSlider';
import useModal from '../../common/hooks/useModal';
import { roundToSignificantDigits, formatJapaneseYen } from '../../common/utils';
import { validateTld } from '../../common/validators/index.js';
import YourbrandContactForm from './form/YourbrandContactForm';

const BRANDTLDLOGO = brandTldLogo;
const BRANDTLD_IMAGE01 = brandTldImage01;
const BRANDTLD_IMAGE02 = brandTldImage02;
const BRANDTLD_IMAGE03 = brandTldImage03;
const BRANDTLDCORP_LEAD = brandTldCorpLead;
const BRANDTLDCORP_CANON = brandTldCorpCanon;
const BRANDTLDCORP_DNP = brandTldCorpDnp;
const BRANDTLDCORP_GMO = brandTldCorpGmo;
const BRANDTLDCORP_HITACHI = brandTldCorpHitachi;
const BRANDTLDCORP_KDDI = brandTldCorpKddi;
const BRANDTLDCORP_RICOH = brandTldCorpRicoh;
const BRANDTLDCORP_SHARP = brandTldCorpSharp;
const BRANDTLDCORP_TORAY = brandTldCorpToray;
const BRANDTLDCORP_TOYOTA = brandTldCorpToyota;
const BRANDTLDCORP_YODOBASHI = brandTldCorpYodobashi;

const MAIL_IMAGE = mailImage;

const ICANN_INITIAL_FEE_USD = 227000; // 22.7万ドル
const ICANN_RENEWAL_FEE_USD = 26000; // 2.6万ドル

function Yourbrand() {
  const [brandTld, setBrandTld] = useState('');
  const [error, setError] = useState('');
  const { show, Modal, setIsModalClosable } = useModal();
  const [modalContent, setModalContent] = useState(<></>);

  const [exchangeRate, setExchangeRate] = useState(null);

  const isMobile = useMediaQuery({ query: '(max-width: 600px)' });

  useEffect(() => {
    const fetchExchangeRate = async () => {
      try {
        const response = await fetch('https://security-api.gmo.jp/static/json/usd_rate.json');

        if (!response.ok) {
          throw new Error('為替レートの取得に失敗しました');
        }

        const data = await response.json();
        setExchangeRate(data.rate);
      } catch (error) {
        console.error('Error fetching exchange rate:', error);
      }
    };

    fetchExchangeRate();
  }, []);

  const handleSubmit = (e) => {
    e.preventDefault();
    setError('');

    const trimmedValue = brandTld.trim();

    let sanitizedValue = trimmedValue;
    if (sanitizedValue.startsWith('.')) {
      sanitizedValue = sanitizedValue.substring(1);
    }

    const { isValid, errors } = validateTld.validate({ tld: sanitizedValue });

    if (!isValid) {
      setError(errors[0].message || 'ドメインを入力してください');
      return;
    }

    if (trimmedValue.includes('.') && !trimmedValue.startsWith('.')) {
      setError('.XXX という形で入力してください');
      return;
    }

    window.location.href = `/security/yourbrand/search/?tld=${encodeURIComponent(sanitizedValue)}`;
  };

  const beforeHandleSubmit = () => {
    setIsModalClosable(false);
  };

  const afterHandleSubmit = () => {
    const completeModalContent = (
      <div className="c-modalBox">
        <div className="c-modalBox__img">
          <img src={MAIL_IMAGE} alt="" />
        </div>
        <h2 className="c-modalBox__title">資料請求・ご相談を受付しました</h2>
        <p className="c-modalBox__text">メールアドレスにご案内をお送りいたします。</p>
      </div>
    );
    setModalContent(completeModalContent);
    setIsModalClosable(true);
  };

  const handleButtonClick = () => {
    setModalContent(
      <YourbrandContactForm
        beforeHandleSubmit={beforeHandleSubmit}
        afterHandleSubmit={afterHandleSubmit}
      />,
    );
    show();
  };

  return (
    <div className="p-yourbrand">
      <section className="p-yourbrand__main">
        <div className="p-yourbrandMain">
          <h1 className="p-yourbrandMain__title"><img src={BRANDTLDLOGO} alt=".貴社名" /></h1>
          <p className="p-yourbrandMain__lead">
            まずは「.会社名」や「.ブランド名」が
            <span>取得可能か検索してみましょう</span>
          </p>
          <div className="p-yourbrandMain__search">
            <form onSubmit={handleSubmit}>
              <div className="c-inputSearch">
                <label htmlFor="brandTld">
                  <input
                    type="text"
                    id="brandTld"
                    required={true}
                    value={brandTld}
                    onChange={e => setBrandTld(e.target.value)}
                    placeholder="会社名 or ブランド名を入力（例：GMO）"
                    className="c-inputSearch__input"
                  />
                </label>
                <div className="c-inputSearch__button">
                  <Button type="submit" variant="search">
                    <span className="icon-base icon-sec-search-bold icon-size16" />
                    <span className="c-inputSearch__buttonText">検索</span>
                  </Button>
                </div>
              </div>
            </form>
            {error && <div className="p-yourbrandMain__error">{error}</div>}
            <div className="p-yourbrandMain__links">
              <div className="p-yourbrandLinks">
                <a
                  id="yourbrand_tab_top_diagnostic_link"
                  href="/security/"
                  target="_blank"
                  rel="noopener"
                  className="p-yourbrandLinks__list p-yourbrandLinks__list--arrow"
                >
                  <span className="icon-base icon-size20 icon-sec-impersonation" />
                  {isMobile ? '診断' : 'パスワード漏洩・Webサイトリスク診断'}
                </a>
                <a
                  id="yourbrand_tab_top_chat_link"
                  href="/security/?tab=chat"
                  target="_blank"
                  rel="noopener"
                  className="p-yourbrandLinks__list p-yourbrandLinks__list--arrow"
                >
                  <span className="icon-base icon-size24 icon-sec-other
                  icon-color-gray70"
                  />
                  {isMobile ? '相談' : 'その他のセキュリティ相談'}
                </a>
                <div
                  id="yourbrand_yourbrand_link"
                  className="p-yourbrandLinks__list p-yourbrandLinks__list--active"
                >
                  <span className="icon-base icon-size20 icon-sec-www  icon-color-green" />
                  {isMobile ? '「.貴社名」' : '「.貴社名」取得はこちら'}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="p-yourbrandCorpLogo">
          <h2 className="p-yourbrandCorpLogo__lead">
            <span className="p-yourbrandCorpLogo__leadImg">
              <img
                src={BRANDTLDCORP_LEAD}
                alt=""
              />
            </span>
            世界の企業が活用中
          </h2>
          <div className="p-yourbrandCorpLogo__list">
            <ul className="p-yourbrandCorpLogoBelt">
              <li>
                <img
                  src={BRANDTLDCORP_TOYOTA}
                  alt="TOYOTA"
                />
                <span>.toyota</span>
              </li>
              <li>
                <img
                  src={BRANDTLDCORP_TORAY}
                  alt="TORAY"
                />
                <span>.toray</span>
              </li>
              <li>
                <img
                  src={BRANDTLDCORP_RICOH}
                  alt="RICOH"
                />
                <span>.ricoh</span>
              </li>
              <li>
                <img
                  src={BRANDTLDCORP_SHARP}
                  alt="SHARP"
                />
                <span>.sharp</span>
              </li>
              <li>
                <img
                  src={BRANDTLDCORP_HITACHI}
                  alt="HITACHI"
                />
                <span>.hitachi</span>
              </li>
              <li>
                <img
                  src={BRANDTLDCORP_KDDI}
                  alt="KDDI"
                />
                <span>.kddi</span>
              </li>

              <li>
                <img
                  src={BRANDTLDCORP_DNP}
                  alt="DNP"
                />
                <span>.dnp</span>
              </li>
              <li>
                <img
                  src={BRANDTLDCORP_YODOBASHI}
                  alt="ヨドバシカメラ"
                />
                <span>.yodobashi</span>
              </li>
              <li>
                <img
                  src={BRANDTLDCORP_GMO}
                  alt="GMO"
                />
                <span>.gmo</span>
              </li>
              {/* 以下ループ用 */}
              <li aria-hidden="true">
                <img
                  src={BRANDTLDCORP_TOYOTA}
                  alt="TOYOTA"
                />
                <span>.toyota</span>
              </li>
              <li aria-hidden="true">
                <img
                  src={BRANDTLDCORP_TORAY}
                  alt="TORAY"
                />
                <span>.toray</span>
              </li>
              <li aria-hidden="true">
                <img
                  src={BRANDTLDCORP_RICOH}
                  alt="RICOH"

                />
                <span>.ricoh</span>
              </li>
              <li aria-hidden="true">
                <img
                  src={BRANDTLDCORP_SHARP}
                  alt="SHARP"
                />
                <span>.sharp</span>
              </li>
              <li aria-hidden="true">
                <img
                  src={BRANDTLDCORP_HITACHI}
                  alt="HITACHI"

                />
                <span>.hitachi</span>
              </li>
              <li aria-hidden="true">
                <img
                  src={BRANDTLDCORP_KDDI}
                  alt="KDDI"
                />
                <span>.kddi</span>
              </li>
              <li aria-hidden="true">
                <img
                  src={BRANDTLDCORP_DNP}
                  alt="DNP"
                />
                <span>.dnp</span>
              </li>
              <li aria-hidden="true">
                <img
                  src={BRANDTLDCORP_YODOBASHI}
                  alt="ヨドバシカメラ"
                />
                <span>.yodobashi</span>
              </li>
              <li aria-hidden="true">
                <img
                  src={BRANDTLDCORP_GMO}
                  alt="GMO"
                />
                <span>.gmo</span>
              </li>
            </ul>
          </div>
          <p className="p-yourbrandCorpLogo__text">and more</p>
        </div>
        <div className="p-yourbrandNews">
          <div className="c-news">
            <div className="c-news__linkBox">
              <a
                key="top_oss_link"
                id="top_oss_link"
                href="/security/oss-support/"
                target="_blank"
                rel="noopener"
                className="c-newsLink"
              >
                <span className="icon-base icon-size24 icon-sec-oss c-newsLink__icon" />
                <div className="c-newsLink__textOmit">
                  オープンソース開発者の皆さまへ
                  <span className="c-newsLink__textBreak">
                    <span className="c-newsLink__textEm">無料提供中！</span>
                  </span>
                </div>
              </a>
            </div>
            <div className="c-news__sliderBox">
              <VerticalSlider
                items={[
                  <a
                    key="top_devday2025_link"
                    id="top_devday2025_link"
                    href="https://www.gmo.jp/news/article/9594/"
                    target="_blank"
                    rel="noopener"
                    className="c-newsLink"
                  >
                    <span className="icon-base icon-size24 icon-sec-news c-newsLink__icon" />
                    <div className="c-newsLink__textOmit">
                      DevDay2025 Security Night開催！
                    </div>
                  </a>,
                  <a
                    key="top_defcon_link"
                    id="top_defcon_link"
                    href="https://www.gmo.jp/news/article/9592/"
                    target="_blank"
                    rel="noopener"
                    className="c-newsLink"
                  >
                    <span className="icon-base icon-size24 icon-sec-news c-newsLink__icon" />
                    <div className="c-newsLink__textOmit">
                      GMOホワイトハッカー、
                      <span className="c-newsLink__textBreak">DEFCON出陣！</span>
                    </div>
                  </a>,
                  // <a
                  //   key="top_locked_link"
                  //   id="top_locked_link"
                  //   href="https://www.gmo.jp/news/article/9600/"
                  //   target="_blank"
                  //   rel="noopener"
                  //   className="c-newsLink"
                  // >
                  //   <span className="icon-base icon-size24 icon-sec-news c-newsLink__icon" />
                  //   <div className="c-newsLink__textOmit">
                  //     世界最大規模の国際サイバー防衛演習
                  //     <span className="c-newsLink__textBreak">に初参加</span>
                  //   </div>
                  // </a>,
                ]}
                itemHeight={useMediaQuery({ query: '(max-width: 600px)' }) ? 50 : 26}
                visibleCount={1}
              />
            </div>
          </div>
        </div>
      </section>
      <section className="p-yourbrand__sec p-yourbrand__sec--gray gtm-view" id="section_yourbrand_description">
        <div className="p-yourbrand__inner">
          <div className="p-yourbrandColumn p-parallaxObj">
            <div className="p-yourbrandColumn__01">
              <img src={BRANDTLD_IMAGE01} alt="" />
            </div>
            <div className="p-yourbrandColumn__02">
              <h2 className="p-yourbrandColumn__title">
                10年に1度の
                <br />
                新規受付スタート
              </h2>
              <p className="p-yourbrandColumn__text">2026年春、約10年ぶりに.貴社名（ブランドTLD）の新規受付が再開されます。企業のブランド価値を高め、信頼性向上にもつながる特別なドメインの取得を、この貴重な機会にぜひご検討ください。</p>
            </div>
          </div>
          <div className="p-yourbrandColumn p-parallaxObj">
            <div className="p-yourbrandColumn__01">
              <h2 className="p-yourbrandColumn__title">
                .貴社名
                <span className="p-yourbrand__titleS">（ブランドTLD）</span>
                とは
              </h2>
              <p className="p-yourbrandColumn__text">.貴社名（ブランドTLD）はICANNが認可する企業専用のトップレベルドメインです。厳格な審査を通過した企業のみが独占利用でき、なりすまし・フィッシング対策や一貫したブランディング実現に最適です。</p>
            </div>
            <div className="p-yourbrandColumn__02">
              <img src={BRANDTLD_IMAGE02} alt="" />
            </div>
          </div>
          <div className="p-yourbrandColumn p-parallaxObj">
            <div className="p-yourbrandColumn__01">
              <img src={BRANDTLD_IMAGE03} alt="" />
            </div>
            <div className="p-yourbrandColumn__02">
              <h2 className="p-yourbrandColumn__title">
                国内シェア圧倒的No.1
              </h2>
              <p className="p-yourbrandColumn__text">
                GMOインターネットグループは、.貴社名（ブランドTLD）に関して、国内シェア圧倒的No.1の申請・運用サポートをしています。豊富な運用実績とノウハウを有し、日本企業に合った活用方法をご提供します。
              </p>
            </div>
          </div>
        </div>
      </section>
      <section className="p-yourbrand__sec gtm-view" id="section_yourbrand_merit">
        <div className="p-yourbrand__inner">
          <h2 className="p-yourbrand__title p-yourbrand__title--white p-parallaxObj">
            「.貴社名」導入のメリット
          </h2>
          <ol className="p-yourbrandMerit p-parallaxObj">
            <li>
              <div className="p-yourbrandMeritCard">
                <span className="p-yourbrandMeritCard__order">01</span>
                <h3 className="p-yourbrandMeritCard__title">
                  ブランドの信頼性を
                  <br />
                  “ドメイン名”で可視化
                </h3>
                <p className="p-yourbrandMeritCard__text">企業専用のトップレベルドメインのため、URLそのものが「ここが公式です」と伝える視覚的な信頼のシグナルとなり、企業ブランディングの一貫として機能します。</p>
              </div>
            </li>
            <li>
              <div className="p-yourbrandMeritCard">
                <span className="p-yourbrandMeritCard__order">02</span>
                <h3 className="p-yourbrandMeritCard__title">
                  なりすましを
                  <br />
                  “構造的に防ぐ”
                  <br />
                  セキュリティ対策
                </h3>
                <p className="p-yourbrandMeritCard__text">
                  類似ドメインによる詐欺サイトの発生を根本から防止します。
                  <br />
                  さらに、メールやWebを通じたなりすまし攻撃の入口を封じる構造的セキュリティ施策にもなります。
                </p>
              </div>
            </li>
            <li>
              <div className="p-yourbrandMeritCard">
                <span className="p-yourbrandMeritCard__order">03</span>
                <h3 className="p-yourbrandMeritCard__title">
                  ガバナンス強化と
                  <br />
                  管理の効率化
                </h3>
                <p className="p-yourbrandMeritCard__text">子会社等を含めたサプライチェーン全体でブランドTLDを利用することで、自ドメインとサーバーの把握が容易になります。結果として、サイバー攻撃のリスク軽減につながります。</p>
              </div>
            </li>
          </ol>
        </div>
      </section>
      <section className="p-yourbrand__sec p-yourbrand__sec--gray gtm-view" id="section_yourbrand_plan">
        <div className="p-yourbrand__inner p-parallaxObj">
          <h2 className="p-yourbrand__title">
            選べるプラン
          </h2>
          <div className="p-yourbrand__table p-yourbrand__table--scroll">
            <table className="p-yourbrandTable p-yourbrandTable--plan">
              <thead>
                <tr>
                  <th></th>
                  <th>
                    <span className="p-yourbrandTable__badge p-yourbrandTable__badge--null" />
                    <span className="p-yourbrandTable__planName">Basic</span>
                    <span className="p-yourbrandTable__planText">最小限のサポートのみご希望の企業に最適</span>
                  </th>
                  <th>
                    <span className="p-yourbrandTable__badge">おすすめ</span>
                    <span className="p-yourbrandTable__planName">Standard</span>
                    <span className="p-yourbrandTable__planText">申請代行をご希望の企業に最適</span>
                  </th>
                  <th>
                    <span className="p-yourbrandTable__badge p-yourbrandTable__badge--null" />
                    <span className="p-yourbrandTable__planName">Advanced</span>
                    <span className="p-yourbrandTable__planText">さらに充実したサポートをご希望の企業に最適</span>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td colSpan="4" className="p-yourbrandTable__sectionHeader">
                    申請フェーズ（初回のみ）
                  </td>
                </tr>
                <tr>
                  <td className="p-yourbrandTable__feature">
                    レジストリ
                    <br />
                    システムセットアップ
                  </td>
                  <td className="p-yourbrandTable__check">○</td>
                  <td className="p-yourbrandTable__check">○</td>
                  <td className="p-yourbrandTable__check">○</td>
                </tr>
                <tr>
                  <td className="p-yourbrandTable__feature">申請書作成代行</td>
                  <td></td>
                  <td className="p-yourbrandTable__check">○</td>
                  <td className="p-yourbrandTable__check">○</td>
                </tr>
                <tr>
                  <td className="p-yourbrandTable__feature">申請/審査サポート</td>
                  <td></td>
                  <td className="p-yourbrandTable__check">○</td>
                  <td className="p-yourbrandTable__check">○</td>
                </tr>
                <tr>
                  <td className="p-yourbrandTable__feature">導入サポート</td>
                  <td></td>
                  <td></td>
                  <td className="p-yourbrandTable__check">○</td>
                </tr>
                <tr>
                  <td colSpan="4" className="p-yourbrandTable__sectionHeader">
                    運用フェーズ（次回以降/毎年）
                  </td>
                </tr>
                <tr>
                  <td className="p-yourbrandTable__feature">レジストリシステム提供</td>
                  <td className="p-yourbrandTable__check">○</td>
                  <td className="p-yourbrandTable__check">○</td>
                  <td className="p-yourbrandTable__check">○</td>
                </tr>
                <tr>
                  <td className="p-yourbrandTable__feature">運用サポート</td>
                  <td></td>
                  <td className="p-yourbrandTable__check">○</td>
                  <td className="p-yourbrandTable__check">○</td>
                </tr>
                <tr>
                  <td className="p-yourbrandTable__feature">セキュリティパッケージ</td>
                  <td></td>
                  <td></td>
                  <td className="p-yourbrandTable__check">○</td>
                </tr>
                <tr>
                  <td colSpan="4" className="p-yourbrandTable__sectionHeader">オプション</td>
                </tr>
                <tr className="p-yourbrandTable__contact">
                  <td colSpan="4">お問い合わせください</td>
                </tr>
              </tbody>
            </table>
          </div>
          <div className="p-yourbrand__button">
            <Button id="contact_yourbrand_plan" onClick={handleButtonClick} variant="brandTldAccent" widthSize="full">
              資料請求・ご相談（無料）
            </Button>
            <div className="p-yourbrand__note">※申請には約一年を要します。早めのご準備をおすすめします</div>
          </div>
        </div>
      </section>
      <section className="p-yourbrand__sec gtm-view" id="section_yourbrand_price">
        <div className="p-yourbrand__inner p-parallaxObj">
          <h2 className="p-yourbrand__title">
            価格
          </h2>
          <table className="p-yourbrandTable p-yourbrandTable--cost">
            <colgroup>
              <col className="p-yourbrandTable__col--feature" />
              <col className="p-yourbrandTable__col--value" />
            </colgroup>

            <tbody>
              <tr>
                <td colSpan="2" className="p-yourbrandTable__sectionHeader">
                  申請フェーズ（初回のみ）
                </td>
              </tr>
              <tr>
                <td className="p-yourbrandTable__feature">
                  ICANN申請費
                  <sup>※1 ※2</sup>
                </td>
                <td className="p-yourbrandTable__price">
                  22.7万ドル
                  <span>
                    （
                    {exchangeRate
                      ? formatJapaneseYen(roundToSignificantDigits(ICANN_INITIAL_FEE_USD * exchangeRate, 2))
                      : '約3,400万円'}
                    <sup>※3</sup>
                    ）
                  </span>
                </td>
              </tr>
              <tr>
                <td className="p-yourbrandTable__feature">手数料</td>
                <td className="p-yourbrandTable__price">
                  250万円
                  <sup>※4</sup>
                  ～
                </td>
              </tr>
              <tr>
                <td colSpan="2" className="p-yourbrandTable__sectionHeader">
                  運用フェーズ（次回以降/毎年）
                </td>
              </tr>
              <tr>
                <td className="p-yourbrandTable__feature">
                  ICANN運用維持費用
                  <sup>※1 ※2</sup>
                </td>
                <td className="p-yourbrandTable__price">
                  2.6万ドル
                  <span>
                    （
                    {exchangeRate
                      ? formatJapaneseYen(roundToSignificantDigits(ICANN_RENEWAL_FEE_USD * exchangeRate, 2))
                      : '約390万円'}
                    <sup>※3</sup>
                    ）
                  </span>
                </td>
              </tr>
              <tr>
                <td className="p-yourbrandTable__feature">手数料</td>
                <td className="p-yourbrandTable__price">
                  200万円
                  <sup>※4</sup>
                  ～
                </td>
              </tr>
            </tbody>
          </table>
          <p className="p-yourbrandTable__note">
            ※1&nbsp;ICANN申請費は実費になります。
            <br />
            ※2&nbsp;ICANN申請費は予告なく変更になる場合があります。
            <br />
            ※3&nbsp;日本円換算は、毎日9:00時点の為替レートを用いて算出しております。
            <br />
            ※4&nbsp;Basicプランの価格になります。
          </p>
          <div className="p-yourbrand__button">
            <Button id="contact_yourbrand_price" onClick={handleButtonClick} variant="brandTldAccent" widthSize="full">
              資料請求・ご相談（無料）
            </Button>
            <div className="p-yourbrand__note">※申請には約一年を要します。早めのご準備をおすすめします</div>
          </div>
        </div>
      </section>
      <section className="p-yourbrand__sec p-yourbrand__sec--gray gtm-view" id="section_yourbrand_exist">
        <div className="p-yourbrand__inner p-parallaxObj">
          <h2 className="p-yourbrand__title">
            導入事例
            <span className="p-yourbrand__titleS">（一例）</span>
          </h2>
          <ul className="p-yourbrandCorpEx">
            <li className="p-yourbrandCorpEx__item">
              <img
                className="p-yourbrandCorpEx__logo"
                src={BRANDTLDCORP_TOYOTA}
                alt="TOYOTA"
              />
              <p className="p-yourbrandCorpEx__company">トヨタ自動車株式会社</p>

              <p className="p-yourbrandCorpEx__tld">.toyota</p>
            </li>
            <li className="p-yourbrandCorpEx__item">
              <img
                className="p-yourbrandCorpEx__logo"
                src={BRANDTLDCORP_TORAY}
                alt="TORAY"
              />
              <p className="p-yourbrandCorpEx__company">東レ株式会社</p>
              <p className="p-yourbrandCorpEx__tld">.toray</p>
            </li>
            <li className="p-yourbrandCorpEx__item">
              <img
                className="p-yourbrandCorpEx__logo"
                src={BRANDTLDCORP_CANON}
                alt="Canon"
              />
              <p className="p-yourbrandCorpEx__company">キヤノン株式会社</p>
              <p className="p-yourbrandCorpEx__tld">.canon</p>
            </li>
            <li className="p-yourbrandCorpEx__item">
              <img
                className="p-yourbrandCorpEx__logo"
                src={BRANDTLDCORP_RICOH}
                alt="RICOH"
              />
              <p className="p-yourbrandCorpEx__company">株式会社リコー</p>
              <p className="p-yourbrandCorpEx__tld">.ricoh</p>
            </li>
            <li className="p-yourbrandCorpEx__item">
              <img
                className="p-yourbrandCorpEx__logo"
                src={BRANDTLDCORP_SHARP}
                alt="SHARP"
              />
              <p className="p-yourbrandCorpEx__company">シャープ株式会社</p>
              <p className="p-yourbrandCorpEx__tld">.sharp</p>
            </li>
            <li className="p-yourbrandCorpEx__item">
              <img
                className="p-yourbrandCorpEx__logo"
                src={BRANDTLDCORP_HITACHI}
                alt="HITACHI"
              />
              <p className="p-yourbrandCorpEx__company">株式会社日立製作所</p>
              <p className="p-yourbrandCorpEx__tld">.hitachi</p>
            </li>
            <li className="p-yourbrandCorpEx__item">
              <img
                className="p-yourbrandCorpEx__logo"
                src={BRANDTLDCORP_KDDI}
                alt="KDDI"
              />
              <p className="p-yourbrandCorpEx__company">KDDI株式会社</p>
              <p className="p-yourbrandCorpEx__tld">.kddi</p>
            </li>

            <li className="p-yourbrandCorpEx__item">
              <img
                className="p-yourbrandCorpEx__logo"
                src={BRANDTLDCORP_DNP}
                alt="DNP"
              />
              <p className="p-yourbrandCorpEx__company">大日本印刷株式会社</p>
              <p className="p-yourbrandCorpEx__tld">.dnp</p>
            </li>
            <li className="p-yourbrandCorpEx__item">
              <img
                className="p-yourbrandCorpEx__logo"
                src={BRANDTLDCORP_YODOBASHI}
                alt="ヨドバシカメラ"
              />
              <p className="p-yourbrandCorpEx__company">株式会社ヨドバシカメラ</p>
              <p className="p-yourbrandCorpEx__tld">.yodobashi</p>
            </li>
            <li className="p-yourbrandCorpEx__item">
              <img
                className="p-yourbrandCorpEx__logo"
                src={BRANDTLDCORP_GMO}
                alt="GMO"
              />
              <p className="p-yourbrandCorpEx__company">
                GMOインターネットグループ株式会社
              </p>
              <p className="p-yourbrandCorpEx__tld">.gmo</p>
            </li>
          </ul>
          <div className="p-yourbrand__button">
            <Button id="contact_yourbrand_exist" onClick={handleButtonClick} variant="brandTldAccent" widthSize="full">
              資料請求・ご相談（無料）
            </Button>
            <div className="p-yourbrand__note">※申請には約一年を要します。早めのご準備をおすすめします</div>
          </div>
        </div>
      </section>
      <Modal>
        {modalContent}
      </Modal>
    </div>
  );
}

export default Yourbrand;
