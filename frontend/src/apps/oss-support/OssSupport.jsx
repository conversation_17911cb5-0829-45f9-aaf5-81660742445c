import { useState } from 'react';
import { useMediaQuery } from 'react-responsive';
import ossBadgeOl from '../../assets/img/oss_badge_ol.svg';
import ossHedgedoc from '../../assets/img/oss_hedgedoc.png';
import ossLogo from '../../assets/img/oss_logo.svg';
import ossLogoYoko from '../../assets/img/oss_logo_yoko.svg';
import ossNextJs from '../../assets/img/oss_next_js.png';
import ossReportImg from '../../assets/img/oss_report_img.png';
import ossSec01 from '../../assets/img/oss_sec_01.png';
import ossSec02 from '../../assets/img/oss_sec_02.png';
import ossSec03 from '../../assets/img/oss_sec_03.png';
import ossSec04 from '../../assets/img/oss_sec_04.png';
import ossTakumi from '../../assets/img/oss_takumi.png';
import ossVim from '../../assets/img/oss_vim.png';
import Button from '../../common/components/Button.jsx';
import VerticalSlider from '../../common/components/VerticalSlider';
import { isValidUrl } from '../../common/utils';
import Showcase from './takumi-demo/Showcase.jsx';

const OSSLOGO = ossLogo;
const OSS_VIM = ossVim;
const OSS_NEXTJS = ossNextJs;
const OSS_HEDGEDOC = ossHedgedoc;
const OSS_SEC_01 = ossSec01;
const OSS_SEC_02 = ossSec02;
const OSS_SEC_03 = ossSec03;
const OSS_SEC_04 = ossSec04;
const OSS_BADGE_OL = ossBadgeOl;
const OSSTAKUMI = ossTakumi;
const OSSLOGOYOKO = ossLogoYoko;
const OSS_REPORT_IMG = ossReportImg;

function OssSupport() {
  const [url, setUrl] = useState('');
  const [error, setError] = useState('');

  const isMobile = useMediaQuery({ query: '(max-width: 600px)' });

  const handleSubmit = (e) => {
    e.preventDefault();
    setError('');

    if (isValidUrl(url)) {
      const targetUrl = `https://flatt.tech/oss/gmo?url=${encodeURIComponent(url)}`;
      window.open(targetUrl, '_blank');
      return;
    } else {
      setError('URLを入力してください');
      return;
    }
  };

  return (
    <div className="p-oss">
      <section className="p-oss__main">
        <div className="p-ossMain">
          <h1 className="p-ossMain__title"><img src={OSSLOGO} alt="GMOオープンソース開発者応援プログラム" /></h1>
          <p className="p-ossMain__lead">
            OSSにセキュリティAIを
            <span className="p-ossMain__leadRed">無料提供</span>
            。
            <span className="p-ossMain__spBreak">今すぐ対象か確認しましょう</span>
          </p>
          <div className="p-ossMain__search">
            <form onSubmit={handleSubmit} className="p-ossSearch">
              <div className="c-inputSearch">
                <label htmlFor="oss">
                  <input
                    type="text"
                    id="oss"
                    required={true}
                    value={url}
                    onChange={e => setUrl(e.target.value)}
                    placeholder={isMobile ? 'あなたのOSSのURLを入力' : 'あなたのOSSのURLを入力（例: https://github.com/octocat/Hello-World）'}
                    className="c-inputSearch__input c-inputSearch__input--border"
                  />
                </label>
                <div className="c-inputSearch__button">
                  <Button type="submit" id="contact_oss_search_upper" variant="search">
                    <span className="icon-base icon-sec-search-bold icon-size16" />
                    <span className="c-inputSearch__buttonText">確認</span>
                  </Button>
                </div>
              </div>
              {error && <div className="p-ossSearch__error">{error}</div>}
            </form>
          </div>
          <div className="p-ossMain__news">
            <div className="c-news">
              <div className="c-news__linkBox">
                <a
                  key="top_yourbrand_link"
                  id="top_yourbrand_link"
                  href="/security/yourbrand/"
                  target="_blank"
                  rel="noopener"
                  className="c-newsLink"
                >
                  <span className="icon-base icon-size20 icon-sec-www c-newsLink__icon" />
                  <div className="c-newsLink__textOmit">
                    10年に1度のチャンス！
                    <span className="c-newsLink__textBreak">
                      「
                      <span className="c-newsLink__textEm">.貴社名</span>
                      」でなりすまし対策
                    </span>
                  </div>
                </a>
              </div>
              <div className="c-news__sliderBox">
                <VerticalSlider
                  items={[
                    <a
                      key="top_devday2025_link"
                      id="top_devday2025_link"
                      href="https://www.gmo.jp/news/article/9594/"
                      target="_blank"
                      rel="noopener"
                      className="c-newsLink"
                    >
                      <span className="icon-base icon-size24 icon-sec-news c-newsLink__icon" />
                      <div className="c-newsLink__textOmit">
                        DevDay2025 Security Night開催！
                      </div>
                    </a>,
                    <a
                      key="top_defcon_link"
                      id="top_defcon_link"
                      href="https://www.gmo.jp/news/article/9592/"
                      target="_blank"
                      rel="noopener"
                      className="c-newsLink"
                    >
                      <span className="icon-base icon-size24 icon-sec-news c-newsLink__icon" />
                      <div className="c-newsLink__textOmit">
                        GMOホワイトハッカー、
                        <span className="c-newsLink__textBreak">DEFCON出陣！</span>
                      </div>
                    </a>,
                    // <a
                    //   key="top_locked_link"
                    //   id="top_locked_link"
                    //   href="https://www.gmo.jp/news/article/9600/"
                    //   target="_blank"
                    //   rel="noopener"
                    //   className="c-newsLink"
                    // >
                    //   <span className="icon-base icon-size24 icon-sec-news c-newsLink__icon" />
                    //   <div className="c-newsLink__textOmit">
                    //     世界最大規模の国際サイバー防衛演習
                    //     <span className="c-newsLink__textBreak">に初参加</span>
                    //   </div>
                    // </a>,
                  ]}
                  itemHeight={useMediaQuery({ query: '(max-width: 600px)' }) ? 50 : 26}
                  visibleCount={1}
                />
              </div>
            </div>
          </div>
        </div>
      </section>
      <section className="p-oss__sec p-oss__sec--gray gtm-view" id="section_oss_contribution">
        <div className="p-oss__inner p-parallaxObj">
          <h2 className="p-oss__title p-oss__title--center">
            セキュリティ診断AIエージェント「Takumi byGMO」は
            <br />
            世界中のOSSの安全に貢献しています
          </h2>
          <ul className="p-ossCardList p-parallaxObj">
            <li>
              <article className="p-ossCard">
                <div className="p-ossCard__label">CVE-2025-29768</div>
                <h3 className="p-ossCard__title">
                  potential data loss with zip.vim and special crafted zip files in Vim &lt; v9&#46;1&#46;1198
                </h3>
                <p className="p-ossCard__text">vim/vim</p>
                <div className="p-ossCard__img"><img src={OSS_VIM} alt="vim" /></div>
              </article>
            </li>
            <li>
              <article className="p-ossCard">
                <div className="p-ossCard__label">CVE-2025-30218</div>
                <h3 className="p-ossCard__title">
                  x-middleware-subrequest-id may be leaked to external hosts
                </h3>
                <p className="p-ossCard__text">vercel/next.js</p>
                <div className="p-ossCard__img"><img src={OSS_NEXTJS} alt="next.js" /></div>
              </article>
            </li>
            <li>
              <article className="p-ossCard">
                <div className="p-ossCard__label">CVE-2025-31483</div>
                <h3 className="p-ossCard__title">
                  Stored XSS in Miniflux Media Proxy due to improper Content-Security-Policy configuration
                </h3>
                <p className="p-ossCard__text">miniflux/v2</p>
              </article>
            </li>
            <li>
              <article className="p-ossCard">
                <div className="p-ossCard__label">CVE-2025-32391</div>
                <h3 className="p-ossCard__title">
                  XSS possibility through malicious SVG uploads
                </h3>
                <p className="p-ossCard__text">hedgedoc/hedgedoc</p>
                <div className="p-ossCard__img"><img src={OSS_HEDGEDOC} alt="HedgeDoc" /></div>
              </article>
            </li>
          </ul>
        </div>
      </section>
      <section className="p-oss__sec gtm-view" id="section_oss_oss">
        <div className="p-oss__inner">
          <div className="p-ossColumn p-parallaxObj">
            <h2 className="p-ossColumn__title">
              インターネットを支える
              <br />
              オープンソースソフトウェア
              <span className="p-ossColumn__titleS">（OSS）</span>
            </h2>
            <div className="p-ossColumn__img">
              <img src={OSS_SEC_01} alt="" />
            </div>
            <div className="p-ossColumn__textBox">
              <p className="p-ossColumn__text">世界中のソフトウェアは、無数のオープンソースソフトウェア（OSS）を部品としてつくられています。また、今日では、OSSをそのまま自社システムとして利用する場面も少なくありません。</p>
              <p className="p-ossColumn__text">
                Linux Foundation は、
                <span className="p-ossColumn__textEm">日本企業の82%</span>
                がOSSを中程度から広範に使用していると報告しています
                <span className="p-ossColumn__textS">※</span>
                。
              </p>
              <div className="p-ossColumn__note">
                <p>※ Linux Foundation「World of Open Source: Japan Spotlight 2023」https://www.linuxfoundation.org/research/world-of-open-source-japan-2023</p>
              </div>
            </div>
          </div>
          <div className="p-ossColumn p-ossColumn--even p-parallaxObj">
            <h2 className="p-ossColumn__title">
              開発者は縁の下の力持ち
              <br />
              セキュリティの期待も背負う
            </h2>
            <div className="p-ossColumn__img">
              <img src={OSS_SEC_02} alt="" />
            </div>
            <div className="p-ossColumn__textBox">
              <p className="p-ossColumn__text">
                OSSの利用が一般的になるにつれ、OSSへの期待も増してきています。
                <br />
                GitHubによる調査
                <span className="p-ossColumn__textS">※</span>
                では、
                <span className="p-ossColumn__textEm">設計・開発初期からのセキュリティへの取り組み</span>
                （Secure by Design）を、82%もの回答者が重要と考えていることが明らかになっています。
                <br />
                無償奉仕に近い形で社会を支えているのに、OSS開発者には過度な期待がのしかかっている、ということです。
              </p>
              <div className="p-ossColumn__note">
                <p>※ GitHub「Open Source Survey」https://opensourcesurvey.org/2024/</p>
              </div>
            </div>
          </div>
          <div className="p-ossColumn p-parallaxObj">
            <div className="p-ossColumn__img">
              <img src={OSS_SEC_03} alt="" />
            </div>
            <h2 className="p-ossColumn__title">
              インターネットを支える人に
              <br />
              No.1のセキュリティAIを
              <span className="p-ossColumn__titleRed">無料</span>
              で
            </h2>
            <div className="p-ossColumn__textBox">
              <p className="p-ossColumn__text">
                GMOインターネットグループは、「GMOオープンソース開発者応援プログラム」を通して、
                <span className="p-ossColumn__textEm">インターネットを支えるオープンソース開発者にセキュリティ診断AIエージェント「Takumi byGMO」を無料提供</span>
                します。
              </p>
              <p className="p-ossColumn__text">これによりオープンソース開発者は、利用者からのセキュリティへの期待に楽に応えつつ、ソフトウェアづくりに集中することができます。</p>
            </div>
          </div>
        </div>
      </section>
      <section className="p-oss__sec p-oss__sec--gray gtm-view" id="section_oss_support">
        <div className="p-oss__inner">
          <h2 className="p-oss__titleImg p-parallaxObj">
            <picture>
              <source srcSet={OSSLOGO} media="(max-width:599px)" />
              <img src={OSSLOGOYOKO} alt="GMOオープンソース開発者応援プログラム" />
            </picture>
          </h2>
          <div className="p-ossFundColumn p-parallaxObj">
            <h3 className="p-ossFundColumn__title">
              ご支援内容
            </h3>
            <p className="p-ossFundColumn__lead">
              GMO Flatt Securityの
              <br />
              セキュリティ診断AIエージェント「Takumi byGMO」の利用枠を
              <span className="p-ossFundColumn__leadRed">無料</span>
              でご提供いたします。
            </p>
            <div className="p-ossFundColumn__demo">
              <h4 className="p-ossFundColumn__demoTitle">「Takumi byGMO」に任せられること</h4>
              <Showcase />
              <div className="p-ossFundColumn__demoImg"><img src={OSSTAKUMI} alt="" /></div>
            </div>
            <div className="p-ossFundColumn__detail">
              <ul className="p-ossFundDetailList">
                <li>
                  <div className="p-ossFundDetail">
                    <span className="p-ossFundDetail__check" aria-hidden="true" />
                    <p>Takumi byGMO はWeb/Slack/GitHubで利用できる、セキュリティのAIエージェントです。</p>
                  </div>
                </li>
                <li>
                  <div className="p-ossFundDetail">
                    <span className="p-ossFundDetail__check" aria-hidden="true" />
                    <p>Pull Requestのセキュリティレビューや、脆弱性発見、発見時のトリアージに利用できます。</p>
                  </div>
                </li>
                <li>
                  <div className="p-ossFundDetail">
                    <span className="p-ossFundDetail__check" aria-hidden="true" />
                    <p>毎月一定の利用枠内でご利用いただけます。</p>
                  </div>
                </li>
                <li>
                  <div className="p-ossFundDetail">
                    <span className="p-ossFundDetail__check" aria-hidden="true" />
                    <p>ご支援中は、以下のREADME.mdへのバッジ掲載をお願いいたします。</p>
                  </div>
                  <div className="p-ossFundDetailImg">
                    <img src={OSS_BADGE_OL} alt="" />
                  </div>
                </li>
              </ul>
            </div>
          </div>
          <div className="p-ossFlow p-parallaxObj">
            <h3 className="p-ossFlow__title">
              ご利用までの流れ
            </h3>
            <ol className="p-ossFlowList">
              <li>
                <div className="p-ossFlowCard">
                  <h4 className="p-ossFlowCard__title">
                    <span className="p-ossFlowCard__order">1</span>
                    応募
                  </h4>
                  <p className="p-ossFlowCard__text">GitHubリポジトリのリンク等、オープンソースソフトウェアに関するURLを、ご連絡先とともにご提出ください。</p>
                </div>
              </li>
              <li>
                <div className="p-ossFlowCard">
                  <h4 className="p-ossFlowCard__title">
                    <span className="p-ossFlowCard__order">2</span>
                    事務局が審査
                  </h4>
                  <p className="p-ossFlowCard__text">頂戴したオープンソースソフトウェアの情報等を拝見し、「Takumi byGMO」を有効にご活用いただける状況かを確認・審査いたします。</p>
                </div>
              </li>
              <li>
                <div className="p-ossFlowCard">
                  <h4 className="p-ossFlowCard__title">
                    <span className="p-ossFlowCard__order">3</span>
                    利用開始
                  </h4>
                  <p className="p-ossFlowCard__text">その他支援に必要なご対応をいただいた後、セキュリティ診断AIエージェント「Takumi byGMO」利用のために必要なアカウント等をご共有いたします。</p>
                </div>
              </li>
            </ol>
          </div>
        </div>
      </section>
      <section className="p-oss__sec p-oss__sec--blue gtm-view" id="section_oss_report">
        <div className="p-oss__inner">
          <div className="p-ossReport p-parallaxObj">
            <h2 className="p-ossReport__title">
              <span className="p-ossReport__titleS">
                セキュリティ診断AIエージェント
                <br />
                「Takumi byGMO」は
              </span>
              <br />
              脆弱性検知率 No.1
            </h2>
            <div className="p-ossReport__textBox">
              <p className="p-ossReport__text">
                Takumi byGMO は最新LLMと、開発元GMO Flatt Security株式会社が培った脆弱性リサーチのナレッジを結集して作られています。
              </p>
              <p className="p-ossReport__text">
                Devin, Cline, Claude Codeといった他のコーディングAIエージェントと比較し、検知率、正確性、ノイズ率等、複数の評価指標で
                <span className="p-ossReport__textEm">No.1</span>
                <span className="p-ossReport__textS">※</span>
                を獲得しています。
              </p>
              <div className="p-ossReport__note">
                <p>※ No.1: 2025年6月3日時点、GMO Flatt Security株式会社が行った、数種のコーディングエージェントとの比較に基づく。</p>
              </div>
            </div>
            <div className="p-ossReport__img">
              <img src={OSS_REPORT_IMG} alt="" />
              <div className="p-ossReport__button">
                <Button
                  id="oss_benchmark"
                  as="a"
                  href="https://flatt.tech/takumi/form/benchmark"
                  target="_blank"
                  rel="noopener"
                  referrerPolicy="strict-origin-when-cross-origin"
                  variant="ossSecondary"
                  widthSize="full"
                >
                  性能レポートをダウンロード
                  <span className="icon-base icon-sec-link icon-size14 icon-color-oss-blue icon-rotate135" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section className="p-oss__sec gtm-view" id="section_oss_takumi">
        <div className="p-oss__inner p-parallaxObj">
          <div className="p-ossColumn p-ossColumn--even">
            <h2 className="p-ossColumn__title">
              非公開のコードも
              <br />
              「Takumi byGMO」で安全に
              <span className="p-ossColumn__titleS p-ossColumn__pcBreak">（有償）</span>
            </h2>
            <div className="p-ossColumn__img">
              <img src={OSS_SEC_04} alt="" />
            </div>
            <div className="p-ossColumn__textBox">
              <p className="p-ossColumn__text">
                オープンソースソフトウェア以外（法人で開発しているSaaS 等のシステム）に対しても、GMOインターネットグループのNo.1セキュリティ診断AIエージェント「Takumi byGMO」を
                <span className="p-ossColumn__textEm">月額70,000円（税抜）</span>
                で利用できます。
              </p>
            </div>
          </div>
          <div className="p-oss__buttonBox">
            <Button
              id="oss_usecase"
              as="a"
              href="https://flatt.tech/takumi/usecases"
              target="_blank"
              rel="noopener"
              referrerPolicy="strict-origin-when-cross-origin"
              variant="ossSecondary"
              widthSize="full"
            >
              詳細を確認
            </Button>
            <Button
              id="oss_trial"
              as="a"
              href="https://cloud.shisho.dev/hello/takumi?utm_source=gmo_netsec_lp&utm_medium=gmo_jp&utm_campaign=gmo_netsec"
              target="_blank"
              rel="noopener"
              referrerPolicy="strict-origin-when-cross-origin"
              variant="ossPrimary"
              widthSize="full"
            >
              いますぐ試す
            </Button>
          </div>
        </div>
      </section>
      <section className="p-oss__sec p-oss__sec--gray gtm-view" id="section_oss_search_lower">
        <div className="p-oss__inner p-oss__inner--bottom">
          <div className="p-ossEnd p-parallaxObj">
            <h2 className="p-ossEnd__title">
              OSSにセキュリティAIを
              <span className="p-ossEnd__titleRed">無料提供</span>
              <span className="p-ossEnd__pcBreak">
                今すぐ対象か確認しましょう
              </span>
            </h2>
            <div className="p-ossEnd__search">
              <form onSubmit={handleSubmit} className="p-ossSearch">
                <div className="c-inputSearch">
                  <label htmlFor="ossBottom">
                    <input
                      type="text"
                      id="ossBottom"
                      required={true}
                      value={url}
                      onChange={e => setUrl(e.target.value)}
                      placeholder={isMobile ? 'あなたのOSSのURLを入力' : 'あなたのOSSのURLを入力（例: https://github.com/octocat/Hello-World）'}
                      className="c-inputSearch__input c-inputSearch__input--border"
                    />
                  </label>
                  <div className="c-inputSearch__button">
                    <Button type="submit" id="contact_oss_search_lower" variant="search">
                      <span className="icon-base icon-sec-search-bold icon-size16" />
                      <span className="c-inputSearch__buttonText">確認</span>
                    </Button>
                  </div>
                </div>
                {error && <div className="p-ossSearch__error">{error}</div>}
              </form>
            </div>
            <div className="p-ossEnd__img"><img src={OSSTAKUMI} alt="" /></div>
          </div>
        </div>
      </section>
    </div>
  );
}

export default OssSupport;
