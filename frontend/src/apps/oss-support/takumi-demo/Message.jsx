import { motion } from 'framer-motion';
import PropTypes from 'prop-types';
import ChevronRight from './ChevronRight';
import Panda from './Panda';

function Message({ message, isExpanded, onToggleExpand }) {
  switch (message.type) {
    case 'user':
      return (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          style={{ display: 'flex', alignItems: 'flex-start', gap: '0.75rem' }}
        >
          <div style={{
            display: 'flex',
            height: '1.75rem',
            width: '1.75rem',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: '0.375rem',
            backgroundColor: '#f3f4f6',
          }}
          >
            <span style={{ fontWeight: '500', color: '#374151', fontSize: '0.75rem' }}>U</span>
          </div>
          <div style={{ flex: '1' }}>
            <p style={{ color: '#1f2937', fontSize: '0.875rem' }}>{message.content}</p>
            {message.timestamp && (
              <span style={{ color: '#9ca3af', fontSize: '0.75rem' }}>{message.timestamp}</span>
            )}
          </div>
        </motion.div>
      );

    case 'takumi':
      return (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          style={{ display: 'flex', alignItems: 'flex-start', gap: '0.75rem' }}
        >
          <Panda style={{ height: '1.75rem', width: '1.75rem', borderRadius: '0.375rem' }} />
          <div style={{ flex: '1', display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
            <p style={{ color: '#1f2937', fontSize: '0.875rem' }}>{message.content}</p>

            {/* Inline Report */}
            {message.features?.includes('report') && message.reportContent && (
              <motion.div
                initial={false}
                animate={{ height: isExpanded ? 'auto' : '32px' }}
                style={{ overflow: 'hidden' }}
              >
                <button
                  type="button"
                  onClick={onToggleExpand}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.25rem',
                    fontWeight: '500',
                    color: '#032273',
                    fontSize: '0.875rem',
                    textDecoration: 'none',
                    cursor: 'pointer',
                    background: 'none',
                    border: 'none',
                  }}
                  onMouseEnter={(e) => { e.target.style.textDecoration = 'underline'; }}
                  onMouseLeave={(e) => { e.target.style.textDecoration = 'none'; }}
                >
                  {isExpanded ? 'レポートを非表示' : 'レポートを表示'}
                  <ChevronRight
                    style={{
                      height: '0.75rem',
                      width: '0.75rem',
                      transition: 'transform 150ms ease',
                      transform: isExpanded ? 'rotate(90deg)' : 'rotate(0deg)',
                    }}
                  />
                </button>

                {isExpanded && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    style={{ marginTop: '0.5rem', borderRadius: '0.375rem', border: '1px solid #e5e7eb', backgroundColor: '#f9fafb', padding: '0.75rem' }}
                  >
                    <h4 style={{ marginBottom: '0.5rem', fontWeight: '600', fontSize: '0.875rem' }}>
                      {message.reportContent.title}
                    </h4>
                    <ul style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem', listStyle: 'none', margin: 0, padding: 0 }}>
                      {message.reportContent.items.map(item => (
                        <li key={item} style={{ color: '#374151', fontSize: '0.75rem' }}>
                          {item}
                        </li>
                      ))}
                    </ul>
                  </motion.div>
                )}
              </motion.div>
            )}
          </div>
        </motion.div>
      );

    case 'thinking':
      return (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          style={{ display: 'flex', alignItems: 'flex-start', gap: '0.75rem' }}
        >
          <div style={{ display: 'flex', height: '1.75rem', width: '1.75rem', alignItems: 'center', justifyContent: 'center', fontSize: '0.875rem' }}>
            🤔
          </div>
          <p style={{ color: '#6b7280', fontSize: '0.875rem', fontStyle: 'italic' }}>{message.content}</p>
        </motion.div>
      );

    default:
      return null;
  }
};

Message.propTypes = {
  message: PropTypes.object.isRequired,
  isExpanded: PropTypes.bool.isRequired,
  onToggleExpand: PropTypes.func.isRequired,
};

export default Message;
