import { motion } from 'framer-motion';
import PropTypes from 'prop-types';
import Panda from './Panda';

function TypingIndicator({ type }) {
  const icon
    = type === 'thinking'
      ? (
        <div style={{ display: 'flex', height: '1.75rem', width: '1.75rem', alignItems: 'center', justifyContent: 'center', fontSize: '0.875rem' }}>🤔</div>
      )
      : type === 'takumi'
        ? (
          <Panda style={{ height: '1.75rem', width: '1.75rem', borderRadius: '0.375rem' }} />
        )
        : null;

  return (
    <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
      {icon}
      <div style={{ display: 'flex', gap: '0.25rem' }}>
        <motion.div
          style={{ height: '0.5rem', width: '0.5rem', borderRadius: '50%', backgroundColor: '#9ca3af' }}
          animate={{ scale: [1, 1.2, 1] }}
          transition={{
            repeat: Number.POSITIVE_INFINITY,
            duration: 1.4,
            delay: 0,
          }}
        />
        <motion.div
          style={{ height: '0.5rem', width: '0.5rem', borderRadius: '50%', backgroundColor: '#9ca3af' }}
          animate={{ scale: [1, 1.2, 1] }}
          transition={{
            repeat: Number.POSITIVE_INFINITY,
            duration: 1.4,
            delay: 0.2,
          }}
        />
        <motion.div
          style={{ height: '0.5rem', width: '0.5rem', borderRadius: '50%', backgroundColor: '#9ca3af' }}
          animate={{ scale: [1, 1.2, 1] }}
          transition={{
            repeat: Number.POSITIVE_INFINITY,
            duration: 1.4,
            delay: 0.4,
          }}
        />
      </div>
    </div>
  );
};

TypingIndicator.propTypes = { type: PropTypes.string.isRequired };

export default TypingIndicator;
