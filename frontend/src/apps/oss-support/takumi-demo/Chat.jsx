import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';
import Message from './Message';
import TypingIndicator from './TypingIndicator';

function Chat({ demo, onComplete, isPaused = false }) {
  const [visibleMessages, setVisibleMessages] = useState(0);
  const [expandedReports, setExpandedReports] = useState(new Set());
  const [typingMessage, setTypingMessage] = useState(null);
  const [demoCompleted, setDemoCompleted] = useState(false);

  useEffect(() => {
    const showNextMessage = () => {
      if (visibleMessages < demo.messages.length) {
        const currentMsg = demo.messages[visibleMessages];

        // Show typing indicator for Takumi messages
        if (currentMsg.type === 'takumi' || currentMsg.type === 'thinking') {
          setTypingMessage(visibleMessages);
          setTimeout(() => {
            setTypingMessage(null);
            setVisibleMessages(prev => prev + 1);
          }, currentMsg.duration || 1500);
        } else {
          setVisibleMessages(prev => prev + 1);
        }
      } else if (visibleMessages === demo.messages.length && !demoCompleted) {
        // Mark demo as completed
        setDemoCompleted(true);
      }
    };

    // Initial delay before starting
    const timer = setTimeout(
      showNextMessage,
      visibleMessages === 0 ? 500 : 800,
    );
    return () => clearTimeout(timer);
  }, [visibleMessages, demo.messages, demoCompleted]);

  // Handle completion separately - only respects pause state
  useEffect(() => {
    if (demoCompleted && onComplete) {
      const timer = setTimeout(() => {
        if (!isPaused) {
          onComplete();
        }
      }, 2000);
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [demoCompleted, isPaused, onComplete]);

  const toggleReport = (index) => {
    setExpandedReports((prev) => {
      const next = new Set(prev);
      if (next.has(index)) {
        next.delete(index);
      } else {
        next.add(index);
      }
      return next;
    });
  };

  return (
    <div style={{ height: '100%', overflowY: 'auto', borderRadius: '0.5rem', backgroundColor: 'white' }}>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem', padding: '1.5rem' }}>
        {demo.messages.slice(0, visibleMessages).map((message, index) => (
          <Message
            key={`${message.type}-${index}`}
            message={message}
            isExpanded={expandedReports.has(index)}
            onToggleExpand={() => toggleReport(index)}
          />
        ))}

        {/* Typing indicator */}
        {typingMessage !== null && (
          <TypingIndicator type={demo.messages[typingMessage].type} />
        )}
      </div>
    </div>
  );
};

Chat.propTypes = {
  demo: PropTypes.object.isRequired,
  onComplete: PropTypes.func.isRequired,
  isPaused: PropTypes.bool,
};

export default Chat;
