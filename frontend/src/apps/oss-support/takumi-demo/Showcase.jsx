import { AnimatePresence, motion } from 'framer-motion';
import { useState, useCallback } from 'react';
import Chat from './Chat';

const demos = [
  {
    title: '設計の診断',
    messages: [
      {
        type: 'user',
        content:
          '認証機能について、先週からの変更をセキュリティの観点で分析してください',
        timestamp: '9:42 AM',
      },
      {
        type: 'thinking',
        content: '認証機能の最新コミットを分析中...',
        duration: 2000,
      },
      {
        type: 'takumi',
        content: '認証機能で2つのセキュリティ上の懸念を特定しました:',
        features: ['report'],
        reportContent: {
          title: 'セキュリティ分析レポート',
          items: [
            '🔴 緊急: パスワードリセットトークンに有効期限がない',
            '🟡 中程度: ログインエンドポイントにレートリミットがない',
          ],
        },
      },
    ],
  },
  {
    title: '依存の診断',
    messages: [
      {
        type: 'user',
        content: '依存関係の既知の脆弱性をチェックしてください',
        timestamp: '9:45 AM',
      },
      {
        type: 'thinking',
        content: 'プロジェクトの依存関係の脆弱性をスキャン中...',
        duration: 3000,
      },
      {
        type: 'takumi',
        content: '依存関係で3つの脆弱性を発見しました:',
        features: ['report', 'action'],
        reportContent: {
          title: '依存関係の脆弱性',
          items: [
            '🔴 高: lodash < 4.17.21 (CVE-2021-23337)',
            '🟡 中程度: axios < 0.21.1 (CVE-2020-28168)',
            '🟡 低: minimist < 1.2.6 (CVE-2021-44906)',
          ],
        },
      },
    ],
  },
  {
    title: 'コードの詳細な診断',
    messages: [
      {
        type: 'user',
        content: 'コードベースにSQLインジェクションの脆弱性はありますか？',
        timestamp: '9:48 AM',
      },
      {
        type: 'thinking',
        content: 'SQLインジェクションのパターンをスキャン中...',
        duration: 2500,
      },
      {
        type: 'takumi',
        content:
          '良いお知らせです！SQLインジェクションの脆弱性は検出されませんでした。コードは正しくクエリパラメーターを使用しています。',
      },
    ],
  },
  {
    title: '差分の診断',
    messages: [
      {
        type: 'user',
        content:
          '最近のAPIエンドポイントの変更をセキュリティの観点でレビューしてください',
        timestamp: '9:51 AM',
      },
      {
        type: 'thinking',
        content: 'APIエンドポイントの変更をセキュリティの観点でレビュー中...',
        duration: 2000,
      },
      {
        type: 'takumi',
        content: 'APIの変更をレビューしました。発見事項は以下の通りです:',
        features: ['report'],
        reportContent: {
          title: 'APIセキュリティレビュー',
          items: [
            '✅ Good: すべてのエンドポイントで認証機能が実装されています',
            '⚠️ Issue: /api/users/:id でパスパラメーターの検証が不足しています',
            '💡 Suggestion: パスパラメーターとセッションの紐付けを実装してください',
          ],
        },
      },
    ],
  },
];

function Showcase() {
  const [activeDemo, setActiveDemo] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [demoKey, setDemoKey] = useState(0); // Force remount of demo chat

  // Simple approach: Let the child component drive the timing
  const handleDemoComplete = useCallback(() => {
    if (!isPaused) {
      setActiveDemo(prev => (prev + 1) % demos.length);
      setDemoKey(prev => prev + 1);
    }
  }, [isPaused]);

  const selectDemo = useCallback((index) => {
    setActiveDemo(index);
    setDemoKey(prev => prev + 1);
  }, []);

  return (
    <div
      // style={{ fontFamily: 'Noto Sans JP, sans-serif, sans' }}
      onMouseEnter={() => setIsPaused(true)}
      onMouseLeave={() => setIsPaused(false)}
    >
      <div style={{ width: '100%', maxWidth: '42rem' }}>
        {/* Breadcrumb Navigation */}
        <div style={{ marginBottom: '1rem', display: 'flex', justifyContent: 'center' }}>
          <nav style={{ display: 'flex', gap: '0.25rem' }}>
            {demos.map((demo, index) => (
              <button
                key={demo.title}
                type="button"
                onClick={() => selectDemo(index)}
                style={{
                  borderRadius: '9999px',
                  paddingLeft: '0.75rem',
                  paddingRight: '0.75rem',
                  paddingTop: '0.25rem',
                  paddingBottom: '0.25rem',
                  fontSize: '0.875rem',
                  transition: 'all 150ms ease',
                  backgroundColor: activeDemo === index ? '#032273' : '#ffffff',
                  color: activeDemo === index ? 'white' : '#4b5563',
                  cursor: 'pointer',
                  border: 'none',
                }}
                onMouseEnter={(e) => {
                  if (activeDemo !== index) {
                    e.target.style.backgroundColor = '#d1d5db';
                  }
                }}
                onMouseLeave={(e) => {
                  if (activeDemo !== index) {
                    e.target.style.backgroundColor = '#ffffff';
                  }
                }}
              >
                {demo.title}
              </button>
            ))}
          </nav>
        </div>

        {/* Demo Content Area */}
        <div
          style={{
            position: 'relative',
            height: '450px',
            overflow: 'hidden',
            filter: 'drop-shadow(0 4px 20px rgba(0, 0, 0, 0.1))',
            willChange: 'transform',
          }}
        >
          <AnimatePresence mode="wait">
            <motion.div
              key={demoKey}
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -50 }}
              transition={{ duration: 0.3 }}
              style={{ height: '100%' }}
            >
              <Chat
                demo={demos[activeDemo]}
                onComplete={handleDemoComplete}
                isPaused={isPaused}
              />
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Pause indicator - always reserve space */}
        <p style={{ marginTop: '1rem', height: '1rem', textAlign: 'center', color: '#6b7280', fontSize: '0.75rem' }}>
          {isPaused && '自動進行を一時停止しています'}
        </p>
      </div>
    </div>
  );
};

export default Showcase;
