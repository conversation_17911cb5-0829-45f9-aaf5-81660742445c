import { useState, useEffect } from 'react';
import { useTranslation, Trans } from 'react-i18next';
import ContactButton from '../../common/components/ContactButton';
import ErrorComponent from '../../common/components/Error';
import { useErrorMessages } from '../../common/messages/error';
import ChatButton from '../chat/ChatButton';
import ExpiredResult from './ExpiredResult';
import PasswordContactSection from './PasswordContactSection';
import Result from './Result';

function Password() {
  const { i18n } = useTranslation('check_password');
  const errorMessages = useErrorMessages();
  const [isLoading, setIsLoading] = useState(true);
  const [result, setResult] = useState(null);
  const [error, setError] = useState('');
  const [isCompleted, setIsCompleted] = useState(false);

  const queryParams = new URLSearchParams(location.search);
  const code = queryParams.get('code');

  const isExpired = !result?.details;

  useEffect(() => {
    if (code) {
      setIsLoading(true);

      (async () => {
        try {
          const response = await fetch(
            `${import.meta.env.VITE_API_HOST}/api/password?code=${code}`,
          );

          if (response.ok) {
            const { result, status } = await response.json();
            if (status === 'success') {
              setResult(result);
            }
          } else {
            const { status, message } = await response.json();
            if (status === 'error') {
              throw new Error(message);
            }
          }
        } catch (err) {
          if (err.message === 'Code is expired') {
            setError(errorMessages.CODE_EXPIRED);
            return;
          }
          if (err.message === 'Invalid code') {
            setError(errorMessages.INVALID_CODE);
            return;
          }
          console.error(err);
          setError(errorMessages.NOT_FOUND);
        } finally {
          setIsLoading(false);
        }
      })();
    }
  }, [code]);

  if (isLoading) {
    return <></>;
  }

  if (error) {
    return <ErrorComponent text={error} />;
  }

  return (
    <section>
      <div className="p-base">
        <div className="p-base__inner">
          <div className="p-base__message">
            <div className="c-glassPanel">
              <p className="c-glassPanel__text">
                <Trans
                  ns="check_password"
                  i18nKey="パスワード漏洩診断日：{{date}}"
                  values={{ date: new Date(result.summary.createdAt).toLocaleDateString(i18n.language === 'en' ? 'en-US' : 'ja-JP') }}
                />
              </p>
            </div>
          </div>
          <div className="p-base__content">
            {isExpired
              ? (
                <ExpiredResult
                  {...result}
                  code={code}
                  onClickedContact={() => setIsCompleted(false)}
                />
              )
              : (
                <Result
                  {...result}
                  code={code}
                  onClickedContact={() => setIsCompleted(false)}
                />
              )}
          </div>
          {result.summary?.isCompany && (
            <div className="p-base__contact">
              <PasswordContactSection
                code={code}
                email={result.summary?.email}
                isCompleted={isCompleted}
                setIsCompleted={setIsCompleted}
              />
            </div>
          )}
        </div>
      </div>
      {result.summary?.isCompany && (
        <div className="p-baseFixed">
          <div className="p-baseFixed__contactButton">
            <ContactButton
              isPasswordSite={true}
              onClick={() => {
                setIsCompleted(false);
              }}
            />
          </div>
          <div className="p-baseFixed__chatButton">
            <ChatButton />
          </div>
        </div>
      )}
    </section>
  );
}

export default Password;
