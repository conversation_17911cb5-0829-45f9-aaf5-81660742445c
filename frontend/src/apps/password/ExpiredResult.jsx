import PropTypes from 'prop-types';
import { Trans, useTranslation } from 'react-i18next';
import resultImageAlert from '../../assets/img/rank_alert.png';
import resultImageSafe from '../../assets/img/rank_safe.png';
import Button from '../../common/components/Button';
import { openWithLanguage } from '../../common/utils/navigationUtils';
import PasswordPeriodicCheckup from './PasswordPeriodicCheckup';

const RESULT_IMAGE_ALERT = resultImageAlert;
const RESULT_IMAGE_SAFE = resultImageSafe;

function ExpiredResult({ code, summary, count, configuration }) {
  const { t } = useTranslation('check_password');

  const handleNewDiagnostic = () => {
    openWithLanguage('/security/');
  };

  const handleReDiagnostic = () => {
    localStorage.setItem(
      'state',
      JSON.stringify({ diagnosticValue: summary.email }),
    );
    openWithLanguage('/security/');
  };
  return (
    <div className="c-panel">
      <div className="c-panel__content c-panel__content--gap20">
        <div className="c-panel__side">
          {count > 0
            ? (
              <div className="c-panel__rankImg">
                <img src={RESULT_IMAGE_ALERT} alt="alert" />
              </div>
            )
            : (
              <div className="c-panel__rankImg">
                <img src={RESULT_IMAGE_SAFE} alt="safe" />
              </div>
            )}
        </div>
        <div className="c-panel__main">
          {count > 0
            ? (
              <>
                <p className="c-panel__note c-panel__note--alert">{t('要注意')}</p>
                <p className="c-panel__result">
                  {t('診断したメールアドレスで利用中のサービスにおいてパスワードが漏洩している可能性があります。')}
                </p>
              </>
            )
            : (
              <>
                <p className="c-panel__note">{t('安全です')}</p>
                <p className="c-panel__result">
                  {t('診断したメールアドレスで利用中のサービスにおいてパスワード漏洩はありません。')}
                </p>
              </>
            )}
          <div className="p-passwordResult">
            {/* spのみ表示 */}
            {count > 0 && (
              <div className="p-passwordResult__note p-passwordResult__note--pcNone">
                <dl className="p-passwordResultNote">
                  <dt>{t('対応方法')}</dt>
                  <dd>
                    <ul>
                      <li>
                        {t('・該当サービスのパスワードを変更し、他サービスでも同じパスワードをご利用の場合はすべて変更してください')}
                      </li>
                      <li>{t('・パスワード設定は最大文字数を推奨します')}</li>
                      <li>
                        {t('・二要素認証、二段階認証可能なサービスは設定を推奨します')}
                      </li>
                      <li>
                        {t('・ワンタイムパスワード、IP制限が可能なサービスは設定を推奨します')}
                      </li>
                    </ul>
                  </dd>
                </dl>
                <dl className="p-passwordResultNote p-passwordResultNote--normal">
                  <dt>{t('企業でご利用のメールアドレス')}</dt>
                  <dd>
                    {t('社員のパスワード漏洩がご心配な情報システム担当者様や経営者の皆様は、「専門家へ無償相談」のボタンよりお気軽にご相談ください。')}
                  </dd>
                </dl>
              </div>
            )}
            {/* spのみ表示 end */}
            <div className="p-passwordResult__email">
              <div className="c-panelBox">
                {summary.email && (
                  <dl className="c-panelBox__item">
                    <dt>{t('診断したメールアドレス')}</dt>
                    <dd>{summary.email}</dd>
                  </dl>
                )}
                <div className="c-panelBox__button">
                  <Button
                    id="another_email"
                    onClick={() => handleNewDiagnostic()}
                    external
                    exIcon="small"
                    variant="secondary"
                  >
                    {t('他のメールアドレスを診断')}
                  </Button>
                </div>
              </div>
            </div>
            <div className="p-passwordResult__summary">
              <div
                className={
                  count > 0
                    ? 'p-passwordResult__count p-passwordResult__count--error'
                    : 'p-passwordResult__count p-passwordResult__count--safe'
                }
              >
                {count}
              </div>
              {count > 0
                ? (
                  <p>{t('パスワードが漏洩した可能性のあるサイト')}</p>
                )
                : (
                  <p>{t('情報漏洩は検出されませんでした')}</p>
                )}
            </div>
            <div className="p-passwordResult__detail">
              {/* pcのみ表示 */}
              {count > 0 && (
                <div className="p-passwordResult__note p-passwordResult__note--spNone">
                  <dl className="p-passwordResultNote">
                    <dt>{t('対応方法')}</dt>
                    <dd>
                      <ul>
                        <li>
                          {t('・該当サービスのパスワードを変更し、他サービスでも同じパスワードをご利用の場合はすべて変更してください')}
                        </li>
                        <li>{t('・パスワード設定は最大文字数を推奨します')}</li>
                        <li>
                          {t('・二要素認証、二段階認証可能なサービスは設定を推奨します')}
                        </li>
                        <li>
                          {t('・ワンタイムパスワード、IP制限が可能なサービスは設定を推奨します')}
                        </li>
                      </ul>
                    </dd>
                  </dl>
                  <dl className="p-passwordResultNote p-passwordResultNote--normal">
                    <dt>{t('企業でご利用のメールアドレス')}</dt>
                    <dd>
                      {t('社員のパスワード漏洩がご心配な情報システム担当者様や経営者の皆様は、「専門家へ無償相談」のボタンよりお気軽にご相談ください。')}
                    </dd>
                  </dl>
                </div>
              )}
              {/* pcのみ表示 end */}
              <PasswordPeriodicCheckup
                code={code}
                nextCheckedAt={configuration?.nextCheckedAt}
                isRegularly={configuration?.isRegularly}
                interval={configuration?.interval}
                isNotification={configuration?.isNotification}
                createdAt={summary.createdAt}
              />
              <div className="p-passwordResult__annotation">
                <Trans
                  ns="check_password"
                  i18nKey="※各項目の詳細を確認できる期限が過ぎています。<sp>ご確認いただくには<btn>再度認診</btn>を行ってください。</sp>"
                  components={{
                    sp: <span />,
                    btn: <Button variant="textXs" onClick={() => handleReDiagnostic()} />,
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

ExpiredResult.propTypes = {
  code: PropTypes.string.isRequired,
  summary: PropTypes.shape({
    email: PropTypes.string,
    createdAt: PropTypes.string,
  }).isRequired,
  count: PropTypes.number.isRequired,
  configuration: PropTypes.shape({
    nextCheckedAt: PropTypes.string,
    isRegularly: PropTypes.bool,
    interval: PropTypes.number,
    isNotification: PropTypes.bool,
  }),
};

export default ExpiredResult;
