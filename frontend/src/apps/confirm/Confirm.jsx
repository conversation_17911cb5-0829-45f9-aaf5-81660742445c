import { useEffect, useState } from 'react';
import { useTranslation, Trans } from 'react-i18next';
import mailImage from '../../assets/img/illust_mail.png';
import ErrorComponent from '../../common/components/Error';
import { useErrorMessages } from '../../common/messages/error';
import { normalizeLanguage } from '../../common/utils/languageUtils';
import { navigateWithLanguage } from '../../common/utils/navigationUtils';

const MAIL_IMAGE = mailImage;

function Confirm() {
  const { t, i18n } = useTranslation('check_confirm');
  const errorMessages = useErrorMessages();
  const [storage, setStorage] = useState({ email: null });
  const [grecaptcha, setGrecaptcha] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  const queryParams = new URLSearchParams(location.search);
  const code = queryParams.get('code');

  useEffect(() => {
    const { grecaptcha } = window;
    if (grecaptcha) {
      grecaptcha.ready(() => {
        setGrecaptcha(grecaptcha);
      });
    }
  }, []);

  useEffect(() => {
    if (code) {
      if (!grecaptcha) {
        return;
      }
      (async () => {
        try {
          const recaptchaToken = await grecaptcha.execute(
            import.meta.env.VITE_RECAPTCHA_SITE_KEY,
            { action: 'submit' },
          );

          const supportedLang = normalizeLanguage(i18n.language);

          const response = await fetch(
            `${import.meta.env.VITE_API_HOST}/api/confirm`,
            {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ code, recaptchaToken, lang: supportedLang }),
            },
          );

          if (response.ok) {
            const { result, status } = await response.json();
            if (status === 'success') {
              localStorage.setItem(
                'state',
                JSON.stringify({ email: result.email, fqdn: result.fqdn }),
              );
              navigateWithLanguage('/security/check/complete/');
            }
          } else {
            const { status, message } = await response.json();
            if (status === 'error') {
              throw new Error(message);
            }
          }
        } catch (err) {
          if (err.message === 'Code is expired') {
            setError(errorMessages.CODE_EXPIRED);
          } else if (err.message === 'Invalid code') {
            setError(errorMessages.INVALID_CODE);
          } else {
            console.error(err);
            setError(errorMessages.NOT_FOUND);
          }

          setIsLoading(false);
        }
      })();
    } else {
      if (!storage.email) {
        try {
          const state = localStorage.getItem('state');
          if (!state) {
            throw new Error('State not found');
          }
          const parsedState = JSON.parse(state);
          const { email, fqdn } = parsedState;
          if (!email) {
            throw new Error('Email not found');
          }
          setStorage({ email, fqdn });
          localStorage.removeItem('state');
        } catch (err) {
          console.error(err);
          window.history.back();
        }
        setIsLoading(false);
      }
    }
  }, [grecaptcha, code, storage.email]);

  if (isLoading) {
    return <></>;
  }

  if (error) {
    return <ErrorComponent text={error} />;
  }

  return (
    <section>
      <div className="p-base">
        <div className="p-base__inner">
          <h1 className="c-title c-title--pdb20">
            <Trans
              ns="check_confirm"
              i18nKey="メールアドレスの<sp>確認を行います</sp>"
              components={{ sp: <span /> }}
            />
          </h1>
          <p className="p-base__text">
            {t('メール内のリンクからメールアドレスの確認をしてください。')}
          </p>
          <div className="c-panel c-panel--s">
            <div className="c-panel__img">
              <img src={MAIL_IMAGE} alt="" />
            </div>
            <p className="c-panel__text">
              <Trans
                ns="check_confirm"
                i18nKey="ご入力いただいたメールアドレス<sp>{{email}}</sp>に確認メールを送信しました。"
                values={{ email: storage.email }}
                components={{ sp: <span className="c-panel__emailAddress" /> }}
              />
            </p>

            <div className="c-panel__annotation c-panel__annotation--center">
              {t('※メールが届かない場合は、迷惑メールフォルダをご確認いただくか、もう一度入力してください。')}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default Confirm;
