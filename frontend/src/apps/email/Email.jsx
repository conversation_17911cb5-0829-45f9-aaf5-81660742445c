import { useEffect, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import Button from '../../common/components/Button';
import { normalizeLanguage } from '../../common/utils/languageUtils';
import { navigateWithLanguage, openWithLanguage } from '../../common/utils/navigationUtils';

function Email() {
  const { t, i18n } = useTranslation('check_email');
  const [fqdn, setFqdn] = useState(null);
  const [grecaptcha, setGrecaptcha] = useState(null);
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    const { grecaptcha } = window;
    if (grecaptcha) {
      grecaptcha.ready(() => {
        setGrecaptcha(grecaptcha);
      });
    }
  }, []);

  useEffect(() => {
    if (!fqdn) {
      try {
        const state = localStorage.getItem('state');
        if (!state) {
          throw new Error('State not found');
        }
        const f = JSON.parse(state).fqdn;
        if (!f) {
          throw new Error('Fqdn not found');
        }
        setFqdn(f);
        localStorage.removeItem('state');
      } catch (err) {
        console.error(err);
        window.history.back();
      }
    }
  }, [fqdn]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      if (!grecaptcha) {
        throw new Error('Not fount grecaptcha');
      }
      const recaptchaToken = await grecaptcha.execute(
        import.meta.env.VITE_RECAPTCHA_SITE_KEY,
        { action: 'submit' },
      );

      const supportedLang = normalizeLanguage(i18n.language);

      const response = await fetch(
        `${import.meta.env.VITE_API_HOST}/api/fqdn`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email, fqdn, recaptchaToken, lang: supportedLang }),
        },
      );

      if (response.ok) {
        const { result, status } = await response.json();
        if (status === 'success') {
          localStorage.setItem('state', JSON.stringify({ email, fqdn }));
          navigateWithLanguage('/security/check/complete/');
        } else if (status === 'request') {
          localStorage.setItem(
            'state',
            JSON.stringify({ email, fqdn, authTxt: result.authTxt }),
          );
          navigateWithLanguage('/security/check/verify/');
        } else if (status === 'confirm') {
          localStorage.setItem('state', JSON.stringify({ email, fqdn }));
          navigateWithLanguage('/security/check/confirm/');
        }
      } else {
        const { status, message } = await response.json();
        if (status === 'error') {
          throw new Error(`Fqdn api error message: ${message}`);
        }
      }
    } catch (err) {
      console.error(err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleNewDiagnostic = () => {
    openWithLanguage('/security/');
  };

  if (!fqdn) {
    return <></>;
  }

  return (
    <section>
      <div className="p-base">
        <div className="p-base__inner">
          <h1 className="c-title c-title--pdb20">
            <Trans
              ns="check_email"
              i18nKey="セキュリティ診断を<sp>開始します</sp>"
              components={{ sp: <span /> }}
            />
          </h1>
          <p className="p-base__text">
            <Trans ns="check_email" i18nKey="診断には時間がかかる場合があります。<br />結果が準備できましたら、メールでお知らせいたします。" components={{ br: <br /> }} />
          </p>
          <div className="c-panel">
            <div className="c-panel__head">
              <div className="c-panelBox">
                <dl className="c-panelBox__item">
                  <dt>{t('診断対象のURL')}</dt>
                  <dd>{fqdn}</dd>
                </dl>
                <div className="c-panelBox__button">
                  <Button
                    id="another_url"
                    onClick={handleNewDiagnostic}
                    external
                    exIcon="small"
                    variant="secondary"
                  >
                    {t('他のURLを診断')}
                  </Button>
                </div>
              </div>
            </div>
            <div className="c-panel__body">
              <form onSubmit={handleSubmit} className="c-inputForm">
                <div className="c-inputForm__box">
                  <label htmlFor="email" className="c-input__label">
                    {t('メールアドレス')}
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={email}
                    onChange={e => setEmail(e.target.value)}
                    className="c-input__input"
                    placeholder={t('メールアドレスを入力してください')}
                    required
                  />
                </div>
                <div className="c-inputForm__button">
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    variant="primary"
                    widthSize="full"
                  >
                    {isSubmitting
                      ? (
                        <div className="c-loader c-loader--button" />
                      )
                      : null}
                    {t('次へ進む')}
                  </Button>
                </div>
                <p className="c-inputForm__note">
                  {t('※入力いただいたメールアドレスは、診断結果通知および確認の目的以外には使用しません。')}
                </p>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default Email;
