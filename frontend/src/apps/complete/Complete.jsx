import { useEffect, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import receivedImage from '../../assets/img/illust_received.png';
import EmailSection from '../../common/components/EmailSection';
import FqdnSection from '../../common/components/FqdnSection';
import {
  getFqdn,
  isValidEmail,
  isValidFqdn,
  isValidUrl,
} from '../../common/utils';
import { normalizeLanguage } from '../../common/utils/languageUtils';
import { navigateWithLanguage } from '../../common/utils/navigationUtils';

const RECEIVED_IMAGE = receivedImage;

function Complete() {
  const { t, i18n } = useTranslation('check_complete');
  const [storage, setStorage] = useState({
    email: null,
    fqdn: null,
  });
  const [grecaptcha, setGrecaptcha] = useState(null);

  const { email, fqdn } = storage;

  useEffect(() => {
    const { grecaptcha } = window;
    if (grecaptcha) {
      grecaptcha.ready(() => {
        setGrecaptcha(grecaptcha);
      });
    }
  }, []);

  useEffect(() => {
    if (!email) {
      try {
        const state = localStorage.getItem('state');
        if (!state) {
          throw new Error('State not found');
        }
        const parsedState = JSON.parse(state);
        const { email: emailFromStorage, fqdn: fqdnFromStorage } = parsedState;
        if (!emailFromStorage) {
          throw new Error('Email not found');
        }
        setStorage({ email: emailFromStorage, fqdn: fqdnFromStorage });
        localStorage.removeItem('state');
      } catch (err) {
        console.error(err);
        window.history.back();
      }
    }
  }, [email]);

  if (!email) {
    return <></>;
  }

  const handleFqdnSubmit = (value) => {
    if (isValidFqdn(value) || isValidUrl(value)) {
      localStorage.setItem('state', JSON.stringify({ fqdn: getFqdn(value) }));
      navigateWithLanguage('/security/check/email/');
    } else {
      // TODO: validation error
    }
  };

  const handleEmailSubmit = async (value) => {
    try {
      if (!grecaptcha) {
        throw new Error('Not fount grecaptcha');
      }
      const recaptchaToken = await grecaptcha.execute(
        import.meta.env.VITE_RECAPTCHA_SITE_KEY,
        { action: 'submit' },
      );
      if (isValidEmail(value)) {
        const supportedLang = normalizeLanguage(i18n.language);

        const response = await fetch(
          `${import.meta.env.VITE_API_HOST}/api/email`,
          {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email: value, recaptchaToken, lang: supportedLang }),
          },
        );

        if (response.ok) {
          localStorage.setItem('state', JSON.stringify({ email: value }));
          navigateWithLanguage('/security/check/complete/');
        } else {
          const { status, message } = await response.json();
          if (status === 'error') {
            throw new Error(`Email api error message: ${message}`);
          }
        }
      } else if (isValidFqdn(value) || isValidUrl(value)) {
        localStorage.setItem('state', JSON.stringify({ fqdn: getFqdn(value) }));
        navigateWithLanguage('/security/check/email/');
      } else {
        // TODO: validation error 表示
      }
    } catch (err) {
      console.error(err);
    }
  };

  return (
    <section>
      <div className="p-base">
        <div className="p-base__inner">
          <h1 className="c-title c-title--pdb20">
            {fqdn
              ? t('サイトリスク診断の受付完了')
              : t('パスワード漏洩診断の受付完了')}
          </h1>
          <p className="p-base__text">
            {fqdn
              ? t('診断結果のお届けには約30分ほどお時間をいただきます。')
              : t('メール内のリンクから診断結果をご確認いただけます。')}
          </p>
          <div className="c-panel c-panel--s">
            <div className="c-panel__img">
              <img src={RECEIVED_IMAGE} alt="" />
            </div>
            <p className="c-panel__text">
              <Trans
                ns="check_complete"
                i18nKey="診断が完了しましたら、ご入力いただいたメールアドレス<sp>{{email}}</sp>に診断結果報告メールを送信します。"
                values={{ email }}
                components={{ sp: <span className="c-panel__emailAddress" /> }}
              />
            </p>

            <div className="c-panel__annotation c-panel__annotation--center">
              {t('※メールが届かない場合は、迷惑メールフォルダをご確認いただくか、もう一度入力してください。')}
            </div>
          </div>
        </div>
        <div className="p-base__sub">
          {fqdn
            ? (
              <EmailSection onSubmit={handleEmailSubmit} />
            )
            : (
              <FqdnSection onSubmit={handleFqdnSubmit} />
            )}
        </div>
      </div>
    </section>
  );
}

export default Complete;
