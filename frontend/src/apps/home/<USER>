import PropTypes from 'prop-types';
import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

function SearchInput({
  placeholder,
  initValue = '',
  onChange = () => { },
}) {
  const { t } = useTranslation('home');
  const resolvedPlaceholder = placeholder ?? t('メールアドレス or URLを入力');
  const [value, setValue] = useState(initValue);

  useEffect(() => {
    setValue(initValue);
  }, [initValue]);

  const handleChangeValue = (value) => {
    setValue(value);
    onChange && onChange(value);
  };

  return (
    <div className="c-inputSearchHome">
      <input
        type="text"
        value={value}
        onChange={e => handleChangeValue(e.target.value)}
        placeholder={resolvedPlaceholder}
        className="c-inputSearchHome__input"
        form="securityTopForm"
      />
    </div>
  );
}

SearchInput.propTypes = {
  placeholder: PropTypes.string,
  initValue: PropTypes.string,
  onChange: PropTypes.func,
};

export default SearchInput;
