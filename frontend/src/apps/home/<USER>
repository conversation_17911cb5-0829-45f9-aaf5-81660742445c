import PropTypes from 'prop-types';
import logoImage from '../../assets/img/logo-v2.svg';
import logoImageEn from '../../assets/img/logo_en.svg';

const LOGO_IMAGES = {
  ja: logoImage,
  en: logoImageEn,
};

function Header({ title, description, lang = 'ja' }) {
  return (
    <div className="p-homeHeader">
      <h1>
        <img src={LOGO_IMAGES[lang]} alt={title} />
      </h1>
      {description && <p className="text-gray-600">{description}</p>}
    </div>
  );
}

Header.propTypes = {
  title: PropTypes.string.isRequired,
  description: PropTypes.string,
  lang: PropTypes.string,
};

export default Header;
