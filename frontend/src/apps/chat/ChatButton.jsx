import {
  Chat<PERSON><PERSON><PERSON>,
  MainContainer,
  Message,
  MessageInput,
  MessageList,
} from '@chatscope/chat-ui-kit-react';
import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import Button from '../../common/components/Button';
import useChatHandler from '../../common/hooks/useChatHandler';
import { normalizeLanguage } from '../../common/utils/languageUtils';
import { navigateWithLanguage } from '../../common/utils/navigationUtils';
import { ChatMessageBubbleMarkDown } from './ChatMessageBubbleMarkDown';

export const ChatButton = ({
  name,
  chatBoxtitle,
  chatBoxDescription,
  systemMessage,
  isShowMaximize = true,
}) => {
  const { t, i18n } = useTranslation('chat');
  const resolvedName = name ?? t('その他のセキュリティ相談');
  const resolvedChatBoxtitle = chatBoxtitle ?? t('その他のセキュリティ相談');
  const resolvedChatBoxDescription = chatBoxDescription ?? t('貴方がセキュリティで懸念されている点を入力してください。AIによる24時間自動応答でお答えいたします。');
  const resolvedSystemMessage = systemMessage ?? t('セキュリティで懸念されている点について、お気軽にお尋ねください。');
  const [isOpen, setIsOpen] = useState(false);

  const [messages, setMessages] = useState([
    {
      role: 'system',
      content: resolvedSystemMessage,
    },
  ]);
  const [threadTs, setThreadTs] = useState(null);
  const [grecaptcha, setGrecaptcha] = useState(null);
  const [isSending, setIsSending] = useState(false);
  const [inputText, setInputText] = useState('');

  const location = useLocation();
  const {
    isChatValidAndAllowed,
    incrementChatCounter,
    clearChatErrors,
    chatError,
  } = useChatHandler();

  useEffect(() => {
    const { grecaptcha } = window;
    if (grecaptcha) {
      grecaptcha.ready(() => {
        setGrecaptcha(grecaptcha);
      });
    }
  }, []);

  const handleInputChange = (_, textContent) => {
    clearChatErrors();
    setInputText(textContent);
  };

  const handleSend = async (_, post) => {
    if (!isChatValidAndAllowed(post)) {
      return;
    }

    const oldMessages = messages;
    setMessages([...oldMessages, { role: 'user', content: post }]);
    setInputText('');
    setIsSending(true);
    incrementChatCounter();
    try {
      if (!grecaptcha) {
        throw new Error('Not fount grecaptcha');
      }
      const recaptchaToken = await grecaptcha.execute(
        import.meta.env.VITE_RECAPTCHA_SITE_KEY,
        { action: 'submit' },
      );

      const supportedLang = normalizeLanguage(i18n.language);

      const response = await fetch(
        `${import.meta.env.VITE_API_HOST}/api/chat`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            post,
            messages: oldMessages
              .filter(({ role }) => ['user', 'assistant'].includes(role))
              .map(m => ({ role: m.role, content: m.content })),
            threadTs,
            recaptchaToken,
            path: location.pathname,
            lang: supportedLang,
          }),
        },
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder('utf-8');
      let buffer = '';
      let hasNewLineOccurred = false;

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        buffer += decoder.decode(value, { stream: true });
        if (!hasNewLineOccurred && buffer.includes('\n')) {
          const [messageBeforeNewLine, ...rest] = buffer.split('\n');
          setThreadTs(messageBeforeNewLine);
          buffer = rest.join('\n');
          hasNewLineOccurred = true;
        }
        if (hasNewLineOccurred && buffer.length > 0) {
          setMessages([
            ...oldMessages,
            { role: 'user', content: post },
            { role: 'assistant', content: buffer },
          ]);
          setIsSending(false);
        }
      }
      setMessages([
        ...oldMessages,
        { role: 'user', content: post },
        {
          role: 'assistant',
          content: buffer,
          feedback: null,
          replyNumber: oldMessages.filter(({ role }) => role === 'assistant')
            .length,
        },
      ]);
      reader.releaseLock();
    } catch (err) {
      console.error(err);
    } finally {
      setIsSending(false);
    }
  };

  const handleFeedback = async (replyNumber, feedback) => {
    try {
      let newFeedback = feedback;
      const newMessages = messages.map((m) => {
        if (m.role !== 'assistant') return m;
        if (m.replyNumber !== replyNumber) return m;

        if (m.feedback === feedback) {
          newFeedback = null;
        }
        m.feedback = newFeedback;
        return m;
      });
      if (newFeedback === 'bad') {
        newMessages.push({
          role: 'system',
          content: t('ご期待にお応えできず申し訳ございません。よろしければさらに詳しくお聞かせいただけませんでしょうか。'),
        });
      }
      setMessages(newMessages);

      if (!threadTs) return;
      if (!grecaptcha) {
        throw new Error('Not fount grecaptcha');
      }
      const recaptchaToken = await grecaptcha.execute(
        import.meta.env.VITE_RECAPTCHA_SITE_KEY,
        { action: 'submit' },
      );
      const response = await fetch(
        `${import.meta.env.VITE_API_HOST}/api/feedback`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            threadTs,
            replyNumber,
            feedback: newFeedback,
            recaptchaToken,
          }),
        },
      );
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (err) {
      console.error(err);
    }
  };

  const handleMaximized = () => {
    const currentState = JSON.parse(localStorage.getItem('state'));
    localStorage.setItem(
      'state',
      JSON.stringify({ ...currentState, messages, threadTs }),
    );
    navigateWithLanguage('/security/check/chat/');
  };

  return (
    <div className="p-baseChat">
      {isOpen
        ? (
          <div className="p-baseChat__board">
            <div className="p-chatPanelSmall__head">
              <h1 className="p-chatPanelSmall__title">
                <span className="icon-base icon-sec-chat icon-size36 icon-color-darkGreen " />
                {resolvedChatBoxtitle}
              </h1>
              <p className="p-chatPanelSmall__text">
                {resolvedChatBoxDescription}
              </p>
              <div className="p-chatPanelSmall__action">
                {isShowMaximize && (
                  <Button onClick={() => handleMaximized()} variant="text">
                    <span className="icon-base icon-sec-expansion icon-size20 icon-color-darkGreen" />
                  </Button>
                )}
                <Button onClick={() => setIsOpen(false)} variant="text">
                  <span className="icon-base icon-sec-close icon-size20 icon-color-darkGreen" />
                </Button>
              </div>
            </div>
            <div className="p-chatPanelSmall__body">
              <MainContainer>
                <ChatContainer>
                  <MessageList>
                    {messages.map((m, idx) => (
                      <div
                        key={idx}
                        className={
                          m.role === 'user'
                            ? 'p-chatMs p-chatMs--right'
                            : 'p-chatMs'
                        }
                      >
                        {m.role !== 'user' && (
                          <span className="icon-base icon-sec-operator icon-size36 icon-color-darkGreen" />
                        )}
                        <div className="p-chatMs__message">
                          <Message
                            model={{
                              direction:
                                m.role === 'user' ? 'outgoing' : 'incoming',
                            }}
                          >
                            <Message.CustomContent>
                              <ChatMessageBubbleMarkDown content={m.content} />
                            </Message.CustomContent>
                          </Message>
                          {m.role !== 'user' && (
                            <>
                              {m.feedback !== undefined
                                && m.replyNumber !== undefined && (
                                <div className="p-chatMs__feedback">
                                  <Button
                                    variant="text"
                                    className={m.feedback}
                                    onClick={() =>
                                      handleFeedback(m.replyNumber, 'good')}
                                  >
                                    <span
                                      className={
                                        m.feedback === 'good'
                                          ? 'icon-base icon-sec-good-on icon-size16 icon-color-darkGreen'
                                          : 'icon-base icon-sec-good icon-size16 icon-color-darkGreen'
                                      }
                                    />
                                  </Button>
                                  <Button
                                    variant="text"
                                    className={m.feedback}
                                    onClick={() =>
                                      handleFeedback(m.replyNumber, 'bad')}
                                  >
                                    <span
                                      className={
                                        m.feedback === 'bad'
                                          ? 'icon-base icon-sec-good-on icon-size16 icon-color-darkGreen icon-rotate180'
                                          : 'icon-base icon-sec-good icon-size16 icon-color-darkGreen icon-rotate180'
                                      }
                                    />
                                  </Button>
                                </div>
                              )}
                            </>
                          )}
                        </div>
                      </div>
                    ))}
                    {/* 待機中のローダー */}
                    {isSending && (
                      <div className="p-chatPanelSmall__loading">
                        <div className="c-loaderChat">
                          <span />
                          <span />
                          <span />
                        </div>
                      </div>
                    )}
                  </MessageList>
                  <MessageInput
                    placeholder={t('メッセージを入力してください')}
                    onSend={handleSend}
                    onChange={handleInputChange}
                    value={inputText}
                  />
                </ChatContainer>
              </MainContainer>
              <div className="p-chatPanelSmall__footer">
                {chatError && (
                  <p className="p-chatPanelSmall__error">
                    <span className="icon-base icon-sec-caution icon-color-error" />
                    {chatError}
                  </p>
                )}
                <p className="p-chatPanelSmall__note">
                  {t('回答は全てAIによる自動生成です')}
                </p>
              </div>
            </div>
          </div>
        )
        : (
          <div className="p-baseChat__button">
            <Button
              id="chat_common"
              onClick={() => setIsOpen(true)}
              variant="chat"
            >
              <span className="icon-base icon-sec-chat icon-size36 icon-color-darkGreen" />
              {resolvedName}
            </Button>
          </div>
        )}
    </div>
  );
};
ChatButton.propTypes = {
  name: PropTypes.string,
  chatBoxtitle: PropTypes.string,
  chatBoxDescription: PropTypes.string,
  systemMessage: PropTypes.string,
  isShowMaximize: PropTypes.bool,
};

export default ChatButton;
