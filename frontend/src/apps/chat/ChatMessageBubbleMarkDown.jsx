import PropTypes from 'prop-types';
import Markdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

const renderers = {
  code: ({ children }) => {
    return <>{children}</>;
  },
  pre: ({ children, className }) => {
    if (
      children
      && typeof children === 'object'
      && children.type
      && children.type.name === 'code'
    ) {
      return <pre className={className}>{children}</pre>;
    }

    return <pre className={className}>{children}</pre>;
  },
  table: ({ children, ...props }) => <table {...props}>{children}</table>,
  thead: ({ children, ...props }) => <thead {...props}>{children}</thead>,
  tbody: ({ children, ...props }) => <tbody {...props}>{children}</tbody>,
  tr: ({ children, ...props }) => <tr {...props}>{children}</tr>,
  td: ({ children, ...props }) => <td {...props}>{children}</td>,
  th: ({ children, ...props }) => <th {...props}>{children}</th>,
  ul: ({ children, ...props }) => <ul {...props}>{children}</ul>,
  ol: ({ children, ...props }) => <ol {...props}>{children}</ol>,
  li: ({ children, ...props }) => {
    return <li {...props}>{children}</li>;
  },
  a: ({ children, ...props }) => (
    <a {...props} target="_blank" rel="noopener noreferrer">
      {children}
    </a>
  ),
};

export const ChatMessageBubbleMarkDown = ({ content }) => {
  return (
    <div className="p-chatMarkDown">
      <Markdown components={renderers} remarkPlugins={[remarkGfm]}>
        {content}
      </Markdown>
    </div>
  );
};

ChatMessageBubbleMarkDown.propTypes = { content: PropTypes.string.isRequired };
