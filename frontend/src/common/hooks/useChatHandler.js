import { useEffect, useState } from 'react';
import { useChatLimiter } from './useChatLimiter';
import { useValidateChat } from './useValidateChat';

const useChatHandler = () => {
  const {
    validate,
    clearError: clearValidateError,
    error: validateError,
  } = useValidateChat();
  const {
    checkChatLimits,
    clearError: clearLimitError,
    error: limitChatError,
    incrementChatCounter,
  } = useChatLimiter();
  const [chatError, setChatError] = useState(null);

  const clearChatErrors = () => {
    clearValidateError();
    clearLimitError();
  };

  const isChatValidAndAllowed = (text) => {
    return validate(text) && checkChatLimits();
  };

  useEffect(() => {
    const error = validateError || limitChatError;
    if (error) {
      setChatError(error);
    } else {
      setChatError(null);
    }
  }, [validateError, limitChatError]);

  return {
    chatError,
    clearChatErrors,
    isChatValidAndAllowed,
    incrementChatCounter,
  };
};

export default useChatHandler;
