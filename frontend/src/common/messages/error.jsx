import { useTranslation } from 'react-i18next';

export const useErrorMessages = () => {
  const { t } = useTranslation('common');
  return {
    NOT_FOUND: t('ページが見つかりませんでした'),
    INVALID_CODE: t('URLが間違っています'),
    CODE_EXPIRED: t('URLの有効期限が切れています'),
    MESSAGE_CONTAINS_EMAIL_ERROR: t('メールアドレスは送信できません。'),
    MESSAGE_EXCEEDS_MAX_LENGTH: t('メッセージが長すぎます。'),
    CHAT_REACHED_MINUTE_LIMIT: t('1分間の送信回数の上限に達しました。'),
    CHAT_REACHED_DAY_LIMIT: t('本日の送信回数の上限に達しました。'),
  };
};
