import PropTypes from 'prop-types';
import { useEffect, useMemo, useRef, useState } from 'react';

const ANIMATION_DURATION = 600;

const VerticalSlider = ({
  items,
  interval = 3000,
  visibleCount = 1,
  itemHeight = 26,
}) => {
  const containerRef = useRef(null);
  const [current, setCurrent] = useState(0);
  const total = items.length;

  const slideItems = useMemo(() => {
    const clones = items.slice(0, visibleCount);
    return [...items, ...clones];
  }, [items, visibleCount]);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrent(prev => prev + 1);
    }, interval);
    return () => clearInterval(timer);
  }, [interval]);

  useEffect(() => {
    const slider = containerRef.current;
    if (!slider) return;

    if (current <= total) {
      // 通常のスライド
      slider.style.transition = `transform ${ANIMATION_DURATION}ms ease-in-out`;
      slider.style.transform = `translateY(-${current * itemHeight}px)`;
    }

    if (current === total) {
      // クローン表示後に即座に戻す（ただし current はそのまま）
      setTimeout(() => {
        slider.style.transition = 'none';
        slider.style.transform = `translateY(0px)`;

        requestAnimationFrame(() => {
          requestAnimationFrame(() => {
            setCurrent(0);
            slider.style.transition = `transform ${ANIMATION_DURATION}ms ease-in-out`;
          });
        });
      }, ANIMATION_DURATION);
    }
  }, [current, total, itemHeight]);

  return (
    <div
      className="c-verticalSlider"
      style={{ height: `${itemHeight * visibleCount}px` }}
    >
      <ul ref={containerRef} className="c-verticalSlider__list">
        {slideItems.map((item, i) => (
          <li
            key={i}
            className="c-verticalSlider__text"
            style={{ height: `${itemHeight}px` }}
          >
            {item}
          </li>
        ))}
      </ul>
    </div>
  );
};

VerticalSlider.propTypes = {
  items: PropTypes.arrayOf(PropTypes.node).isRequired,
  interval: PropTypes.number,
  visibleCount: PropTypes.number,
  itemHeight: PropTypes.number,
};

export default VerticalSlider;
