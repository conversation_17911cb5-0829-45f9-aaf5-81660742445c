import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import FacebookShare from './SnsShare/FacebookShare';
import InstagramShare from './SnsShare/InstagramShare';
import TikTokShare from './SnsShare/TikTokShare';
import XShare from './SnsShare/XShare';
import YouTubeShare from './SnsShare/YouTubeShare';

function SnsShareContainer({ id, text, isShowX, isShowFacebook, isShowInstagram, isShowYouTube, isShowTikTok }) {
  const { t } = useTranslation('common');

  return (
    <div id={id} className="c-snsShare">
      <p className="c-snsShare__title">{t('結果をシェア')}</p>
      <ul className="c-snsShare__list">
        {isShowX && <li><XShare id={`${id}_x`} text={text} /></li>}
        {isShowFacebook && <li><FacebookShare id={`${id}_facebook`} text={text} /></li>}
        {isShowInstagram && <li><InstagramShare id={`${id}_instagram`} text={text} /></li>}
        {isShowYouTube && <li><YouTubeShare id={`${id}_youtube`} text={text} /></li>}
        {isShowTikTok && <li><TikTokShare id={`${id}_tiktok`} text={text} /></li>}
      </ul>
    </div>
  );
}

SnsShareContainer.propTypes = {
  id: PropTypes.string.isRequired,
  text: PropTypes.string.isRequired,
  isShowX: PropTypes.bool,
  isShowFacebook: PropTypes.bool,
  isShowInstagram: PropTypes.bool,
  isShowYouTube: PropTypes.bool,
  isShowTikTok: PropTypes.bool,
};

SnsShareContainer.defaultProps = {
  isShowX: true,
  isShowFacebook: true,
  isShowInstagram: true,
  isShowYouTube: true,
  isShowTikTok: true,
};

export default SnsShareContainer;
