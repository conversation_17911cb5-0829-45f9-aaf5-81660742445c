import PropTypes from 'prop-types';
import { Trans, useTranslation } from 'react-i18next';
import FqdnInputForm from './FqdnInputForm';

function FqdnSection({ onSubmit }) {
  const { t } = useTranslation('common');

  return (
    <section className="c-subSection">
      <div className="c-subSection__inner">
        <h2 className="c-title c-title--white">
          <Trans
            ns="common"
            i18nKey="Webサイト<sp>リスク診断も無料です</sp><white>Website Risk Assessment</white>"
            components={{
              sp: <span />,
              white: <span className="c-title__sub c-title__sub--white" />,
            }}
          />
        </h2>
        <p className="c-subSection__text">
          {t('WEBの侵入リスクなどを無料でお調べいたします')}
        </p>
        <div className="c-subSection__form">
          <FqdnInputForm onSubmit={onSubmit} />
        </div>
        <p className="c-subSection__text">
          <Trans
            ns="common"
            i18nKey="「 脆弱性診断 」<sp>「 クラウド利用・リスク診断 」</sp><sp>「実在証明・盗聴防止（SSL）診断」</sp><br /><sp>「なりすまし診断」を同時に実施します。</sp>"
            components={{ sp: <span />, br: <br /> }}
          />
        </p>
        <p className="c-subSection__note">{t('診断結果は順次表示されます。')}</p>
        {/* <div className="c-subSection__img">
          <img src={FQDN_SECTION_IMAGE} alt="" width="582" height="192" />
        </div> */}
      </div>
    </section>
  );
}

FqdnSection.propTypes = { onSubmit: PropTypes.func.isRequired };

export default FqdnSection;
