import PropTypes from 'prop-types';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import Button from './Button';

function EmailInputForm({ onSubmit }) {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { t } = useTranslation('common');

  const handleSubmit = (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    if (email.trim()) {
      onSubmit(email.trim());
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="c-inputSearch">
        <input
          type="email"
          id="email"
          value={email}
          onChange={e => setEmail(e.target.value)}
          placeholder={t('メールアドレスを入力')}
          className="c-inputSearch__input c-inputSearch__input--border"
        />
        <div className="c-inputSearch__button">
          <Button type="submit" variant="search" disabled={isSubmitting}>
            {isSubmitting
              ? (
                <span className="c-loader c-loader--button" />
              )
              : (
                <span className="icon-base icon-sec-search-bold icon-size16 icon-color-black" />
              )}
            <span className="c-inputSearch__buttonText">{t('無料診断')}</span>
          </Button>
        </div>
      </div>
    </form>
  );
}

EmailInputForm.propTypes = { onSubmit: PropTypes.func.isRequired };

export default EmailInputForm;
