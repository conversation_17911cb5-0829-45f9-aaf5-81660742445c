import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';

const PeriodicCheckupArea = ({ children, nextDateStr }) => {
  const { t } = useTranslation('check_password');

  return (
    <div className="c-periodicCheck">
      <div className="c-periodicCheck__head">
        <div className="c-periodicCheck__textWrap">
          <div className="c-periodicCheck__icon">
            <span className="icon-base icon-sec-cycle icon-size16 icon-color-darkGreen" />
          </div>
          <p className="c-periodicCheck__text">
            {t('定期診断を無償で行い、メールでお知らせします。')}
          </p>
        </div>
        <div className="c-periodicCheck__list">{children}</div>
      </div>
      {nextDateStr && (
        <div className="c-periodicCheck__date">
          {t('次回診断日：{{date}}', { date: nextDateStr })}
        </div>
      )}
    </div>
  );
};

PeriodicCheckupArea.propTypes = {
  children: PropTypes.node.isRequired,
  nextDateStr: PropTypes.string,
};

export default PeriodicCheckupArea;
