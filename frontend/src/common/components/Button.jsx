import PropTypes from 'prop-types';

function Button({
  children,
  isLoading,
  as = 'button',
  external = false,
  exIcon = '',
  variant = 'primary',
  widthSize = '',
  className = '',
  ...props
}) {
  const variantStyles = {
    primary: 'c-buttonPrimary',
    secondary: 'c-buttonSecondary',
    accent: 'c-buttonAccent',
    accentSecondary: 'c-buttonAccentSecondary',
    accentTertiary: 'c-buttonAccentTertiary',
    search: 'c-buttonSearch',
    searchHome: 'c-buttonSearchHome',
    chat: 'c-buttonChat',
    brandTldPrimary: 'c-buttonTldPrimary',
    brandTldAccent: 'c-buttonTldAccent',
    brandTldSecondary: 'c-buttonTldSecondary',
    ossPrimary: 'c-buttonOssPrimary',
    ossSecondary: 'c-buttonOssSecondary',
    image: 'c-buttonImage',
    text: 'c-buttonText',
    textXs: 'c-buttonText c-buttonText--xs',
    textInline: 'c-buttonText c-buttonText--inline',
    textUnderLine: 'c-buttonText c-buttonText--underline',
  };
  const widthStyles = widthSize === 'full' ? 'c-button--sizeFull' : '';
  const exIconStyles
    = exIcon === 'large'
      ? 'icon-base icon-sec-link icon-size16'
      : exIcon === 'small'
        ? 'icon-base icon-sec-link icon-size12'
        : exIcon === 'xsmall'
          ? 'icon-base icon-sec-link icon-size10'
          : '';

  const disabledStyles = isLoading || props.disabled ? '' : '';

  const Component = as;

  return (
    <Component
      className={`${variantStyles[variant]} ${widthStyles} ${disabledStyles} ${className}`}
      disabled={isLoading || props.disabled}
      {...props}
    >
      {isLoading && <div className="c-loader c-loader--button" />}
      {children}
      {external && <span className={`${exIconStyles}`} />}
    </Component>
  );
}

Button.propTypes = {
  children: PropTypes.node,
  isLoading: PropTypes.bool,
  as: PropTypes.oneOfType([PropTypes.string, PropTypes.elementType]),
  external: PropTypes.bool,
  exIcon: PropTypes.string,
  variant: PropTypes.string,
  widthSize: PropTypes.string,
  className: PropTypes.string,
  disabled: PropTypes.bool,
};

export default Button;
