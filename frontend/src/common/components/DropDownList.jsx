import PropTypes from 'prop-types';
import { useState } from 'react';

const DropDownList = ({ name, options, value, onChange, disabled = false }) => {
  const [show, setShow] = useState(false);
  return (
    <div className="c-dropDownList">
      <button
        id={`${name}_open`}
        onClick={() => setShow(!show)}
        disabled={disabled}
        className={` ${
          show
            ? 'c-dropDownList__select c-dropDownList__select--show'
            : 'c-dropDownList__select'
        }`}
      >
        {value}
      </button>
      {show && (
        /* optionOverlayを全画面に配置、リスト外クリックで閉じることが可能 */
        <>
          <div
            id={`${name}_close`}
            onClick={() => setShow(false)}
            className="c-dropDownList__optionOverlay"
          />
          <ul className="c-dropDownList__option">
            {options.map((option, index) => (
              <li
                id={`${name}_data_${option}`}
                key={index}
                data-value={option}
                onClick={() => {
                  onChange(option);
                  setShow(false);
                }}
              >
                {option}
              </li>
            ))}
          </ul>
        </>
      )}
    </div>
  );
};

DropDownList.propTypes = {
  name: PropTypes.string.isRequired,
  options: PropTypes.arrayOf(PropTypes.string).isRequired,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
};

export default DropDownList;
