import PropTypes from 'prop-types';

const ToggleSwitch = ({ name, isOn, handleToggle }) => {
  return (
    <div className="c-toggleSwitch">
      <input
        checked={isOn}
        onChange={handleToggle}
        className="c-toggleSwitch__input"
        id={name}
        type="checkbox"
      />
      <label
        className={`c-toggleSwitch__label ${
          isOn ? 'c-toggleSwitch__label--on' : 'c-toggleSwitch__label--off'
        }`}
        htmlFor={name}
      />
    </div>
  );
};

ToggleSwitch.propTypes = {
  name: PropTypes.string.isRequired,
  isOn: PropTypes.bool.isRequired,
  handleToggle: PropTypes.func.isRequired,
};

export default ToggleSwitch;
