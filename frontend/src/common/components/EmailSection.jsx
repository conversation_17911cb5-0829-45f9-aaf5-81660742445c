import PropTypes from 'prop-types';
import { Trans, useTranslation } from 'react-i18next';
import EmailInputForm from './EmailInputForm';

function EmailSection({ onSubmit }) {
  const { t } = useTranslation('common');

  return (
    <section className="c-subSection">
      <div className="c-subSection__inner">
        <h2 className="c-title c-title--white">
          <Trans
            ns="common"
            i18nKey="パスワード漏洩診断も無料です<white>Password Leak Detection</white>"
            components={{ white: <span className="c-title__sub c-title__sub--white" /> }}
          />
        </h2>
        <p className="c-subSection__text">
          {t('メールアドレスを入力して、パスワード漏洩の有無を無料でお調べいたします。')}
        </p>
        <div className="c-subSection__form">
          <EmailInputForm onSubmit={onSubmit} />
        </div>
        {/* <div className="c-subSection__img">
          <img src={EMAIL_SECTION_IMAGE} alt="" width="485" height="200" />
        </div> */}
      </div>
    </section>
  );
}

EmailSection.propTypes = { onSubmit: PropTypes.func.isRequired };

export default EmailSection;
