import PropTypes from 'prop-types';

const InfoTooltip = ({ content }) => {
  return (
    <div className="c-infoTooltip">
      <div className="c-infoTooltip__icon">
        <div className="icon-base icon-sec-question icon-size14 icon-color-gray40" />
      </div>
      <div className="c-infoTooltip__content">{content}</div>
    </div>
  );
};

InfoTooltip.propTypes = { content: PropTypes.string.isRequired };

export default InfoTooltip;
