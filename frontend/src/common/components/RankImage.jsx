import PropTypes from 'prop-types';
import rankAlert from '../../assets/img/rank_alert.png';
import rankSafe from '../../assets/img/rank_safe.png';
import rankWarning from '../../assets/img/rank_warning.png';

const ndsRankImages = {
  A: rankSafe,
  B: rankSafe,
  C: rankWarning,
  D: rankWarning,
  E: rankAlert,
};

const impersonationRankImages = {
  A: rankSafe,
  B: rankWarning,
  C: rankAlert,
  D: rankAlert,
};

function RankImage({ rank, type }) {
  if (type === 'nds') {
    return <img src={ndsRankImages[rank]} alt={`ランク${rank}`} />;
  }
  return <img src={impersonationRankImages[rank]} alt={`ランク${rank}`} />;
}

RankImage.propTypes = {
  rank: PropTypes.oneOf(['A', 'B', 'C', 'D', 'E']).isRequired,
  type: PropTypes.oneOf(['nds', 'impersonation']).isRequired,
};

export default RankImage;
