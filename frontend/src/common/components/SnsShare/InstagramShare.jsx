import PropTypes from 'prop-types';
import { generateShareUrl } from '../../utils/shareUtils';

function InstagramShare({ id, text }) {
  const shareUrl = generateShareUrl('instagram', text);

  return (
    <a
      id={id}
      href={shareUrl}
      className="c-snsShare__button c-snsShare__button--instagram"
      target="_blank"
      rel="noopener noreferrer"
      aria-label="Share on Instagram"
    >
    </a>
  );
}

InstagramShare.propTypes = { id: PropTypes.string.isRequired, text: PropTypes.string.isRequired };

export default InstagramShare;
