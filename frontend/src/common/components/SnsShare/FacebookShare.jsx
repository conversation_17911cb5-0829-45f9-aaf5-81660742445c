import PropTypes from 'prop-types';
import { generateShareUrl } from '../../utils/shareUtils';

function FacebookShare({ id, text }) {
  const shareUrl = generateShareUrl('facebook', text);

  return (
    <a
      id={id}
      href={shareUrl}
      className="c-snsShare__button c-snsShare__button--facebook"
      target="_blank"
      rel="noopener noreferrer"
      aria-label="Share on Facebook"
    >
    </a>
  );
}

FacebookShare.propTypes = { id: PropTypes.string.isRequired, text: PropTypes.string.isRequired };

export default FacebookShare;
