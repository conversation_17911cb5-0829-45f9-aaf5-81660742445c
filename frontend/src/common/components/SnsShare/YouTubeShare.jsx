import PropTypes from 'prop-types';
import { generateShareUrl } from '../../utils/shareUtils';

function YouTubeShare({ id, text }) {
  const shareUrl = generateShareUrl('youtube', text);

  return (
    <a
      id={id}
      href={shareUrl}
      className="c-snsShare__button c-snsShare__button--youtube"
      target="_blank"
      rel="noopener noreferrer"
      aria-label="Share on YouTube"
    >
    </a>
  );
}

YouTubeShare.propTypes = { id: PropTypes.string.isRequired, text: PropTypes.string.isRequired };

export default YouTubeShare;
