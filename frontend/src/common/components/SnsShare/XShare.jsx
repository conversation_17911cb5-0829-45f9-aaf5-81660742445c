import PropTypes from 'prop-types';
import xIcon from '../../../assets/img/icon-x.svg';
import { generateShareUrl } from '../../utils/shareUtils';

const X_ICON = xIcon;

function XShare({ id, text }) {
  const shareUrl = generateShareUrl('x', text);

  return (
    <a
      id={id}
      href={shareUrl}
      className="c-snsShare__button"
      target="_blank"
      rel="noopener noreferrer"
      aria-label="Share on X"
    >
      <img src={X_ICON} alt="X" />
    </a>
  );
}

XShare.propTypes = { id: PropTypes.string.isRequired, text: PropTypes.string.isRequired };

export default XShare;
