import PropTypes from 'prop-types';
import { generateShareUrl } from '../../utils/shareUtils';

function TikTokShare({ id, text }) {
  const shareUrl = generateShareUrl('tiktok', text);

  return (
    <a
      id={id}
      href={shareUrl}
      className="c-snsShare__button c-snsShare__button--tiktok"
      target="_blank"
      rel="noopener noreferrer"
      aria-label="Share on TikTok"
    >
    </a>
  );
}

TikTokShare.propTypes = { id: PropTypes.string.isRequired, text: PropTypes.string.isRequired };

export default TikTokShare;
