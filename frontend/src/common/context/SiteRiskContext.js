import { createContext } from 'react';

export const SiteRiskContext = createContext(null);

const STATUSES = {
  alert: 1,
  warning: 2,
  safe: 3,
};

export const getSiteRiskWorstStatus = ({ nds, ssl, impersonation }) => {
  let ndsStatus = 'safe';
  switch (nds.rank) {
    case 'A':
      ndsStatus = 'safe';
      break;
    case 'B':
      ndsStatus = 'safe';
      break;
    case 'C':
      ndsStatus = 'warning';
      break;
    case 'D':
      ndsStatus = 'warning';
      break;
    case 'E':
      ndsStatus = 'alert';
      break;
    default:
      ndsStatus = 'safe';
      break;
  }

  let sslStatus = ssl.status;

  let impersonationStatus = 'safe';
  switch (impersonation.rank) {
    case 'A':
      impersonationStatus = 'safe';
      break;
    case 'B':
      impersonationStatus = 'warning';
      break;
    case 'C':
      impersonationStatus = 'alert';
      break;
    case 'D':
      impersonationStatus = 'alert';
      break;
    default:
      impersonationStatus = 'safe';
      break;
  }

  const worstStatus = [ndsStatus, sslStatus, impersonationStatus].sort(
    (a, b) => STATUSES[a] - STATUSES[b],
  )[0];

  return worstStatus;
};
