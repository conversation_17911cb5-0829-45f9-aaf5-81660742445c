const getLangQuery = () => {
  const params = new URLSearchParams(window.location.search);
  return params.get('lang');
};

export const appendLangQuery = (path) => {
  const lang = getLangQuery();
  if (!lang) return path;

  const [basePath, existingQuery] = path.split('?');
  const query = new URLSearchParams(existingQuery || '');
  query.set('lang', lang);

  return `${basePath}?${query.toString()}`;
};

export const navigateWithLanguage = (path) => {
  window.location.href = appendLangQuery(path);
};

export const openWithLanguage = (path) => {
  window.open(appendLangQuery(path), '_blank');
};
