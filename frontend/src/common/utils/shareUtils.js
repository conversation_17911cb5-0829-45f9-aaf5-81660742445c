/**
 * SNSシェア機能のユーティリティ関数
 */

/**
 * 指定されたプラットフォームでテキストを共有するためのURLを生成します
 * @param {string} platform - シェアするプラットフォーム名
 * @param {string} text - シェアするテキスト
 * @returns {string} シェアURL
 */
export const generateShareUrl = (platform, text) => {
  const encodedText = encodeURIComponent(text);
  const currentUrl = encodeURIComponent(window.location.href);

  switch (platform) {
    case 'x':
      return `https://twitter.com/intent/tweet?text=${encodedText}`;
    case 'facebook':
      return `https://www.facebook.com/sharer/sharer.php?u=${currentUrl}&quote=${encodedText}`;
    case 'instagram':
      return 'https://www.instagram.com/';
    case 'youtube':
      return 'https://www.youtube.com/';
    case 'tiktok':
      return 'https://www.tiktok.com/';
    default:
      return '';
  }
};

/**
 * 指定されたURLを新しいウィンドウで開きます
 * @param {string} url - 開くURL
 */
export const openShareWindow = (url) => {
  if (url) {
    window.open(url, '_blank');
  }
};

export const generatePasswordShareContent = (leakCount) => {
  const url = leakCount === 0
    ? 'security24.gmo/?share_p1'
    : 'security24.gmo/?share_p2';

  const emoji = leakCount === 0 ? '🎉' : '😱';

  return `／

無料パスワード漏洩診断の結果・・・
パスワード漏洩数は「${leakCount}件」でした${emoji}

＼
------

あなたのパスワードは安全ですか
無料診断： ${url}

#ネットのセキュリティもGMO #GMOセキュリティ24`;
};

export const generateSiteRiskShareContent = (riskLevel) => {
  const urls = {
    safe: 'security24.gmo/?share_w1',
    warning: 'security24.gmo/?share_w2',
    alert: 'security24.gmo/?share_w3',
  };

  const statuses = {
    safe: '安全判定',
    warning: '要注意判定',
    alert: '危険判定',
  };

  const emoji = {
    safe: '🎉',
    warning: '😢',
    alert: '😱',
  };

  return `／

無料Webサイトリスク診断 の結果・・・
私のサイトは「${statuses[riskLevel]}」でした${emoji[riskLevel]}

＼
------

あなたのWebサイトは安全ですか
無料診断： ${urls[riskLevel]}

#ネットのセキュリティもGMO #GMOセキュリティ24`;
};
