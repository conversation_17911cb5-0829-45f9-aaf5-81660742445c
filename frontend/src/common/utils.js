import { validateEmail } from './validators/index.js';

export const isValidEmail = (value) => {
  return validateEmail(value);
};

export const isValidFqdn = (value) => {
  const standardDomainRegex
    = /^(?=.{1,255}$)([A-Za-z0-9](?:[A-Za-z0-9-]{0,61}[A-Za-z0-9])?\.)+[A-Za-z]{2,}$/;
  const japaneseDomainRegex
    = /^(?=.{1,255}$)([A-Za-z0-9\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FFF](?:[A-Za-z0-9\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FFF-]{0,61}[A-Za-z0-9\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FFF])?\.)+[A-Za-z\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FFF]{2,}$/;
  return standardDomainRegex.test(value) || japaneseDomainRegex.test(value);
};

export const calculateRemainder = (expiredAt) => {
  const currentDate = new Date();

  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth();
  const currentDay = currentDate.getDate();

  const expiredYear = expiredAt.getFullYear();
  const expiredMonth = expiredAt.getMonth();
  const expiredDay = expiredAt.getDate();

  let monthsDiff
    = (expiredYear - currentYear) * 12 + (expiredMonth - currentMonth);

  if (expiredDay < currentDay) {
    monthsDiff -= 1;
  }

  if (monthsDiff >= 1) {
    return `あと${monthsDiff}ヶ月`;
  } else {
    const timeDiffInMilliseconds = expiredAt - currentDate;
    const daysDiff = Math.ceil(timeDiffInMilliseconds / (1000 * 60 * 60 * 24));
    return `あと${daysDiff}日`;
  }
};

export const isValidUrl = (value) => {
  const urlRegex
    = /^(https?:\/\/)?((?=.{1,255}$)([A-Za-z0-9\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FFF](?:[A-Za-z0-9\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FFF-]{0,61}[A-Za-z0-9\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FFF])?\.)+[A-Za-z\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FFF]{2,})?(\/.*)?$/;

  return urlRegex.test(value);
};

export const getFqdn = (value) => {
  if (isValidFqdn(value)) {
    return value;
  }

  if (isValidUrl(value)) {
    const urlParts = value.replace(/^(https?:\/\/)?/, '').split('/');
    return urlParts[0];
  }
};

export const validateChat = (value) => {
  const emailRegex = /\b[^\s@]+@[^\s@]+\.[^\s@]+\b/;
  const hasEmail = emailRegex.test(value);

  return !hasEmail;
};

export function formatDatetime(isoString) {
  const isoMatch1 = isoString.match(/(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):/);
  if (isoMatch1) {
    const [, year, month, day, hour, minute] = isoMatch1;
    return `${year}年${month}月${day}日${hour}時${minute}分`;
  }

  const isoMatch2 = isoString.match(
    /(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})/,
  );
  if (isoMatch2) {
    const [, year, month, day, hour, minute] = isoMatch2;
    return `${year}年${month}月${day}日${hour}時${minute}分`;
  }

  return isoString;
}

/**
 * 指定された有効桁数で数値を丸める
 * @param {number} value - 丸める数値
 * @param {number} digits - 有効桁数
 * @returns {number} - 丸められた数値
 */
export const roundToSignificantDigits = (value, digits) => {
  if (value === 0) return 0;
  const multiplier = 10 ** (digits - 1 - Math.floor(Math.log10(Math.abs(value))));
  return Math.round(value * multiplier) / multiplier;
};

/**
 * 日本円表示用のフォーマット関数
 * @param {number} value - フォーマットする数値
 * @returns {string} - フォーマットされた文字列（例: 3,400万円）
 */
export const formatJapaneseYen = (value) => {
  if (value >= 10000) {
    const inMan = value / 10000;
    return `${inMan.toLocaleString()}万円`;
  }
  return `${value.toLocaleString()}円`;
};
