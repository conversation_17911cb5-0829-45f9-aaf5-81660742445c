import chatStyleUrl from '@chatscope/chat-ui-kit-styles/dist/default/styles.min.css?url';
import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import './assets/js/index.js';
import App from './App.jsx';
import styleUrl from './assets/css/style.scss?url';

const loadCss = (href, id) => {
  return new Promise((resolve) => {
    if (document.getElementById(id)) {
      return resolve(); // すでに読み込み済みなら即 resolve
    }

    const link = document.createElement('link');
    link.id = id;
    link.rel = 'stylesheet';
    link.href = href;
    link.onload = () => resolve();
    document.head.appendChild(link);
  });
};

const WrappedApp = () => {
  if (import.meta.env.VITE_ENV === 'local') {
    return <App />;
  }
  return (
    <StrictMode>
      <App />
    </StrictMode>
  );
};

(async () => {
  await Promise.all([
    loadCss(`${chatStyleUrl}?ts=${__BUILD_TIMESTAMP__}`, 'chat-css'),
    loadCss(`${styleUrl}?ts=${__BUILD_TIMESTAMP__}`, 'app-css'),
  ]);
  createRoot(document.getElementById('app')).render(<WrappedApp />);
})();
