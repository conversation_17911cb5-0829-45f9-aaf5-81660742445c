import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { BrowserRouter, Route, Routes, useLocation } from 'react-router-dom';
import './i18n';
import Chat from './apps/chat/Chat';
import Complete from './apps/complete/Complete';
import Confirm from './apps/confirm/Confirm';
import Email from './apps/email/Email';
import Home from './apps/home/<USER>';
import OssSupport from './apps/oss-support/OssSupport';
import Password from './apps/password/Password';
import SiteRisk from './apps/site-risk/SiteRisk';
import Verify from './apps/verify/Verify';
import Yourbrand from './apps/yourbrand/Yourbrand';
import YourbrandSearch from './apps/yourbrand/YourbrandSearch';

function LanguageWrapper({ children }) {
  const { i18n } = useTranslation();
  const location = useLocation();

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const lang = params.get('lang');
    const supportedLangs = ['en', 'ja'];
    const nextLang = supportedLangs.includes(lang || '') ? lang : 'ja';

    if (lang && i18n.language !== nextLang) {
      i18n.changeLanguage(nextLang);
    }
  }, [location.search, i18n]);

  return children;
}

function App() {
  return (
    <>
      <BrowserRouter>
        <LanguageWrapper>
          <Routes>
            <Route
              path="/security/"
              element={<Home />}
            />
            <Route
              path="/security/check/complete/"
              element={<Complete />}
            />
            <Route
              path="/security/check/confirm/"
              element={<Confirm />}
            />
            <Route
              path="/security/check/email/"
              element={<Email />}
            />
            <Route
              path="/security/check/verify/"
              element={<Verify />}
            />
            <Route
              path="/security/check/password/"
              element={<Password />}
            />
            <Route
              path="/security/check/site-risk/"
              element={<SiteRisk />}
            />
            <Route
              path="/security/check/chat/"
              element={<Chat />}
            />
            <Route
              path="/security/yourbrand/"
              element={<Yourbrand />}
            />
            <Route
              path="/security/yourbrand/search/"
              element={<YourbrandSearch />}
            />
            <Route
              path="/security/oss-support/"
              element={<OssSupport />}
            />
            <Route
              path="/*"
              element={<h1>Not Found Page</h1>}
            />
          </Routes>
        </LanguageWrapper>
      </BrowserRouter>
    </>
  );
}

export default App;
