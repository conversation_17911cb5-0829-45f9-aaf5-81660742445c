const enModules = import.meta.glob('../assets/locales/en/*.json', { query: '?url', eager: true });
const jaModules = import.meta.glob('../assets/locales/ja/*.json', { query: '?url', eager: true });

function extractLocaleUrls(modules) {
  const result = {};
  for (const path in modules) {
    const match = path.match(/\/([a-z-_]+)\.json$/);
    if (match) {
      const key = match[1]; // ファイル名部分（拡張子除く）
      result[key] = modules[path].default;
    }
  }
  return result;
}

export const localeUrls = {
  en: extractLocaleUrls(enModules),
  ja: extractLocaleUrls(jaModules),
};
