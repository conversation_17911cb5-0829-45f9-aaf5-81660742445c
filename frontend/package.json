{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev-build": "vite build --mode=dev", "prod-build": "vite build --mode=prod", "build": "vite build", "dev-deploy": "yarn dev-build && gsutil cp -r ./dist/static gs://dev-security-static", "prod-deploy": "yarn prod-build && gsutil cp -r ./dist/static gs://security-static", "lint": "eslint .", "license-checker": "license-checker --onlyAllow 'MIT;ISC;Apache-2.0;BSD' --production", "preview": "vite preview"}, "dependencies": {"@chatscope/chat-ui-kit-react": "^2.0.3", "@chatscope/chat-ui-kit-styles": "^1.4.0", "ajv": "^8.17.1", "ajv-errors": "^3.0.0", "ajv-formats": "^3.0.1", "ajv-keywords": "^5.1.0", "framer-motion": "^12.22.0", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "lucide-react": "^0.469.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.6.0", "react-markdown": "^9.0.3", "react-responsive": "^10.0.1", "react-router-dom": "^7.1.0", "react-toastify": "^11.0.5", "remark-gfm": "^4.0.0"}, "devDependencies": {"@stylistic/eslint-plugin": "^4.2.0", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.17.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.13.0", "license-checker": "^25.0.1", "postcss": "8", "sass-embedded": "^1.83.4", "stylelint": "^16.13.2", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-order": "^6.0.4", "vite": "^6.0.3"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}