export const CVE_NAMES = {
  'CVE-2020-1927': {
    description: '脆弱性が報告されているバージョンのソフトウェアを利用しています。\nこの脆弱性により、攻撃者がシステムに対して不正な操作を行うリスクが高まります。\n該当するソフトウェアについては、最新のパッチを適用し、脆弱性を修正することを強く推奨します。\n\n＜対象のバージョン情報一覧＞\n・2.4.41\n\n＜検出したCVE番号一覧＞\n・CVE-2006-20001\n・CVE-2019-17567\n・CVE-2020-1927\n・CVE-2020-1934\n・CVE-2020-9490\n・CVE-2020-11984\n・CVE-2020-11993\n・CVE-2020-13938\n・CVE-2020-13950\n・CVE-2020-35452\n・CVE-2021-26690\n・CVE-2021-26691\n・CVE-2021-30641\n・CVE-2021-33193\n・CVE-2021-34798\n・CVE-2021-36160\n・CVE-2021-39275\n・CVE-2021-40438\n・CVE-2021-44224\n・CVE-2021-44790\n・CVE-2022-22719\n・CVE-2022-22720\n・CVE-2022-22721\n・CVE-2022-23943\n・CVE-2022-26377\n・CVE-2022-28330\n・CVE-2022-28614\n・CVE-2022-28615\n・CVE-2022-29404\n・CVE-2022-30556\n・CVE-2022-31813\n・CVE-2022-36760\n・CVE-2022-37436\n・CVE-2023-25690\n・CVE-2023-27522\n・CVE-2023-31122\n・CVE-2023-45802\n・CVE-2024-27316\n・CVE-2024-38474\n・CVE-2024-38476\n・CVE-2024-38477\n・CVE-2024-40898',
    impact: 'バージョン情報をもとに、既知の脆弱性を狙った攻撃を受ける恐れがあります。\nまた、検出されたバージョンのソフトウェアに既知の脆弱性がない場合でも、調査により他の要因から脆弱性が見つかり、攻撃の糸口となる可能性があります。\nバージョン情報の秘匿化と、適切なセキュリティ対策の実施が重要です。',
    measure: '脆弱性が確認された場合、公式のセキュリティパッチがリリースされていることを確認し、速やかに適用することで、システムの安全性を確保します。\n\n1. 使用しているソフトウェアやシステムに脆弱性が存在する場合、公式のセキュリティ情報（ベンダーサイト、セキュリティアドバイザリ）を確認し、対象となる脆弱性に対するセキュリティパッチがリリースされているかを確認します。\n2. セキュリティパッチや最新バージョンを公式サイトまたは信頼できるソースからダウンロードします。\n3. パッチ適用前に、システム全体や関連ファイルのバックアップを取得し、万が一のトラブルに備えます。\n4. パッチ適用手順に従い、セキュリティパッチをシステムに適用します。\n5. パッチの適用後、必要に応じてシステムやサービスを再起動し、正常に動作しているかを確認します。\n6. セキュリティパッチの適用後、システムログやアクセスログを監視し、異常な動作や不正アクセスがないかを確認します。\n\nこのプロセスを定期的に実行し脆弱性が発見された場合には即時に対策を行うことでセキュリティリスクを低減できます。',
  },
  'CVE-2017-20005': {
    description: '脆弱性が報告されているバージョンのソフトウェアを利用しています。この脆弱性により、攻撃者がシステムに対して不正な操作を行うリスクが高まります。該当するソフトウェアについては、最新のパッチを適用し、脆弱性を修正することを強く推奨します。＜対象のバージョン情報一覧＞・1.10.2＜検出したCVE番号一覧＞CVE番号は脆弱性の識別番号です。また、PoCとは脆弱性への攻撃手法が公開されている状態です。PoCが公開されている場合、攻撃者に対象の脆弱性を悪用されやすい状態にあります。「脆弱性がどれくらい危険か（CVSSスコア）」と「攻撃手法が公開されているか（PoCの有/無）」の2つの観点を注視すると、どの脆弱性から対策すべきかの判断が効果的にできるようになります。・CVE-2017-7529　　CVSS: 7.5　　Nginx versions since 0.5.6 up to and including 1.13.2 are vulnerable to integer overflow vulnerability in nginx range filter module resulting into leak of potentially sensitive information triggered by specially crafted request.・CVE-2017-20005 PoC　　CVSS: 9.8　　NGINX before 1.13.6 has a buffer overflow for years that exceed four digits, as demonstrated by a file with a modification date in 1969 that causes an integer overflow (or a false modification date far in the future), when encountered by the autoindex module.・CVE-2018-16843　　CVSS: 5.3　　nginx before versions 1.15.6 and 1.14.1 has a vulnerability in the implementation of HTTP/2 that can allow for excessive memory consumption. This issue affects nginx compiled with the ngx_http_v2_module (not compiled by default) if the \'http2\' option of the \'listen\' directive is used in a configuration file.・CVE-2018-16844　　CVSS: 5.3　　nginx before versions 1.15.6 and 1.14.1 has a vulnerability in the implementation of HTTP/2 that can allow for excessive CPU usage. This issue affects nginx compiled with the ngx_http_v2_module (not compiled by default) if the \'http2\' option of the \'listen\' directive is used in a configuration file.・CVE-2018-16845　　CVSS: 8.2　　nginx before versions 1.15.6, 1.14.1 has a vulnerability in the ngx_http_mp4_module, which might allow an attacker to cause infinite loop in a worker process, cause a worker process crash, or might result in worker process memory disclosure by using a specially crafted mp4 file. The issue only affects nginx if it is built with the ngx_http_mp4_module (the module is not built by default) and the .mp4. directive is used in the configuration file. Further, the attack is only possible if an attacker is able to trigger processing of a specially crafted mp4 file with the ngx_http_mp4_module.・CVE-2019-9511　　CVSS: 7.5　　Some HTTP/2 implementations are vulnerable to window size manipulation and stream prioritization manipulation, potentially leading to a denial of service. The attacker requests a large amount of data from a specified resource over multiple streams. They manipulate window size and stream priority to force the server to queue the data in 1-byte chunks. Depending on how efficiently this data is queued, this can consume excess CPU, memory, or both.・CVE-2019-9513　　CVSS: 7.5　　Some HTTP/2 implementations are vulnerable to resource loops, potentially leading to a denial of service. The attacker creates multiple request streams and continually shuffles the priority of the streams in a way that causes substantial churn to the priority tree. This can consume excess CPU.・CVE-2019-9516　　CVSS: 7.5　　Some HTTP/2 implementations are vulnerable to a header leak, potentially leading to a denial of service. The attacker sends a stream of headers with a 0-length header name and 0-length header value, optionally Huffman encoded into 1-byte or greater headers. Some implementations allocate memory for these headers and keep the allocation alive until the session dies. This can consume excess memory.・CVE-2019-20372 PoC　　CVSS: 5.3　　NGINX before 1.17.7, with certain error_page configurations, allows HTTP request smuggling, as demonstrated by the ability of an attacker to read unauthorized web pages in environments where NGINX is being fronted by a load balancer.・CVE-2021-3618　　CVSS: 7.4　　ALPACA is an application layer protocol content confusion attack, exploiting TLS servers implementing different protocols but using compatible certificates, such as multi-domain or wildcard certificates. A MiTM attacker having access to victim\'s traffic at the TCP/IP layer can redirect traffic from one subdomain to another, resulting in a valid TLS session. This breaks the authentication of TLS and cross-protocol attacks may be possible where the behavior of one protocol service may compromise the other at the application layer.・CVE-2021-23017　　CVSS: 7.7　　A security issue in nginx resolver was identified, which might allow an attacker who is able to forge UDP packets from the DNS server to cause 1-byte memory overwrite, resulting in worker process crash or potential other impact.・CVE-2022-41741　　CVSS: 7.0　　NGINX Open Source before versions 1.23.2 and 1.22.1, NGINX Open Source Subscription before versions R2 P1 and R1 P1, and NGINX Plus before versions R27 P1 and R26 P1 have a vulnerability in the module ngx_http_mp4_module that might allow a local attacker to corrupt NGINX worker memory, resulting in its termination or potential other impact using a specially crafted audio or video file. The issue affects only NGINX products that are built with the ngx_http_mp4_module, when the mp4 directive is used in the configuration file. Further, the attack is possible only if an attacker can trigger processing of a specially crafted audio or video file with the module ngx_http_mp4_module.・CVE-2022-41742　　CVSS: 7.1　　NGINX Open Source before versions 1.23.2 and 1.22.1, NGINX Open Source Subscription before versions R2 P1 and R1 P1, and NGINX Plus before versions R27 P1 and R26 P1 have a vulnerability in the module ngx_http_mp4_module that might allow a local attacker to cause a worker process crash, or might result in worker process memory disclosure by using a specially crafted audio or video file. The issue affects only NGINX products that are built with the module ngx_http_mp4_module, when the mp4 directive is used in the configuration file. Further, the attack is possible only if an attacker can trigger processing of a specially crafted audio or video file with the module ngx_http_mp4_module.・CVE-2023-44487 PoC　　CVSS: 7.5　　The HTTP/2 protocol allows a denial of service (server resource consumption) because request cancellation can reset many streams quickly, as exploited in the wild in August through October 2023.',
    impact: 'バージョン情報をもとに、既知の脆弱性を狙った攻撃を受ける恐れがあります。また、検出されたバージョンのソフトウェアに既知の脆弱性がない場合でも、調査により他の要因から脆弱性が見つかり、攻撃の糸口となる可能性があります。バージョン情報の秘匿化と、適切なセキュリティ対策の実施が重要です。',
    measure: '脆弱性が確認された場合、公式のセキュリティパッチがリリースされていることを確認し、速やかに適用することで、システムの安全性を確保します。1. 使用しているソフトウェアやシステムに脆弱性が存在する場合、公式のセキュリティ情報（ベンダーサイト、セキュリティアドバイザリ）を確認し、対象となる脆弱性に対するセキュリティパッチがリリースされているかを確認します。2. セキュリティパッチや最新バージョンを公式サイトまたは信頼できるソースからダウンロードします。3. パッチ適用前に、システム全体や関連ファイルのバックアップを取得し、万が一のトラブルに備えます。4. パッチ適用手順に従い、セキュリティパッチをシステムに適用します。5. パッチの適用後、必要に応じてシステムやサービスを再起動し、正常に動作しているかを確認します。6. セキュリティパッチの適用後、システムログやアクセスログを監視し、異常な動作や不正アクセスがないかを確認します。このプロセスを定期的に実行し脆弱性が発見された場合には即時に対策を行うことでセキュリティリスクを低減できます。',
  },
};
