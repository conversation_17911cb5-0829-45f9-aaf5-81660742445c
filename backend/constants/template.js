export const TEMPLATE_CONFIRM_EMAIL = 'CONFIRM_EMAIL';
export const TEMPLATE_CONFIRM_EMAIL_EN = 'CONFIRM_EMAIL_EN';
export const TEMPLATE_HIBP_READY = 'HIBP_READY_TEMPLATE';
export const TEMPLATE_HIBP_READY_EN = 'HIBP_READY_TEMPLATE_EN';
export const TEMPLATE_IMPERSONATION_READY = 'IMPERSONATION_READY_TEMPLATE';
export const TEMPLATE_IMPERSONATION_READY_EN = 'IMPERSONATION_READY_TEMPLATE_EN';
export const TEMPLATE_NDS_READY = 'NDS_READY_TEMPLATE';
export const TEMPLATE_NDS_READY_EN = 'NDS_READY_TEMPLATE_EN';
export const TEMPLATE_RECON_READY = 'RECON_READY_TEMPLATE';
export const TEMPLATE_RECON_READY_EN = 'RECON_READY_TEMPLATE_EN';
export const TEMPLATE_PASSWORD_CONTACT_THANKS = 'PASSWORD_CONTACT_THANKS_TEMPLATE';
export const TEMPLATE_PASSWORD_CONTACT_THANKS_EN = 'PASSWORD_CONTACT_THANKS_TEMPLATE_EN';
export const TEMPLATE_SITE_RISK_CONTACT_THANKS = 'SITE_RISK_CONTACT_THANKS_TEMPLATE';
export const TEMPLATE_SITE_RISK_CONTACT_THANKS_EN = 'SITE_RISK_CONTACT_THANKS_TEMPLATE_EN';
export const TEMPLATE_NDS_CONTACT_THANKS = 'NDS_CONTACT_THANKS_TEMPLATE';
export const TEMPLATE_NDS_CONTACT_THANKS_EN = 'NDS_CONTACT_THANKS_TEMPLATE_EN';
export const TEMPLATE_CLOUD_CONTACT_THANKS = 'CLOUD_CONTACT_THANKS_TEMPLATE';
export const TEMPLATE_CLOUD_CONTACT_THANKS_EN = 'CLOUD_CONTACT_THANKS_TEMPLATE_EN';
export const TEMPLATE_SSL_CONTACT_THANKS = 'TEMPLATE_SSL_CONTACT_THANKS';
export const TEMPLATE_SSL_CONTACT_THANKS_EN = 'TEMPLATE_SSL_CONTACT_THANKS_EN';
export const TEMPLATE_IMPERSONATION_CONTACT_THANKS = 'IMPERSONATION_CONTACT_THANKS_TEMPLATE';
export const TEMPLATE_IMPERSONATION_CONTACT_THANKS_EN = 'IMPERSONATION_CONTACT_THANKS_TEMPLATE_EN';
export const TEMPLATE_BRAND_TLD_CONTACT_THANKS = 'BRAND_TLD_CONTACT_THANKS_TEMPLATE';
export const TEMPLATE_BRAND_TLD_CONTACT_THANKS_EN = 'BRAND_TLD_CONTACT_THANKS_TEMPLATE_EN';

export const TEMPLATE_REGULARLY_ANNOUNCE = 'REGULARLY_ANNOUNCE';

export const TEMPLATE_REGULARLY_HIBP_READY = 'REGULARLY_HIBP_READY_TEMPLATE';
export const TEMPLATE_REGULARLY_HIBP_READY_EN = 'REGULARLY_HIBP_READY_TEMPLATE_EN';
export const TEMPLATE_REGULARLY_IMPERSONATION_READY = 'REGULARLY_IMPERSONATION_READY_TEMPLATE';
export const TEMPLATE_REGULARLY_IMPERSONATION_READY_EN = 'REGULARLY_IMPERSONATION_READY_TEMPLATE_EN';
export const TEMPLATE_REGULARLY_NDS_READY = 'REGULARLY_NDS_READY_TEMPLATE';
export const TEMPLATE_REGULARLY_NDS_READY_EN = 'REGULARLY_NDS_READY_TEMPLATE_EN';
export const TEMPLATE_REGULARLY_RECON_READY = 'REGULARLY_RECON_READY_TEMPLATE';
export const TEMPLATE_REGULARLY_RECON_READY_EN = 'REGULARLY_RECON_READY_TEMPLATE_EN';

export const TEMPLATE_EMERGENCY_HIBP_READY = 'EMERGENCY_HIBP_READY_TEMPLATE';
export const TEMPLATE_EMERGENCY_HIBP_READY_EN = 'EMERGENCY_HIBP_READY_TEMPLATE_EN';
export const TEMPLATE_EMERGENCY_NDS_READY = 'EMERGENCY_NDS_READY_TEMPLATE';
export const TEMPLATE_EMERGENCY_NDS_READY_EN = 'EMERGENCY_NDS_READY_TEMPLATE_EN';

const footer = () => `
――――――――――――――――――――――――
GMO インターネットグループ株式会社（東証プライム 証券コード 9449）
URL https://www.gmo.jp/
――――――――――――――――――――――――
おかげさまで 29 周年 すべての人にインターネット
■GMO INTERNET GROUP■ https://www.gmo.jp/
-----------------------------------------------------------------
総合ネットセキュリティ・サービス
「GMO セキュリティ 24」
①パスワード漏洩診断
②Web リスク診断
③セキュリティ相談 AI
24 時間・すべて無料 https://www.gmo.jp/security/
〜すべての人に安心な未来を〜
――――――――――――――――――――――――
機密情報に関する注意事項：この E-mail は、発信者が意図した
受信者による閲覧・利用を目的としたものです。万一、貴殿が
意図された受信者でない場合には、直ちに送信者に連絡のうえ、
この E-mail を破棄願います。
`;

const footerEn = () => `
----------------------------------
GMO INTERNET GROUP, Inc. 
Group Public Relations Department

URL　　　https://www.gmo.jp/en/
----------------------------------
Thank you for 29 years 
Internet for Everyone
We are the member of GMO Internet Group (Prime: 9449).
■GMO INTERNET GROUP■
https://www.gmo.jp/en/　 
----------------------------------
Comprehensive Internet Security Services
“GMO Security 24”
1.Password leakage diagnosis
2.Web risk diagnosis
3.Security Consultation AI
24 hours a day, all free of charge  https://www.gmo.jp/security
〜A safer future for Everyone～
----------------------------------
NOTICE REGARDING CONFIDENTIAL INFORMATION:
This e-mail message is intended to be conveyed only to the designatedrecipient(s). 
If you are NOT the intended recipient(s) of this e-mail, 
please kindlynotify the sender immediately and delete the original message fromyour system.
`;

export const generateConfirmEmailContent = (expiredAt, fqdn, url) => `
このたびは、GMOセキュリティ24 ( https://www.gmo.jp/security/ ) をご利用いただき、誠にありがとうございます。

本メールはお客様が実施されました「サイトリスク診断」のセキュリティ確保のためのご本人確認となります。
※このメールに心当たりがない場合は、破棄していただきますようお願いいたします。

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
こちらのリンクをクリックすることで、本人確認および診断受付が完了となります。
${url}
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

今後もお客様にご満足いただけるサービス提供・改善に邁進してまいりますので、変わらぬご愛顧を賜りますようお願い申しあげます。

※本メールアドレスは送信専用となります。
※本メールアドレス宛への返信は受付できませんのでご了承ください。

${footer()}
`;

export const generateConfirmEmailContentEn = (expiredAt, fqdn, url) => `
Thank you for using GMO Security 24 (https://www.gmo.jp/security/). This email serves as identity verification for the "Site Risk Assessment" you requested to ensure security.
*If you did not request this service, please discard this email.
┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
Click this link to complete identity verification and assessment registration.
${url}&lang=en
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛
We will continue striving to provide and improve services that meet your satisfaction. We appreciate your continued patronage.
*This email address is for sending only.
*Please note that replies to this email address cannot be accepted.


${footerEn()}
`;

export const generateHibpContent = (createdAt, expiredAt, email, url) => `
このたびは、GMOセキュリティ24 ( https://www.gmo.jp/security/ ) をご利用いただき、誠にありがとうございます。

お客様が実施されました「パスワード漏洩診断」の診断結果をお知らせいたします。


ぜひ内容をご確認のうえ、セキュリティの安全性向上にお役立てください。

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
【診断結果】 ${url}
【診断対象】 ${email}
【診断日時】 ${createdAt}
※閲覧有効期限: ${expiredAt}まで
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

※自動で診断を実施する「定期診断機能」は、上記URLから設定変更が可能です。

${footer()}
`;

export const generateHibpContentEn = (createdAt, expiredAt, email, url) => `
Thank you for using GMO Security 24 (https://www.gmo.jp/security/). Here are the results of your "Password Leak Assessment." Please review the contents to improve your security.

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
【Assessment Results】
${url}&lang=en
【Target】
${email}
【Assessment Date】
${createdAt}
※Results available until: ${expiredAt}
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛
※The "Regular Assessment" feature can be configured from the URL above.

${footerEn()}
`;

export const generateNdsContent = (createdAt, expiredAt, fqdn, url) => `
このたびは、GMOセキュリティ24 ( https://www.gmo.jp/security/ ) をご利用いただき、誠にありがとうございます。

お客様が実施されました「サイトリスク診断」の診断結果をお知らせいたします。

この診断結果は、WEBサイトのリスクを可視化したものです。
ぜひ内容をご確認のうえ、セキュリティの安全性向上にお役立てください。

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
【診断結果】 ${url}
【診断対象】 ${fqdn}
【診断日時】 ${createdAt}
※閲覧有効期限: ${expiredAt}まで
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

※自動で診断を実施する「定期診断機能」は、上記URLから設定変更が可能です。

${footer()}
`;

export const generateNdsContentEn = (createdAt, expiredAt, fqdn, url) => `
"Thank you for using GMO Security 24 (https://www.gmo.jp/security/). Here are the results of your ""Site Risk Assessment."" This assessment visualizes potential risks to your website. Please review the contents to improve your security.

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
【Assessment Results】
${url}&lang=en
【Target】
${fqdn}
【Assessment Date】
${createdAt}
※Results available until: ${expiredAt}
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛
※The "Regular Assessment" feature can be configured from the URL above.

${footerEn()}
`;

export const generatePasswordContactThanksContent = (consultation, contactedAt) => `
このたびは、GMOセキュリティ24 ( https://www.gmo.jp/security/ ) をご利用いただき、誠にありがとうございます。

このたび、以下の内容でお問い合わせを受け付けました。​

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
【お問い合わせ日時】 ${contactedAt}
【お問い合わせ項目】 パスワード漏洩診断
【お問い合わせ内容】 ${consultation}
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

担当者より3営業日以内にご連絡いたしますので、今しばらくお待ちください。​
引き続き、「GMOセキュリティ24」をよろしくお願い申し上げます。​

${footer()}
`;

export const generatePasswordContactThanksContentEn = (consultation, contactedAt) => `
"Thank you for using GMO Security 24 (https://www.gmo.jp/security/). Your inquiry has been received with the following details:

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
【Inquiry Date】
${contactedAt}
【Inquiry Type】
Password Leak Assessment
【Inquiry Details】
${consultation}
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

A representative will contact you within 3 business days. Thank you for your patience.

We appreciate your continued use of "GMO Security 24."

${footerEn()}
`;

export const generateNdsContactThanksContent = () => `
このたびは、GMOセキュリティ24 ( https://www.gmo.jp/security/ ) にて、脆弱性対策をお申込みいただき、誠にありがとうございます。

お申込み内容につきまして、1営業日以内に、GMOグローバルサイン・ホールディングスのセキュリティ対策担当者よりご連絡させていただきます。

お急ぎのところお待たせし、大変恐縮でございますが、今しばらくのご猶予を賜りますよう、何卒よろしくお願い申し上げます。

${footer()}
`;

export const generateNdsContactThanksContentEn = () => `
"Thank you for subscribing to vulnerability countermeasures through GMO Security 24 (https://www.gmo.jp/security/).

A security specialist from GMO GlobalSign Holdings will contact you regarding your application within one business day.

We sincerely apologize for any delay and appreciate your patience.

${footerEn()}
`;

export const generateCloudContactThanksContent = () => `
このたびは、GMOセキュリティ24 ( https://www.gmo.jp/security/ ) にて、クラウドの中身も診断(無料お試し) をお申込みいただきありがとうございます。

お試し用のアカウント発行を可能な限り早急に、遅くとも1営業日以内にはご連絡させていただきます。

お急ぎのところお待たせし大変恐縮ではございますが今しばらくお待ちください。
何卒よろしくお願い申し上げます。

${footer()}
`;

export const generateCloudContactThanksContentEn = () => `
Thank you for applying for cloud content assessment (free trial) through GMO Security 24 ( https://www.gmo.jp/security/ ).

We will contact you regarding the issuance of a trial account as soon as possible, within 1 business day at the latest.

We apologize for keeping you waiting and ask you to wait a little longer.
Thank you for your understanding.

${footerEn()}
`;

export const generateSslContactThanksContent = () => `
このたびは、GMOセキュリティ24 ( https://www.gmo.jp/security/ ) にて、信頼性の高いSSL(無料お試し)のお申込みをいただきありがとうございます。

担当者より1営業日以内にサポートのお電話を差し上げます。

お急ぎのところお待たせし大変恐縮でございますが、心を込めて真摯に対応させていただきますので、今しばらくお待ちください。
何卒よろしくお願い申し上げます。

${footer()}
`;

export const generateSslContactThanksContentEn = () => `
Thank you for applying for the Reliable SSL (Free Trial) through GMO Security 24 (https://www.gmo.jp/security/).

A representative will call you within one business day to provide support.

We sincerely apologize for any delay and will address your needs with the utmost care and attention. Thank you for your patience.

${footerEn()}
`;

export const generateImpersonationContactThanksContent = () => `
このたびは、GMOセキュリティ24 ( https://www.gmo.jp/security/ ) にて、なりすまし対策の無料見積のお問い合わせをいただき、誠にありがとうございます。

GMOブランドセキュリティのなりすまし対策担当より可能な限り早急に、遅くとも1営業日以内にはご連絡させていただきます。

お急ぎのところお待たせし大変恐縮でございますが、心を込めて真摯に対応させていただきますので、今しばらくお待ちください。
何卒よろしくお願い申し上げます。

${footer()}
`;

export const generateImpersonationContactThanksContentEn = () => `
Thank you for your inquiry about a free quote for impersonation countermeasures through GMO Security 24 ( https://www.gmo.jp/security/ ).

An impersonation countermeasure representative from GMO Brand Security will contact you as soon as possible, within 1 business day at the latest.

We apologize for keeping you waiting, but we will respond sincerely with all our heart, so please wait a little longer.
Thank you for your understanding.

${footerEn()}
`;

export const generateBrandTldContactThanksContent = fullname => `
「GMO『.貴社名』申請・運用支援サービス」をご利用いただき、誠にありがとうございます。
この度、お問い合わせを受け付けいたしました。

担当者より1営業日以内にご連絡いたしますので、今しばらくお待ちください。
引き続きよろしくお願い申し上げます。

${footer()}
`;

export const generateBrandTldContactThanksContentEn = fullname => `
Thank you for using the "GMO '.YourCompanyName' Application and Operation Support Service".
We have received your inquiry.

A representative will contact you within 1 business day, so please wait a moment.
Thank you for your continued support.

${footerEn()}
`;

export const generateSiteRiskContactThanksContent = (targets, consultation, contactedAt) => `
このたびは、GMOセキュリティ24 ( https://www.gmo.jp/security/ ) をご利用いただき、誠にありがとうございます。

このたび、以下の内容でお問い合わせを受け付けました。​

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
【お問い合わせ日時】 ${contactedAt}
【お問い合わせ項目】 ${targets.join('・')}
【お問い合わせ内容】 ${consultation}
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

担当者より3営業日以内にご連絡いたしますので、今しばらくお待ちください。​
引き続き、「GMOセキュリティ24」をよろしくお願い申し上げます。​

${footer()}
`;

export const generateAnnounceRegularly = (password, siteRisks) => `
先日はGMOセキュリティ24 (https://www.gmo.jp/security/) をご利用いただきありがとうございました。
この度、無料でご利用いただける「定期診断機能」を新規にリリースいたしましたのでご案内いたします。

「定期診断機能」は先日ご利用いただいたセキュリティ診断を自動で定期的に診断実行する機能です。
セキュリティリスクの早期発見・対策につながる機能ですので、是非ご利用ください。

 ---＼【設定推奨】以下URLからワンクリックで設定可能／--------
${password ? `【パスワード漏洩診断】【${password.email}】${password.url}` : ''}
${siteRisks && siteRisks.length > 0 ? `【Webサイトリスク診断】${siteRisks.map(({ fqdn, url }) => `【${fqdn}】${url}`).join('\n')}` : ''}

 -----------------------------------------------------------------------------

■ 定期診断を推奨する理由について
─────────────────

2024年には「1日あたり100件以上※1」の新しい脆弱性が発表されており、
日々何もしていなくても診断結果は悪化し、サイバー攻撃被害のリスクが高まる可能性がございます。

また、最近のサイバー攻撃の被害は大手企業のみにとどまらず、個人や中小企業までに及びます。

このような被害から身を守るために、定期的な診断によるセキュリティリスクの早期発見が必要となります。
「定期診断」は、人間における健康診断のような役割を果たします。

※1：https://www.cvedetails.com/browse-by-date.php

■ 定期診断の頻度について
─────────────────

上記URLより、「1ヶ月毎・3ヶ月毎・6ヶ月毎」の頻度が選択できます。
GMOセキュリティ24では、「月に1回」の定期診断を推奨しております。

※定期診断機能をOFFにすることで停止することが可能です。

本機能がセキュリティ対策に貢献できることを願っております。
今後ともどうぞよろしくお願い申し上げます。

【注意事項】
※一時的に診断レポートの閲覧期限を延長しております。2025/4/1に通常の期限へ戻します。

${footer()}
`;

export const generateRegularlyHibpContent = (createdAt, expiredAt, email, url) => `
GMOセキュリティ24 ( https://www.gmo.jp/security/ ) をご利用いただきありがとうございます。

「パスワード漏洩診断」の「定期診断」が実施されましたため、診断結果をお知らせいたします。※1
ぜひ内容をご確認のうえ、セキュリティの安全性向上にお役立てください。

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
【診断結果】 ${url}
【診断対象】 ${email}
【診断日時】 ${createdAt}
※閲覧有効期限: ${expiredAt}まで
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

※1 定期診断は、定期的に自動で診断を実施する機能で、過去の診断時に設定された頻度で診断が行われます。
　　設定は上記URLから変更可能です。

${footer()}
`;

export const generateRegularlyNdsContent = (createdAt, expiredAt, fqdn, url) => `
GMOセキュリティ24 ( https://www.gmo.jp/security/ ) をご利用いただきありがとうございます。

「サイトリスク診断」の「定期診断」が実施されましたため、診断結果をお知らせいたします。※1

この診断結果は、WEBサイトのリスクを可視化したものです。
ぜひ内容をご確認のうえ、セキュリティの安全性向上にお役立てください。

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
【診断結果】 ${url}
【診断対象】 ${fqdn}
【診断日時】 ${createdAt}
※閲覧有効期限: ${expiredAt}まで
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

※1 定期診断は、定期的に自動で診断を実施する機能で、過去の診断時に設定された頻度で診断が行われます。
　　設定は上記URLから変更可能です。

${footer()}
`;

export const generateEmergencyHibpContent = ({ createdAt, expiredAt, email, url, title }) => `
GMOセキュリティ24 ( https://www.gmo.jp/security/ ) をご利用いただきありがとうございます。
本メールは過去診断時にパスワード漏洩診断の「定期診断機能」の設定をONに設定いただいたお客様に送信しております。

日本にお住まいのユーザー様にとって影響の大きい大規模なパスワード漏洩（DB更新）を検知したため、
再診断を実施したところ、【${email}】 に紐づく新たなパスワード漏洩を確認いたしました。※1

大変危険な状況のため、早期のパスワード変更をお勧めいたします。

【詳細はこちらから】
${url}

※1 本パスワード漏洩情報には、最近発生したものだけでなく、過去の事案も含まれておりますことをご了承ください。

${footer()}
`;

export const generateEmergencyyNdsContentToUnsafetyFqdn = ({ createdAt, expiredAt, fqdn, url, cve, description, impact, measure, cveUrl }) => `
GMOセキュリティ24 ( https://www.gmo.jp/security/ ) をご利用いただきありがとうございます。
​
本メールは過去診断時にWebサイトリスク診断の「定期診断機能」の設定を​
ONに設定いただいたお客様に送信しております。​

以下、危険度の高い「新規脆弱性 ※1」が発表されたため、再診断を実施した結果​
「${fqdn}」に影響する脆弱性であることが判明いたしました。​
非常に危険な状態のため、詳細情報の確認の上、早期対策をお勧めいたします。​

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
【診断結果】 ${url}
【診断対象】 ${fqdn}
【診断日時】 ${createdAt}
※閲覧有効期限: ${expiredAt}まで
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

【新規脆弱性について】​
CVE(脆弱性の識別番号): ${cve}
CVSS(危険度スコア): 10.0​
脆弱性の概要: ${description}
影響: ${impact}
対策方法： ${measure}

詳細はこちら
${cveUrl}

※1 脆弱性の危険度の判断は、GMOインターネットグループのセキュリティ分野の専門家にて独自で判断しております。

${footer()}
`;

export const generateEmergencyyNdsContentToSafetyFqdn = ({ fqdn, cve, description, impact, measure, cveUrl }) => `
GMOセキュリティ24 ( https://www.gmo.jp/security/ ) をご利用いただきありがとうございます。
​
本メールは過去診断時にWebサイトリスク診断の「定期診断機能」の設定を​
ONに設定いただいたお客様に送信しております。​

以下、危険度の高い「新規脆弱性 ※1」が発表されたため、再診断を実施しましたが、​
「${fqdn}」には影響が無かったことをお知らせいたします。

【新規脆弱性について】​
CVE(脆弱性の識別番号): ${cve}
CVSS(危険度スコア): 10.0​
脆弱性の概要: ${description}
影響: ${impact}
対策方法： ${measure}

詳細はこちら
${cveUrl}

※1 脆弱性の危険度の判断は、GMOインターネットグループのセキュリティ分野の専門家にて独自で判断しております。

${footer()}
`;
