import { isVerified } from '../services/sendgrid.js';
import { createSendgridEventSink } from '../services/sink.js';

const schema = { body: { type: 'array' } };

const handler = async (request, reply) => {
  const signature = request.headers['x-twilio-email-event-webhook-signature'];
  const timestamp = request.headers['x-twilio-email-event-webhook-timestamp'];

  if (!isVerified({ key: process.env.SECRET_SENDGRID_PUBLIC_KEY, body: request.raw.rawBody, signature, timestamp })) {
    console.error(`Unverified sendgrid signature: ${signature}, timestamp: ${timestamp}`);
    return reply.status(403).send('unverified');
  }

  const body = request.body;
  await Promise.all(body
    .filter(({ env }) => env === process.env.ENV)
    .filter(({ event }) => event === 'bounce' || event === 'delivered' || event === 'open')
    .map(({ email, event, timestamp, sg_message_id: sgMessageId, type, bounce_classification: bounceClassification, template, fqdn, code }) => {
      const sentAt = new Date(timestamp * 1000);
      return createSendgridEventSink({ email, event, sentAt, sgMessageId, type, bounceClassification, template, fqdn, code });
    }));

  reply.send('OK');
};

export default { schema, handler };
