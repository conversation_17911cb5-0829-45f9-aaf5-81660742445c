import { FEEDBACK_REACTION, FEEDBACK_THREAD_REACTION } from '../constants/feedback.js';
import { SINK_POST_FEEDBACK } from '../constants/sinks.js';
import { isVerified } from '../services/recaptcha.js';
import { createPostFeedbackSink } from '../services/sink.js';
import { addOrRemoveReaction, sendMessage as sendSlackMessage, getTargetMessage } from '../services/slack.js';

const schema = {
  body: {
    type: 'object',
    required: ['threadTs', 'replyNumber', 'feedback', 'recaptchaToken'],
    properties: {
      threadTs: { type: 'string' },
      replyNumber: { type: 'number' },
      feedback: {
        type: 'string',
        nullable: true,
      },
      recaptchaToken: { type: 'string' },
    },
  },
};

const handler = async (request, reply) => {
  const { threadTs, replyNumber, feedback, recaptchaToken } = request.body;
  if (!(await isVerified(recaptchaToken))) {
    return reply.status(400).send({ status: 'error', message: 'Invalid reCAPTCHA token' });
  }

  createPostFeedbackSink(threadTs, replyNumber, feedback);

  const targetMessage = await getTargetMessage({
    token: process.env.SECRET_SLACK_USER_TOKEN,
    channelId: process.env.SLACK_CHAT_CHANNEL_ID,
    threadTs,
    replyNumber,
  });
  if (!targetMessage) {
    throw new Error('Target message not found');
  }
  const parentThread = await getTargetMessage({
    token: process.env.SECRET_SLACK_USER_TOKEN,
    channelId: process.env.SLACK_CHAT_CHANNEL_ID,
    threadTs,
    replyNumber: null,
  });
  if (!parentThread) {
    throw new Error('Parent thread not found');
  }

  await addOrRemoveReaction({
    token: process.env.SECRET_SLACK_USER_TOKEN,
    channelId: process.env.SLACK_CHAT_CHANNEL_ID,
    targetMessage,
    reaction: FEEDBACK_REACTION[feedback],
    toggle: true,
  });
  await addOrRemoveReaction({
    token: process.env.SECRET_SLACK_USER_TOKEN,
    channelId: process.env.SLACK_CHAT_CHANNEL_ID,
    targetMessage: parentThread,
    reaction: FEEDBACK_THREAD_REACTION,
    toggle: false,
  });

  const channelId = process.env.SLACK_CHAT_CHANNEL_ID;
  const threadUrl = `https://gmointernetworkspace.slack.com/archives/${channelId}/p${targetMessage['ts'].replace('.', '')}??thread_ts=${threadTs}&cid=${channelId}`;
  const notificationPost = `${feedback === 'good' ? 'いいね:thumbsup:' : 'わるいね:thumbsdown:'
  } が押されました。\n${threadUrl}`;
  await sendSlackMessage(
    process.env.SECRET_SLACK_USER_TOKEN,
    channelId,
    notificationPost,
    null,
  );

  return reply.send({ status: 'success' });
};

export default { schema, handler };
