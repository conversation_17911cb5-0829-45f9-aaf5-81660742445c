import { isCompanyEmail } from 'free-email-domains-list';
import { COLLECTION_HIBP, COLLECTION_REGULARLY_PASSWORD_CONFIGURATION } from '../constants/collections.js';
import { TTL_MILLISECOND } from '../constants/constants.js';
import { getDoc } from '../providers/firestore.js';
import { decrypt, hashEmail } from '../services/cryptography.js';
import { convertUTCtoJST } from '../services/daytime.js';

const schema = {
  querystring: {
    type: 'object',
    required: ['code'],
    properties: {
      code: {
        type: 'string',
        pattern: '^[a-z0-9-]+$',
      },
    },
  },
};

const handler = async (request, reply) => {
  const { code } = request.query;

  const decrypted = await decrypt(code, process.env.SECRET_CRYPTOGRAPHY_PASSWORD, process.env.SECRET_CRYPTOGRAPHY_SALT);
  if (!decrypted) {
    return reply.status(400).send({ status: 'error', message: 'Invalid code' });
  }

  const { email, expiredAt } = JSON.parse(decrypted);
  if (!email || !expiredAt) {
    return reply.status(400).send({ status: 'error', message: 'Invalid code' });
  }

  const hashedEmail = await hashEmail(email);
  const [hibpDoc, passwordDoc] = await Promise.all([
    getDoc({ collection: COLLECTION_HIBP, docId: code }),
    getDoc({ collection: COLLECTION_REGULARLY_PASSWORD_CONFIGURATION, docId: hashedEmail }),
  ]);

  if (!passwordDoc.exists) {
    return reply.status(404).send({ status: 'error', message: 'Not found' });
  }

  const { isRegularly, interval, nextCheckedAt, history } = passwordDoc.data();
  const configuration = { isRegularly, interval, nextCheckedAt: nextCheckedAt ? convertUTCtoJST(nextCheckedAt) : null, isNotification: false };

  const now = new Date();
  const isCompany = isCompanyEmail(email);
  if (expiredAt < now.getTime() || !hibpDoc.exists) {
    const createdAt = new Date((new Date(expiredAt)).getTime() - TTL_MILLISECOND);
    const result = history?.find(h => code === h.code);
    return reply.send({
      status: 'success',
      message: null,
      result: { configuration, summary: { email, createdAt: convertUTCtoJST(createdAt), isCompany }, count: result?.count || 0 },
    });
  }

  const hibpData = hibpDoc.data();
  const details = hibpData.result
    .filter(r => r.DataClasses.includes('Passwords'))
    .map(r => ({ title: r.Title, classes: r.DataClasses, breachDate: r.BreachDate }));

  return reply.send({
    status: 'success',
    message: null,
    result: { configuration, summary: { email, createdAt: convertUTCtoJST(hibpData.createdAt), isCompany }, details },
  });
};

export default { schema, handler };
