import Ajv from 'ajv';
import { createBrandTldSearchSink } from '../services/sink.js';
import { request as tldRequest } from '../services/tld.js';

const schema = {
  querystring: {
    type: 'object',
    required: ['name'],
    properties: {
      name: {
        type: 'string',
        minBytes: 3,
      },
    },
  },
};

const handler = async (request, reply) => {
  const { name } = request.query;

  try {
    const result = await tldRequest(name);
    const available = !result.data?.is_valid;
    await createBrandTldSearchSink({ tld: name, available });

    return reply.send({ status: 'success', result: { available } });
  } catch (error) {
    console.error('Error calling TLD API:', error);
    return reply.status(500).send({ status: 'error', message: 'Internal Server Error' });
  }
};

export default { schema, handler };
