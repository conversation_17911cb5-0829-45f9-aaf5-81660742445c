import functions from '@google-cloud/functions-framework';
import {
  FUNCTIONS_DEAD_LETTER_SUBSCRIBER,
  FUNCTIONS_EMAIL_SUBSCRIBER,
  FUNCTIONS_HIBP_SUBSCRIBER,
  FUNCTIONS_IMPERSONATION_SUBSCRIBER,
  FUNCTIONS_NDS_SUBSCRIBER,
  FUNCTIONS_SERVER,
  FUNCTIONS_RECON_SUBSCRIBER,
  FUNCTIONS_PROMPT_SUBSCRIBER,
  FUNCTIONS_SLACK_MESSAGE_SUBSCRIBER,
  FUNCTIONS_STATIC_SUBSCRIBER,
  FUNCTIONS_CHECK_EMAIL_RESERVATION_SUBSCRIBER,
  FUNCTIONS_CHECK_FQDN_RESERVATION_SUBSCRIBER,
  FUNCTIONS_PASSWORD_HISTORY_SUBSCRIBER,
  FUNCTIONS_PASSWORD_REGULARLY_SUBSCRIBER,
  FUNCTIONS_SITE_RISK_HISTORY_SUBSCRIBER,
  FUNCTIONS_SITE_RISK_REGULARLY_SUBSCRIBER,
  FUNCTIONS_WATCH_HIBP_API,
  FUNCTIONS_BULK_CHECK_API,
  FUNCTIONS_PRE_CHECK_EMAIL_SUBSCRIBER,
  FUNCTIONS_PRE_CHECK_FQDN_SUBSCRIBER,
  FUNCTIONS_SPREADSHEET_SUBSCRIBER,
  FUNCTIONS_FETCH_USD_RATE_API,
} from './constants/functions.js';
import bulkCheckApi from './functions/bulk_check_api.js';
import checkEmailReservationSubscriber from './functions/check_email_reservation_subscriber.js';
import checkFqdnReservationSubscriber from './functions/check_fqdn_reservation_subscriber.js';
import deadLetterSubscriber from './functions/dead_letter_subscriber.js';
import emailSubscriber from './functions/email_subscriber.js';
import fetchUsdRateApi from './functions/fetch_usd_rate_api.js';
import hibpSubscriber from './functions/hibp_subscriber.js';
import impersonationSubscriber from './functions/impersonation_subscriber.js';
import ndsSubscriber from './functions/nds_subscriber.js';
import passwordHistorySubscriber from './functions/password_history_subscriber.js';
import passwordRegularlySubscriber from './functions/password_regularly_subscriber.js';
import preCheckEmailSubscriber from './functions/pre_check_email_subscriber.js';
import preCheckFqdnSubscriber from './functions/pre_check_fqdn_subscriber.js';
import promptSubcriber from './functions/prompt_subcriber.js';
import reconSubscriber from './functions/recon_subscriber.js';
import server from './functions/server.js';
import siteRiskHistorySubscriber from './functions/site_risk_history_subscriber.js';
import siteRiskRegularlySubscriber from './functions/site_risk_regularly_subscriber.js';
import slackMessageSubscriber from './functions/slack_message_subscriber.js';
import speedsheetSubscriber from './functions/spreadsheet_subscriber.js';
import staticSubscriber from './functions/static_subscriber.js';
import watchHibpApi from './functions/watch_hibp_api.js';
import subscriberWrapper from './providers/subscriber_wrapper.js';

functions.cloudEvent(FUNCTIONS_CHECK_EMAIL_RESERVATION_SUBSCRIBER, subscriberWrapper(checkEmailReservationSubscriber));
functions.cloudEvent(FUNCTIONS_CHECK_FQDN_RESERVATION_SUBSCRIBER, subscriberWrapper(checkFqdnReservationSubscriber));
functions.cloudEvent(FUNCTIONS_DEAD_LETTER_SUBSCRIBER, subscriberWrapper(deadLetterSubscriber));
functions.cloudEvent(FUNCTIONS_EMAIL_SUBSCRIBER, subscriberWrapper(emailSubscriber));
functions.cloudEvent(FUNCTIONS_HIBP_SUBSCRIBER, subscriberWrapper(hibpSubscriber));
functions.cloudEvent(
  FUNCTIONS_IMPERSONATION_SUBSCRIBER,
  subscriberWrapper(impersonationSubscriber),
);
functions.cloudEvent(FUNCTIONS_NDS_SUBSCRIBER, subscriberWrapper(ndsSubscriber));
functions.cloudEvent(FUNCTIONS_PASSWORD_HISTORY_SUBSCRIBER, subscriberWrapper(passwordHistorySubscriber));
functions.cloudEvent(FUNCTIONS_PASSWORD_REGULARLY_SUBSCRIBER, subscriberWrapper(passwordRegularlySubscriber));
functions.cloudEvent(FUNCTIONS_PRE_CHECK_EMAIL_SUBSCRIBER, subscriberWrapper(preCheckEmailSubscriber));
functions.cloudEvent(FUNCTIONS_PRE_CHECK_FQDN_SUBSCRIBER, subscriberWrapper(preCheckFqdnSubscriber));
functions.cloudEvent(FUNCTIONS_PROMPT_SUBSCRIBER, subscriberWrapper(promptSubcriber));
functions.cloudEvent(FUNCTIONS_RECON_SUBSCRIBER, subscriberWrapper(reconSubscriber));
functions.cloudEvent(FUNCTIONS_SITE_RISK_HISTORY_SUBSCRIBER, subscriberWrapper(siteRiskHistorySubscriber));
functions.cloudEvent(FUNCTIONS_SITE_RISK_REGULARLY_SUBSCRIBER, subscriberWrapper(siteRiskRegularlySubscriber));
functions.cloudEvent(FUNCTIONS_SLACK_MESSAGE_SUBSCRIBER, subscriberWrapper(slackMessageSubscriber));
functions.cloudEvent(FUNCTIONS_STATIC_SUBSCRIBER, subscriberWrapper(staticSubscriber));
functions.cloudEvent(FUNCTIONS_SPREADSHEET_SUBSCRIBER, subscriberWrapper(speedsheetSubscriber));

functions.http(FUNCTIONS_SERVER, server);
functions.http(FUNCTIONS_BULK_CHECK_API, bulkCheckApi);
functions.http(FUNCTIONS_WATCH_HIBP_API, watchHibpApi);
functions.http(FUNCTIONS_FETCH_USD_RATE_API, fetchUsdRateApi);

export default functions;
