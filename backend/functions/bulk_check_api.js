import { PubSub } from '@google-cloud/pubsub';
import { COLLECTION_REGULARLY_PASSWORD_CONFIGURATION, COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION } from '../constants/collections.js';
import { BULK_CHECK_EMAIL } from '../constants/constants.js';
import { TOPIC_PRE_CHECK_EMAIL, TOPIC_PRE_CHECK_FQDN } from '../constants/topics.js';
import { getDoc, getDocsByEncryptedEmail, getDocsByNextCheckedAt } from '../providers/firestore.js';

const pubSubClient = new PubSub({ projectId: process.env.PROJECT_ID });

const api = async (req, res) => {
  try {
    const { collection, isRegularly = false, cve = null, name = null, preview = true, emailPrefixFrom, emailPrefixTo } = req.body;
    if (!collection) {
      throw new Error('collection is required');
    }
    if (![COLLECTION_REGULARLY_PASSWORD_CONFIGURATION, COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION].includes(collection)) {
      throw new Error(`collection is ${COLLECTION_REGULARLY_PASSWORD_CONFIGURATION} or ${COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION}`);
    }
    if (!isRegularly && !(!!cve || !!name)) {
      throw new Error('isRegularly = true or cve/name is required');
    }

    let reservationDocs;
    if (preview) {
      if (collection === COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION) {
        reservationDocs = [await getDoc({ collection, docId: process.env.PREVIEW_SITE_RISK_CONFIGURATION_ID })];
      } else {
        reservationDocs = [await getDoc({ collection, docId: process.env.PREVIEW_PASSWORD_CONFIGURATION_ID })];
      }
    } else {
      if (isRegularly) {
        const to = new Date();
        reservationDocs = await getDocsByNextCheckedAt({ collection, to: to.toISOString(), limit: BULK_CHECK_EMAIL });

        if (reservationDocs.length > BULK_CHECK_EMAIL) {
          res.send('OVER LIMIT');
          return;
        }
      } else {
        reservationDocs = await getDocsByEncryptedEmail({ collection, emailPrefixFrom, emailPrefixTo });
      }
    }

    let topic = pubSubClient.topic(TOPIC_PRE_CHECK_EMAIL);
    if (collection === COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION) {
      topic = pubSubClient.topic(TOPIC_PRE_CHECK_FQDN);
    }

    for (const doc of reservationDocs) {
      const { encryptedEmail, isRegularly: docIsRegularly, interval, ...rest } = doc.data();
      if (!docIsRegularly) {
        if (isRegularly) { // emailPrefix で取得した際は、doc.isRegularly = false のデータは通常ケースのため
          console.error(new Error(`Configured isRegularly = false id: ${doc.id}`));
        }
        continue;
      }

      if (isRegularly && (!name && !cve)) {
        const nextCheckedAt = new Date();
        nextCheckedAt.setMonth(nextCheckedAt.getMonth() + interval);
        doc.ref.update({ nextCheckedAt: nextCheckedAt.toISOString() });
      }

      const data = Buffer.from(JSON.stringify({ encryptedEmail, isRegularly, ...rest, cve, name }));
      await topic.publishMessage({ data });
    }

    if (preview) {
      res.send('PREVIEW OK');
    } else {
      res.send('OK');
    }
  } catch (err) {
    console.error(err);
    res.status(500).send('ERROR');
  }
};

export default api;
