import { request } from '../services/spreadsheet.js';

const subscriber = async ({ data }) => {
  const { docId, sheetId, records } = JSON.parse(Buffer.from(data.message.data, 'base64').toString());

  if (!docId) {
    console.error(new Error('docId is required'));
    return;
  }

  if (!sheetId) {
    console.error(new Error('sheetId is required'));
    return;
  }

  if (!records) {
    console.error(new Error('records is required'));
    return;
  }

  await request({ docId, sheetId, records });
};

export default subscriber;
