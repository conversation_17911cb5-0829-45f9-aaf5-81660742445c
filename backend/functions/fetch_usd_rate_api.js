import { Storage } from '@google-cloud/storage';
import axios from 'axios';

const storage = new Storage({ projectId: process.env.PROJECT_ID });

const api = async (_, res) => {
  try {
    const now = new Date();
    const fetchedAt = now.toISOString();

    const response = await axios.get('http://api.exchangeratesapi.io/v1/latest', {
      params: {
        access_key: process.env.SECRET_EXCHANGE_RATES_API_KEY,
        base: 'EUR',
        symbols: 'USD,JPY',
      },
    });

    if (!response.data || !response.data.rates || !response.data.rates.JPY || !response.data.rates.USD) {
      throw new Error('Failed to get EUR/JPY and EUR/USD rates from exchangeratesapi.io');
    }

    const jpyEurRate = response.data.rates.JPY;
    const usdEurRate = response.data.rates.USD;
    const rate = jpyEurRate / usdEurRate; // JPY/USDレートを計算

    const jsonData = {
      fetched_at: fetchedAt,
      rate: parseFloat(rate.toFixed(2)),
    };

    const jsonContent = JSON.stringify(jsonData, null, 2);

    const bucketName = process.env.STATIC_FILE_BUCKET_NAME;
    const file = storage.bucket(bucketName).file('static/json/usd_rate.json');

    await file.save(jsonContent, { contentType: 'application/json' });

    res.send('OK');
  } catch (err) {
    console.error(err);
    res.status(500).send('ERROR');
  }
};

export default api;
