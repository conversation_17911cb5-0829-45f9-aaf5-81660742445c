import { COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION, COLLECTION_USER_CONFIGURATION } from '../constants/collections.js';
import { getDoc, saveDoc } from '../providers/firestore.js';
import { hash, hashEmail, pubEncrypt } from '../services/cryptography.js';
import { createSiteRiskConfigurationSink } from '../services/sink.js';

const subscriber = async ({ data }) => {
  const { email, fqdn, createdAt, code, isRegularly = false, cve, lang = 'ja' } = JSON.parse(Buffer.from(data.message.data, 'base64').toString());

  if (isRegularly || !!cve) return;

  if (!email || !fqdn || !createdAt || !code) {
    console.error(new Error('email, fqdn, createdAt and code are required'));
    return;
  }

  const encryptedEmail = await pubEncrypt(email, process.env.SECRET_PUBLIC_KEY);
  const hashedEmail = await hashEmail(email);

  const configId = hashedEmail;
  const configDoc = await getDoc({ collection: COLLECTION_USER_CONFIGURATION, docId: configId });
  if (!configDoc.exists) {
    const config = { encryptedEmail, isSiteRiskNotification: false, isPasswordNotification: false };
    await saveDoc({ collection: COLLECTION_USER_CONFIGURATION, docId: configId, data: config });
  }

  const siteRiskId = await hash(`${email}:${fqdn}`);
  const siteRiskDoc = await getDoc({ collection: COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION, docId: siteRiskId });

  const nextCheckedAt = new Date(createdAt);

  if (siteRiskDoc.exists) {
    const { interval } = siteRiskDoc.data();

    // site_risk_history_subscriber との順番が保証できないため
    if (interval === undefined) {
      nextCheckedAt.setMonth(nextCheckedAt.getMonth() + 1);

      const siteRisk = { encryptedEmail, fqdn, isRegularly: true, interval: 1, nextCheckedAt: nextCheckedAt.toISOString() };
      await saveDoc({ collection: COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION, docId: siteRiskId, data: siteRisk });

      await createSiteRiskConfigurationSink(email, fqdn, true, 1, nextCheckedAt);
    }
  } else {
    nextCheckedAt.setMonth(nextCheckedAt.getMonth() + 1);

    const siteRisk = { encryptedEmail, fqdn, isRegularly: true, interval: 1, nextCheckedAt: nextCheckedAt.toISOString() };
    await saveDoc({ collection: COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION, docId: siteRiskId, data: siteRisk });

    await createSiteRiskConfigurationSink(email, fqdn, true, 1, nextCheckedAt);
  }
};

export default subscriber;
