import { generateWatchEmptyHibpMessage, generateWatchHibpMessage } from '../constants/slack_template.js';
import { getBreaches } from '../services/hibp.js';
import { sendMessage, getLastSlackMessage } from '../services/slack.js';

const DEFAULT_DATE = '2025-04-06T00:00:00Z';

const api = async (_, res) => {
  try {
    const lastMessage = await getLastSlackMessage({
      token: process.env.SECRET_SLACK_AI_TOKEN,
      channelId: process.env.SLACK_HIBP_CHANNEL_ID,
      userId: process.env.SLACK_AI_UID,
    });
    const lastPostTime = lastMessage ? new Date(lastMessage.ts * 1000) : new Date(DEFAULT_DATE);

    const breaches = await getBreaches();
    const recentBreaches = getRecentPasswordBreaches(breaches, lastPostTime);

    if (recentBreaches.length > 0) {
      await Promise.all(recentBreaches.map(async ({ Title: title, PwnCount: count }) => {
        const message = generateWatchHibpMessage({ title, count });
        await sendMessage(process.env.SECRET_SLACK_AI_TOKEN, process.env.SLACK_HIBP_CHANNEL_ID, message);
      }));
    } else {
      const message = generateWatchEmptyHibpMessage();
      await sendMessage(process.env.SECRET_SLACK_AI_TOKEN, process.env.SLACK_HIBP_CHANNEL_ID, message);
    }

    res.send('OK');
  } catch (error) {
    console.error(error);
    res.status(500).send('ERROR');
  }
};

const getRecentPasswordBreaches = (breaches, minModifiedDate) => {
  return breaches.filter((breach) => {
    const breachModifiedTime = new Date(breach.ModifiedDate);
    return breachModifiedTime.getTime() > minModifiedDate.getTime() && breach.DataClasses.includes('Passwords');
  });
};

export default api;
