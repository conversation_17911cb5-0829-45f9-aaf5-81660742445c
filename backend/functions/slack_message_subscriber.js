import { THIRTY_DAYS_MILLISECOND } from '../constants/constants.js';
import { PATTERN_CLOUD, PATTERN_IMPERSONATION, PATTERN_NDS, PATTERN_SSL } from '../constants/patterns.js';
import {
  generateBrandTldContactMessage,
  generateCloudResultMessage,
  generateImpersonationResultMessage,
  generateNdsResultMessage,
  generatePasswordContactMessage,
  generateSiteRiskContactMessage,
  generateSslResultMessage,
  SLACK_REPLACEMENT_TAMPLATE_CLOUD,
  SLACK_REPLACEMENT_TAMPLATE_IMPERSONATION,
  SLACK_REPLACEMENT_TAMPLATE_NDS,
  SLACK_REPLACEMENT_TAMPLATE_SSL,
  SLACK_TEMPLATE_BRAND_TLD_CONTACT,
  SLACK_TEMPLATE_CLOUD_RESULT,
  SLACK_TEMPLATE_IMPERSONATION_RESULT,
  SLACK_TEMPLATE_NDS_RESULT,
  SLACK_TEMPLATE_PASSWORD_CONTACT,
  SLACK_TEMPLATE_SITE_RISK_CONTACT,
  SLACK_TEMPLATE_SSL_RESULT,
} from '../constants/slack_template.js';
import { convertUTCtoJST } from '../services/daytime.js';
import { addReaction, replaceMessage, sendMessage } from '../services/slack.js';

const subscriber = async ({ data }) => {
  const {
    token,
    channelId,
    message,
    threadTs,
    updateOptions,
    replyBroadcast = false,
    reactions,
  } = JSON.parse(Buffer.from(data.message.data, 'base64').toString());

  if (!token || !channelId) {
    console.error(new Error('token and channelId are required'));
    return;
  }

  if (message && (message.template || (message.templates && message.templates.length > 0))) {
    const templates = message.templates && message.templates.length > 0
      ? message.templates
      : [message.template];

    const textParts = templates.map((template) => {
      switch (template) {
        case SLACK_TEMPLATE_CLOUD_RESULT: {
          const { status, providers } = message.params;
          if (!status) {
            console.error(new Error('params is not defined'));
            return null;
          }
          return generateCloudResultMessage(status, providers);
        }
        case SLACK_TEMPLATE_IMPERSONATION_RESULT: {
          const { status, rank, count, bimi, brand_tld: brandTld, vmc, spf, dmarc } = message.params;
          if (!status) {
            console.error(new Error('params is not defined'));
            return null;
          }
          return generateImpersonationResultMessage({ status, rank, count, bimi, brandTld, vmc, spf, dmarc });
        }
        case SLACK_TEMPLATE_NDS_RESULT: {
          const { scan_id: scanId, status, rank, critical, high, medium, low, info, titles } = message.params;
          if (!scanId || !status || !titles) {
            console.error(new Error('params is not defined'));
            return null;
          }
          return generateNdsResultMessage({ scanId, status, rank, critical, high, medium, low, info, titles });
        }
        case SLACK_TEMPLATE_SSL_RESULT: {
          const { status, hostname_verification, cert_verification, free_ssl_provider, earliest_expires } = message.params;
          if (!status) {
            console.error(new Error('params is not defined'));
            return null;
          }
          const earliestExpiresJST = earliest_expires ? convertUTCtoJST(earliest_expires) : null;
          return generateSslResultMessage(status, hostname_verification, cert_verification, free_ssl_provider, earliestExpiresJST);
        }
        case SLACK_TEMPLATE_PASSWORD_CONTACT: {
          const { code, email, fullname, telephone, contactedAt, consultation } = message.params;
          if (!code || !email || !contactedAt) {
            console.error(new Error('params is not defined'));
            return null;
          }
          const contactedAtJST = convertUTCtoJST(contactedAt);
          return generatePasswordContactMessage({ code, email, fullname, telephone, consultation, contactedAt: contactedAtJST });
        }
        case SLACK_TEMPLATE_SITE_RISK_CONTACT: {
          const { fqdn, code, email, fullname, telephone, targets, contactedAt, consultation } = message.params;
          if (!fqdn || !code || !email || !contactedAt) {
            console.error(new Error('params is not defined'));
            return null;
          }
          const contactedAtJST = convertUTCtoJST(contactedAt);
          return generateSiteRiskContactMessage({ fqdn, code, email, fullname, telephone, targets, consultation, contactedAt: contactedAtJST });
        }
        case SLACK_TEMPLATE_BRAND_TLD_CONTACT: {
          const { email, fullname, telephone, company, contactedAt, tld } = message.params;
          if (!email || !fullname || !contactedAt) {
            console.error(new Error('params is not defined'));
            return null;
          }
          const contactedAtJST = convertUTCtoJST(contactedAt);
          return generateBrandTldContactMessage({ email, fullname, telephone, contactedAt: contactedAtJST, company, tld });
        }
        default:
          console.error(`Unhandling template: ${template}`);
          return null;
      }
    }).filter(text => text !== null);

    const text = textParts.join('\n\n');

    const { ts } = await sendMessage(token, channelId, text, threadTs, replyBroadcast);

    if (reactions && reactions.length > 0) {
      reactions.forEach(async (reaction) => {
        await addReaction({ token, channelId, reaction, ts });
      });
    }
  } else if (threadTs && updateOptions) {
    const { patterns, replacements } = updateOptions.map(({ template, params }) => {
      let pattern;
      let replacement;
      switch (template) {
        case SLACK_REPLACEMENT_TAMPLATE_CLOUD: {
          const { status, providers } = params;
          if (!status || !providers) {
            console.error(new Error('params is not defined'));
            return;
          }
          pattern = PATTERN_CLOUD;
          if (status === 'error') {
            replacement = 'error';
          } else {
            if (providers.length > 0) replacement = '要確認';
            else replacement = '利用なし';
          }
          break;
        }
        case SLACK_REPLACEMENT_TAMPLATE_IMPERSONATION: {
          const { status, rank } = params;
          if (!status) {
            console.error(new Error('params is not defined'));
            return;
          }
          pattern = PATTERN_IMPERSONATION;
          if (status === 'error') {
            replacement = 'error';
          } else {
            const ranks = {
              A: '安全です',
              B: '要注意',
              C: '危険',
              D: '非常に危険',
            };
            replacement = ranks[rank];
          }
          break;
        }
        case SLACK_REPLACEMENT_TAMPLATE_NDS: {
          const { status, rank } = params;
          if (!status) {
            console.error(new Error('params is not defined'));
            return;
          }
          pattern = PATTERN_NDS;
          if (status === 'error') {
            replacement = 'error';
          } else {
            const ranks = {
              A: '安全です',
              B: '安全です',
              C: '要対策',
              D: '要対策',
              E: '要緊急対応',
            };
            replacement = ranks[rank];
          }
          break;
        }
        case SLACK_REPLACEMENT_TAMPLATE_SSL: {
          const { status, hostname_verification, cert_verification, free_ssl_provider, earliest_expires } = params;
          if (!status) {
            console.error(new Error('params is not defined'));
            return;
          }
          pattern = PATTERN_SSL;
          if (status === 'error') {
            replacement = 'error';
          } else {
            if (!hostname_verification) {
              replacement = '不一致';
            } else if (!cert_verification) {
              replacement = '無効';
            } else {
              const now = new Date();
              const after30Days = new Date(now.getTime() + THIRTY_DAYS_MILLISECOND);

              if (!!free_ssl_provider) {
                if (new Date(earliest_expires).getTime() < now.getTime()) {
                  replacement = '変更推奨（赤）';
                } else if (new Date(earliest_expires).getTime() < after30Days.getTime()) {
                  replacement = '変更推奨（黄）';
                } else {
                  replacement = '変更推奨（黄）';
                }
              } else {
                if (new Date(earliest_expires).getTime() < now.getTime()) {
                  replacement = '変更推奨（赤）';
                } else if (new Date(earliest_expires).getTime() < after30Days.getTime()) {
                  replacement = '変更推奨（黄）';
                } else {
                  replacement = '安全です';
                }
              }
            }
          }
          break;
        }
        default:
          console.error(`Unhandling template: ${template}`);
          return;
      }
      return { pattern, replacement };
    }).reduce((pre, cur) => ({ patterns: [...pre.patterns, cur.pattern], replacements: [...pre.replacements, cur.replacement] }), { patterns: [], replacements: [] });

    if (patterns.length !== replacements.length || patterns.find(p => !p) || replacements.find(r => !r)) {
      console.error(new Error('patterns or replacements are error'));
      return;
    }
    await replaceMessage(token, channelId, threadTs, patterns, replacements);
  } else {
    console.error(new Error('message or updateOptions is required'));
  }
};

export default subscriber;
