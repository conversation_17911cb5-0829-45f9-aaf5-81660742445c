import { PubSub } from '@google-cloud/pubsub';
import { COLLECTION_IMPERSONATION } from '../constants/collections.js';
import { SLACK_REPLACEMENT_TAMPLATE_IMPERSONATION, SLACK_TEMPLATE_IMPERSONATION_RESULT } from '../constants/slack_template.js';
import { TEMPLATE_IMPERSONATION_READY, TEMPLATE_REGULARLY_IMPERSONATION_READY } from '../constants/template.js';
import { TOPIC_SEND_EMAIL, TOPIC_SEND_SLACK_MESSAGE, TOPIC_WRITE_SITE_RISK_HISTORY } from '../constants/topics.js';
import { docExists, saveDoc } from '../providers/firestore.js';
import { request } from '../services/impersonation.js';
import { createImpersonationSink } from '../services/sink.js';

const pubSubClient = new PubSub({ projectId: process.env.PROJECT_ID });

const subscriber = async ({ data }) => {
  const { email, fqdn, createdAt, expiredAt, code, threadTs, isRegularly = false, lang = 'ja' } = JSON.parse(Buffer.from(data.message.data, 'base64').toString());
  if (!email || !fqdn || !createdAt || !expiredAt || !code) {
    console.error(new Error('email, fqdn, createdAt, expiredAt, code are required'));
    return;
  }

  if (await docExists({ collection: COLLECTION_IMPERSONATION, docId: code })) {
    console.error(new Error(`impersonation record duplicated code: ${code}`));
    return;
  }

  const result = await request(fqdn);
  if (!result) return;

  const log = await createImpersonationSink(email, fqdn, createdAt, expiredAt, code, { status: result.status, result: result.data }, isRegularly, lang);

  const dataImpersonation = { email, fqdn, createdAt, expiredAt, status: result.status, result: result.data, isRegularly, lang };
  await saveDoc({ collection: COLLECTION_IMPERSONATION, docId: code, data: dataImpersonation });

  let topic = pubSubClient.topic(TOPIC_WRITE_SITE_RISK_HISTORY);
  let message = Buffer.from(JSON.stringify({ email, fqdn, result: { impersonation: log } }));
  await topic.publishMessage({ data: message });

  if (threadTs) {
    topic = pubSubClient.topic(TOPIC_SEND_SLACK_MESSAGE);
    message = Buffer.from(JSON.stringify({ token: process.env.SECRET_SLACK_USER_TOKEN, channelId: process.env.SLACK_FQDN_CHANNEL_ID, message: { template: SLACK_TEMPLATE_IMPERSONATION_RESULT, params: log }, threadTs }));
    await topic.publishMessage({ data: message });

    message = Buffer.from(JSON.stringify({ token: process.env.SECRET_SLACK_USER_TOKEN, channelId: process.env.SLACK_FQDN_CHANNEL_ID, threadTs, updateOptions: [{ template: SLACK_REPLACEMENT_TAMPLATE_IMPERSONATION, params: log }] }));
    await topic.publishMessage({ data: message });
  }

  // nds, impersonation がメンテの時、メール送信する
  if ((process.env.NDS_MAINTENANCE && process.env.NDS_MAINTENANCE === 'true') && (process.env.IMPERSONATION_MAINTENANCE && process.env.IMPERSONATION_MAINTENANCE === 'true')) {
    topic = pubSubClient.topic(TOPIC_SEND_EMAIL);
    const url = `${process.env.HOST}${process.env.PATH_PREFIX}/check/site-risk/?code=${code}`;
    message = Buffer.from(JSON.stringify({ email, template: isRegularly ? TEMPLATE_REGULARLY_IMPERSONATION_READY : TEMPLATE_IMPERSONATION_READY, params: { createdAt, expiredAt, fqdn, url }, lang }));
    await topic.publishMessage({ data: message });
  }
};

export default subscriber;
