import { FieldValue } from '@google-cloud/firestore';
import { COLLECTION_REGULARLY_PASSWORD_CONFIGURATION } from '../constants/collections.js';
import { getDoc, saveDoc } from '../providers/firestore.js';
import { hashEmail } from '../services/cryptography.js';

const subscriber = async ({ data }) => {
  const { email, createdAt, result } = JSON.parse(Buffer.from(data.message.data, 'base64').toString());
  if (!email || !createdAt || !result) {
    console.error(new Error('email, createdAt and result are required'));
    return;
  }

  const passwordId = await hashEmail(email);
  const passwordDoc = await getDoc({ collection: COLLECTION_REGULARLY_PASSWORD_CONFIGURATION, docId: passwordId });

  const { code, names, count } = result;
  if (passwordDoc.exists) {
    await passwordDoc.ref.update({ history: FieldValue.arrayUnion({ createdAt, code, names, count }) });
  } else {
    await saveDoc({ collection: COLLECTION_REGULARLY_PASSWORD_CONFIGURATION, docId: passwordId, data: { history: FieldValue.arrayUnion({ createdAt, code, names, count }) } });
  }
};

export default subscriber;
