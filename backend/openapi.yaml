swagger: '2.0'
info:
  title: security-api
  version: 0.0.1
schemes:
  - https
produces:
  - application/json
x-google-endpoints:
  - name: security-api
    allowCors: True
paths:
  /api/password:
    get:
      summary: Get Password result
      operationId: get_password
      x-google-backend:
        address: https://asia-northeast1-dev-net-security-marketing.cloudfunctions.net/server/api/password
      parameters:
        - name: 'code'
          in: query
          required: true
          type: string
      responses:
        '200':
          description: A successful response
          schema:
            type: object
            properties:
              status:
                type: string
              message:
                type: string
              result:
                type: object
                properties:
                  summary:
                    type: object
                  detail:
                    type: object
  /api/site-risk:
    get:
      summary: Get SiteRisk result
      operationId: get_site_risk
      x-google-backend:
        address: https://asia-northeast1-dev-net-security-marketing.cloudfunctions.net/server/api/site-risk
      parameters:
        - name: 'code'
          in: query
          required: true
          type: string
      responses:
        '200':
          description: A successful response
          schema:
            type: object
            properties:
              status:
                type: string
              message:
                type: string
              impersonation:
                type: object
              nds:
                type: object
  /api/tld:
    get:
      summary: Check TLD validity
      operationId: get_tld
      x-google-backend:
        address: https://asia-northeast1-dev-net-security-marketing.cloudfunctions.net/server/api/tld
      parameters:
        - name: 'name'
          in: query
          required: true
          type: string
          minLength: 3
      responses:
        '200':
          description: A successful response
          schema:
            type: object
            properties:
              status:
                type: string
              result:
                type: object
                properties:
                  is_valid:
                    type: boolean
  /api/email:
    post:
      summary: Check Email
      operationId: check_email
      x-google-backend:
        address: https://asia-northeast1-dev-net-security-marketing.cloudfunctions.net/server/api/email
      parameters:
        - in: body
          name: body
          required: true
          schema:
            type: object
            properties:
              email:
                type: string
                format: email
              recaptchaToken:
                type: string
      responses:
        '200':
          description: A successful response
          schema:
            type: object
            properties:
              status:
                type: string
              message:
                type: string
  /api/fqdn:
    post:
      summary: Check Fqdn
      operationId: check_fqdn
      x-google-backend:
        address: https://asia-northeast1-dev-net-security-marketing.cloudfunctions.net/server/api/fqdn
      parameters:
        - in: body
          name: body
          required: true
          schema:
            type: object
            properties:
              fqdn:
                type: string
              email:
                type: string
                format: email
              recaptchaToken:
                type: string
      responses:
        '200':
          description: A successful response
          schema:
            type: object
            properties:
              status:
                type: string
              message:
                type: string
              result:
                type: object
                properties:
                  authTxt:
                    type: string
  /api/chat:
    post:
      summary: Chat
      operationId: chat
      x-google-backend:
        address: https://asia-northeast1-dev-net-security-marketing.cloudfunctions.net/server/api/chat
      parameters:
        - in: body
          name: body
          required: true
          schema:
            type: object
            properties:
              post:
                type: string
              messages:
                type: array
                items:
                  type: object
                  properties:
                    role:
                      type: string
                    content:
                      type: string
              threadTs:
                type: string
              recaptchaToken:
                type: string
      responses:
        '200':
          description: A successful response. The response is streamed as a series of events.
          schema:
            type: string 
  /api/feedback:
    post:
      summary: Chat feedback
      operationId: chat_feedback
      x-google-backend:
        address: https://asia-northeast1-dev-net-security-marketing.cloudfunctions.net/server/api/feedback
      parameters:
        - in: body
          name: body
          required: true
          schema:
            type: object
            properties:
              threadTs:
                type: string
              replyNumber:
                type: number
              feedback:
                type: string
              recaptchaToken:
                type: string
      responses:
        '200':
          description: A successful response
          schema:
            type: object
            properties:
              status:
                type: string
              message:
                type: string
  /api/confirm:
    post:
      summary: Confirm Email for SiteRisk
      operationId: confirm_email
      x-google-backend:
        address: https://asia-northeast1-dev-net-security-marketing.cloudfunctions.net/server/api/confirm
      parameters:
        - in: body
          name: body
          required: true
          schema:
            type: object
            properties:
              code:
                type: string
      responses:
        '200':
          description: A successful response
          schema:
            type: object
            properties:
              status:
                type: string
              message:
                type: string
              result:
                type: object
                properties:
                  email:
                    type: string
                  fqdn:
                    type: string
  /webhook/nds:
    post:
      summary: Nds Webhook
      operationId: nds_webhook
      x-google-backend:
        address: https://asia-northeast1-dev-net-security-marketing.cloudfunctions.net/server/webhook/nds
      parameters:
        - in: body
          name: body
          required: true
          schema:
            type: object
            properties:
              id:
                type: string
              fqdn:
                type: string
              rank:
                type: string
              result:
                type: object
      responses:
        '200':
          description: A successful response
          schema:
            type: string
