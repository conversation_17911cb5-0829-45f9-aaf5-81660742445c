export const toCamelCase = (str) => {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
};

export const toCamelCaseObject = (obj) => {
  const result = {};
  Object.keys(obj).forEach((key) => {
    result[toCamelCase(key)] = obj[key];
  });
  return result;
};

export const toSnakeCase = (str) => {
  return str.replace(/([A-Z])/g, '_$1').toLowerCase();
};

export const toSnakeCaseObject = (obj) => {
  const result = {};
  Object.keys(obj).forEach((key) => {
    result[toSnakeCase(key)] = obj[key];
  });
  return result;
};
