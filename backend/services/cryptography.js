import {
  constants,
  createCipheriv,
  createDecipheriv,
  createHash,
  privateDecrypt,
  publicEncrypt,
  randomBytes,
  scrypt,
} from 'crypto';
import * as util from 'util';

const ALGORITHM = 'aes-256-cbc';
const SEPARATOR = '-';
const KEY_LEN = 32;

export const encrypt = async (
  plainStr,
  password,
  salt,
) => {
  const iv = randomBytes(16);
  const key = await util
    .promisify(scrypt)(password, salt, KEY_LEN)
    .then(v => Promise.resolve(v));
  const cipher = createCipheriv(ALGORITHM, key, iv);
  const encryptedStr = Buffer.concat([
    cipher.update(plainStr),
    cipher.final(),
  ]).toString('hex');

  return `${encryptedStr}${SEPARATOR}${iv.toString('hex')}`;
};

export const decrypt = async (
  encryptedStrWithIv,
  password,
  salt,
) => {
  try {
    const splitted = encryptedStrWithIv.split(SEPARATOR);
    if (splitted.length !== 2) {
      throw new Error('Invalid encrypted str');
    }

    const [encryptedStr, ivHex] = splitted;
    const iv = Buffer.from(ivHex, 'hex');
    const key = await util
      .promisify(scrypt)(password, salt, KEY_LEN)
      .then(v => Promise.resolve(v));
    const decipher = createDecipheriv(ALGORITHM, key, iv);
    const decryptedStr = Buffer.concat([
      decipher.update(encryptedStr, 'hex'),
      decipher.final(),
    ]).toString('utf8');
    return decryptedStr;
  } catch (err) {
    // eslint-disable-next-line no-console
    console.log(`decrypt error encryptedStrWithIv:${encryptedStrWithIv}`);
    return false;
  }
};

export const hash = async plainStr => createHash('sha1').update(plainStr).digest('hex');

export const hashEmail = async email => `${(await hash(email.split('@')[0]))}@${email.split('@')[1]}`;

export const pubEncrypt = (plainStr, publicKey) => {
  const buffer = Buffer.from(plainStr, 'utf8');
  const encrypted = publicEncrypt(
    {
      key: publicKey.replace(/\\n/g, '\n'),
      padding: constants.RSA_PKCS1_OAEP_PADDING,
      oaepHash: 'sha256',
    },
    buffer,
  );
  return encrypted.toString('base64');
};

export const priDecrypt = (encryptedStr, privateKey) => {
  const buffer = Buffer.from(encryptedStr, 'base64');
  const decrypted = privateDecrypt(
    {
      key: privateKey.replace(/\\n/g, '\n'),
      padding: constants.RSA_PKCS1_OAEP_PADDING,
      oaepHash: 'sha256',
    },
    buffer,
  );
  return decrypted.toString('utf8');
};
