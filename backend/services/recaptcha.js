import axios from 'axios';

const RECAPTCHA_URL = 'https://www.google.com/recaptcha/api/siteverify';

export const isVerified = async (recaptchaToken) => {
  if (process.env.RECAPTCHA_DRY_RUN && process.env.RECAPTCHA_DRY_RUN === 'true') {
    return true;
  }

  const { status, data } = await axios.post(RECAPTCHA_URL, {}, { params: { secret: process.env.SECRET_RECAPTCHA_SECRET, response: recaptchaToken } });

  if (status !== 200) return false;
  if (!data || !data.success) {
    return false;
  }

  return true;
};
