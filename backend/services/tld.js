import axios from 'axios';

const API_URL = 'https://api-security-dot-bjp-hp-776072411506.asia-northeast1.run.app/oapi/security/checker/tld/v1';

export const request = async (name) => {
  if (process.env.TLD_MAINTENANCE && process.env.TLD_MAINTENANCE === 'true') {
    // eslint-disable-next-line no-console
    console.log(`TLD service is in maintenance mode. name: ${name}`);
    return false;
  }
  try {
    const { status, data } = await axios.get(`${API_URL}?name=${encodeURIComponent(name)}`);

    if (status !== 200) {
      throw new Error(`TLD request error name: ${name}, status: ${status}, data: ${data}`);
    }
    if (!data) {
      throw new Error(`TLD request error name: ${name}`);
    }
    if (!data.status || data.status.type !== 'OK' || !data.data) {
      throw new Error(`TLD request error name: ${name}, data: ${data}`);
    }

    return { status: 'success', data: data.data };
  } catch (err) {
    console.error(new Error(`TLD API Error name: ${name}`));
    console.error(JSON.stringify(err));
    throw err;
  }
};
