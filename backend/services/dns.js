import dns from 'node:dns';

export const isVerified = async (fqdn, key, value) => {
  if (process.env.DNS_DRY_RUN && process.env.DNS_DRY_RUN === 'true') {
    return true;
  }

  try {
    const records = await dns.promises.resolveTxt(fqdn);

    for (const recordEntries of records) {
      for (const entry of recordEntries) {
        if (entry === `${key}=${value}`) return true;
      }
    }
    return false;
  } catch (err) {
    if (err.message.startsWith('queryTxt ENODATA')) return false;
    if (err.message.startsWith('queryTxt ENOTFOUND')) return false;
    throw err;
  }
};
