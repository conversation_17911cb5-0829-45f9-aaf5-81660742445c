import axios from 'axios';

const API_URL = 'https://api-security-dot-bjp-hp-776072411506.asia-northeast1.run.app/oapi/security/checker/site/v2';

export const request = async (fqdn) => {
  if (process.env.IMPERSONATION_MAINTENANCE && process.env.IMPERSONATION_MAINTENANCE === 'true') {
    // eslint-disable-next-line no-console
    console.log(`Impersonation is maintenanced fqdn: ${fqdn}`);
    return false;
  }
  try {
    const { status, data } = await axios.get(`${API_URL}?domain_nm=${fqdn}`, {});

    if (status !== 200) {
      throw new Error(`Impersonation request error fqdn: ${fqdn}, status: ${status}, data: ${data}`);
    }
    if (!data) {
      throw new Error(`Impersonation request error fqdn: ${fqdn}`);
    }
    if (!data.status || data.status.code !== 200 || !data.data) {
      throw new Error(`Impersonation request error fqdn: ${fqdn}, data: ${data}`);
    }

    return { status: 'success', data: data.data };
  } catch (err) {
    console.error(new Error(`Impersonation Api Error fqdn: ${fqdn}`));
    console.error(JSON.stringify(err));
    throw err;
  }
};
