import { PubSub } from '@google-cloud/pubsub';
import { COLLECTION_HIBP } from '../constants/collections.js';
import { TTL_MILLISECOND } from '../constants/constants.js';
import { TEMPLATE_REGULARLY_ANNOUNCE } from '../constants/template.js';
import { TOPIC_CHECK_EMAIL, TOPIC_SEND_EMAIL } from '../constants/topics.js';
import firestore from '../providers/firestore.js';
import { encrypt } from '../services/cryptography.js';

// logs4.map(l => l.textPayload).map(t => t.match(regex3)[1]).map(decodeURIComponent).join('\n')
const EMAILS = [

];

const pubSubClient = new PubSub({ projectId: process.env.PROJECT_ID });

const runOne = async ({ email, password, siteRisks }) => {
  const topic = pubSubClient.topic(TOPIC_SEND_EMAIL);
  const data = Buffer.from(JSON.stringify({ email, template: TEMPLATE_REGULARLY_ANNOUNCE, params: { password, siteRisks } }));

  console.log(`PUBLISH: ${data}`);
  return await topic.publishMessage({ data });
};

const run = async () => {
  for (const email of EMAILS) {
    await runOne(email);
  }
};

run();
