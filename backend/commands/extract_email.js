import { Firestore } from '@google-cloud/firestore';
import { COLLECTION_HIBP, COLLECTION_IMPERSONATION, COLLECTION_REGULARLY_PASSWORD_CONFIGURATION, COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION } from '../constants/collections.js';
import { hashEmail, hash } from '../services/cryptography.js';

const firestore = new Firestore({ projectId: process.env.PROJECT_ID });

async function fetchData() {
  const [passwordSnapshot, siteriskSnapshot, passwordConfigSnapshot, siteriskConfigSnapshot] = await Promise.all([
    firestore.collection(COLLECTION_HIBP).get(),
    firestore.collection(COLLECTION_IMPERSONATION).get(),
    firestore.collection(COLLECTION_REGULARLY_PASSWORD_CONFIGURATION).get(),
    firestore.collection(COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION).get(),
  ]);

  const latestPasswords = {};

  passwordSnapshot.forEach((doc) => {
    const data = doc.data();
    const { email, createdAt } = data;
    if (createdAt && createdAt > '2025-02-06T04:00:00') {
      if (!latestPasswords[email] || !latestPasswords[email].createdAt || (createdAt && latestPasswords[email].createdAt < createdAt)) {
        latestPasswords[email] = { id: doc.id, createdAt };
      }
    }
  });

  const latestSiteRisks = {};

  siteriskSnapshot.forEach((doc) => {
    const data = doc.data();
    const { email, fqdn, createdAt } = data;
    if (createdAt && createdAt > '2025-02-06T04:00:00') {
      if (!latestSiteRisks[email]) {
        latestSiteRisks[email] = { [fqdn]: { id: doc.id, fqdn, createdAt } };
      } else if (!latestSiteRisks[email][fqdn] || !latestSiteRisks[email][fqdn].createdAt || (createdAt && latestSiteRisks[email][fqdn].createdAt < createdAt)) {
        latestSiteRisks[email] = { ...latestSiteRisks[email], [fqdn]: { id: doc.id, fqdn, createdAt } };
      }
    }
  });

  const passwordConfigs = new Set();
  passwordConfigSnapshot.forEach((doc) => {
    passwordConfigs.add(doc.id);
  });

  const siteriskConfigs = new Set();
  siteriskConfigSnapshot.forEach((doc) => {
    siteriskConfigs.add(doc.id);
  });

  const resultsMap = {};

  for (const [email, { id: passwordId }] of Object.entries(latestPasswords)) {
    if (!passwordConfigs.has(await hashEmail(email))) {
      if (!resultsMap[email]) {
        resultsMap[email] = {
          email,
          password: { email, url: `${process.env.HOST}${process.env.PATH_PREFIX}/check/password/?code=${passwordId}` },
          siteRisks: [],
        };
      } else {
        resultsMap[email].password = { email, url: `${process.env.HOST}${process.env.PATH_PREFIX}/check/password/?code=${passwordId}` };
      }
    }
  }

  for (const [email, value] of Object.entries(latestSiteRisks)) {
    for (const [fqdn, { id: siteRiskId }] of Object.entries(value)) {
      if (!siteriskConfigs.has(await hash(`${email}:${fqdn}`))) {
        if (!resultsMap[email]) {
          resultsMap[email] = {
            email,
            siteRisks: [{ fqdn, url: `${process.env.HOST}${process.env.PATH_PREFIX}/check/site-risk/?code=${siteRiskId}` }],
          };
        } else {
          resultsMap[email].siteRisks.push({ fqdn, url: `${process.env.HOST}${process.env.PATH_PREFIX}/check/site-risk/?code=${siteRiskId}` });
        }
      }
    }
  }

  return Object.values(resultsMap);
}

fetchData().then((results) => {
  console.log(results.map(r => JSON.stringify(r)).join(',\n'));
}).catch((error) => {
  console.error('Error fetching data:', error);
});
