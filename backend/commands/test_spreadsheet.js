import { PubSub } from '@google-cloud/pubsub';
import { TOPIC_WRITE_SPREADSHEET } from '../constants/topics.js';

const pubSubClient = new PubSub({ projectId: process.env.PROJECT_ID });

const test = {
  docId: '13kkUDmcRlGZX-p3UAg9RbdAG4DLZO1E1TKReYN7yg8k',
  sheetId: '0',
  records: [
    ['2023-10-01', 'test1', 'test2'],
    ['2023-10-02', 'test3', 'test4'],
    ['2023-10-03', 'test5', 'test6'],
  ],
};

const run = async () => {
  const topic = pubSubClient.topic(TOPIC_WRITE_SPREADSHEET);
  const data = Buffer.from(JSON.stringify(test));
  await topic.publishMessage({ data });
  return true;
};

run();
