import { COLLECTION_CLOUD, COLLECTION_HIBP, COLLECTION_IMPERSONATION, COLLECTION_NDS, COLLECTION_SSL } from '../constants/collections.js';
import { THIRTY_DAYS_MILLISECOND } from '../constants/constants.js';
import { getDocsByDateRange } from '../providers/firestore.js';
import { convertDate } from '../services/daytime.js';

const summaryHibp = async (from, to) => {
  const hbids = await getDocsByDateRange({
    collection: COLLECTION_HIBP,
    from: from,
    to: to,
  });

  const passwordLeakCounts = hbids.reduce((acc, doc) => {
    const passwordLeakCount = doc.result.filter(r => r.DataClasses.includes('Passwords')).length;
    acc[passwordLeakCount] = (acc[passwordLeakCount] || 0) + 1;
    return acc;
  }, {});

  return sortByKey(passwordLeakCounts);
};

const summaryNds = async (from, to) => {
  const nds = await getDocsByDateRange({
    collection: COLLECTION_NDS,
    from: from,
    to: to,
  });

  const rankCounts = nds.reduce(
    (acc, doc) => {
      if (doc.result.status === 'error') {
        acc.error = (acc.error || 0) + 1;
        return acc;
      }

      acc[doc.result.rank] = (acc[doc.result.rank] || 0) + 1;
      return acc;
    },
    { A: 0, B: 0, C: 0, D: 0, E: 0, error: 0 },
  );

  return rankCounts;
};

const summaryCloud = async (from, to) => {
  const cloud = await getDocsByDateRange({
    collection: COLLECTION_CLOUD,
    from: from,
    to: to,
  });

  const cloudProviders = cloud.reduce(
    (acc, doc) => {
      if (doc.status && doc.status === 'error') {
        acc.error += 1;
        return acc;
      }

      const details = Array.from(new Set(doc.result.filter(r => !!r.provider).map(r => JSON.stringify({ cloud: r.provider })))).map(
        JSON.parse,
      );
      const isUsed = details.reduce((pre, cur) => pre || ['AWS', 'GoogleCloud', 'Azure'].includes(cur.cloud), false);

      if (!isUsed) {
        acc.NoCloud += 1;
        return acc;
      }

      const providers = details.map(({ cloud }) => cloud);

      if (providers.includes('AWS')) {
        acc.AWS += 1;
      }
      if (providers.includes('GoogleCloud')) {
        acc.GoogleCloud += 1;
      }
      if (providers.includes('Azure')) {
        acc.Azure += 1;
      }

      return acc;
    },
    {
      AWS: 0,
      GoogleCloud: 0,
      Azure: 0,
      NoCloud: 0,
      error: 0,
    },
  );

  return cloudProviders;
};

const summaryImpernation = async (from, to) => {
  const impersonations = await getDocsByDateRange({
    collection: COLLECTION_IMPERSONATION,
    from: from,
    to: to,
  });

  const rankCounts = impersonations.reduce(
    (acc, doc) => {
      if (doc.status && doc.status === 'error') {
        acc.error += 1;
        return acc;
      }

      acc[doc.result.rank] = (acc[doc.result.rank] || 0) + 1;
      return acc;
    },
    { A: 0, B: 0, C: 0, D: 0, error: 0 },
  );

  return rankCounts;
};

const summarySsl = async (from, to) => {
  const ssls = await getDocsByDateRange({
    collection: COLLECTION_SSL,
    from: from,
    to: to,
  });

  const summarySsl = ssls.reduce(
    (acc, doc) => {
      if (doc.status && doc.status === 'error') {
        acc.error += 1;
        return acc;
      }

      const details = Array.from(
        doc.result.map(r =>
          JSON.stringify({
            freeSslProvider: r.free_ssl_provider,
            expires: r.validity?.not_after ? convertDate(r.validity.not_after).toISOString() : null,
            hostnameVerification: r.hostname_verification,
            certVerification: r.cert_verification,
          }),
        ),
      ).map(JSON.parse);

      const hostnameVerification = details.find(r => !!r.hostnameVerification)?.hostnameVerification?.verified;
      const certVerification = details.find(r => !!r.certVerification)?.certVerification?.verified;

      if (!hostnameVerification) {
        acc.falseHostVerification += 1;
      } else if (!certVerification) {
        acc.falseCertVerification += 1;
      } else {
        const freeSslProvider = details.find(r => !!r.freeSslProvider)?.freeSslProvider;
        const earliestExpires = details
          .filter(({ expires }) => expires)
          .map(({ expires }) => expires)
          .sort((a, b) => new Date(a).getTime() - new Date(b).getTime())[0];

        const now = new Date();
        const after30Days = new Date(now.getTime() + THIRTY_DAYS_MILLISECOND);
        if (!!freeSslProvider) {
          if (new Date(earliestExpires).getTime() < now.getTime()) {
            acc.freeSslProviderExpiredCount += 1;
          } else if (new Date(earliestExpires).getTime() < after30Days.getTime()) {
            acc.freeSslProvidersWithExpiryWithin30Days += 1;
          } else {
            acc.freeSslProvidersWithExpiryOver30Days += 1;
          }
        } else {
          if (new Date(earliestExpires).getTime() < now.getTime()) {
            acc.nonFreeSslProviderExpiredCount += 1;
          } else if (new Date(earliestExpires).getTime() < after30Days.getTime()) {
            acc.nonFreeSslProvidersWithExpiryWithin30Days += 1;
          } else {
            acc.nonFreeSslProvidersWithExpiryOver30Days += 1;
          }
        }
      }
      return acc;
    },
    {
      freeSslProvidersWithExpiryOver30Days: 0,
      freeSslProvidersWithExpiryWithin30Days: 0,
      freeSslProviderExpiredCount: 0,
      nonFreeSslProvidersWithExpiryOver30Days: 0,
      nonFreeSslProvidersWithExpiryWithin30Days: 0,
      nonFreeSslProviderExpiredCount: 0,
      falseHostVerification: 0,
      falseCertVerification: 0,
      error: 0,
    },
  );

  return summarySsl;
};

const sortByKey = (obj) => {
  return Object.keys(obj)
    .sort((a, b) => {
      if (!isNaN(a) && !isNaN(b)) {
        return a - b;
      }
      return a.localeCompare(b);
    })
    .reduce((acc, key) => {
      acc[key] = obj[key];
      return acc;
    }, {});
};

const run = async (from, to) => {
  const hibpPromise = summaryHibp(from, to);
  const ndsPromise = summaryNds(from, to);
  const cloudPromise = summaryCloud(from, to);
  const impersonationPromise = summaryImpernation(from, to);
  const sslPromise = summarySsl(from, to);

  const [hibp, nds, cloud, impersonation, ssl] = await Promise.all([
    hibpPromise,
    ndsPromise,
    cloudPromise,
    impersonationPromise,
    sslPromise,
  ]);

  console.log({ hibp, nds, cloud, impersonation, ssl });

  return { hibp, nds, cloud, impersonation, ssl };
};

const [from, to] = process.argv.slice(2);

if (!from || !to) {
  console.error('Please provide both "from" and "to" date parameters.');
  process.exit(1);
}

run(from, to);
