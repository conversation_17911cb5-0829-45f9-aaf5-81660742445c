import { PubSub } from '@google-cloud/pubsub';
import { COLLECTION_HIBP } from '../constants/collections.js';
import { TTL_MILLISECOND } from '../constants/constants.js';
import { TEMPLATE_REGULARLY_ANNOUNCE } from '../constants/template.js';
import { TOPIC_CHECK_EMAIL, TOPIC_SEND_EMAIL } from '../constants/topics.js';
import { publishCheckEmail } from '../handlers/post_email.js';
import { publishCheckFqdn } from '../handlers/post_fqdn.js';
import firestore from '../providers/firestore.js';
import { encrypt } from '../services/cryptography.js';

const INPUT = [
  '<EMAIL>',
  '<EMAIL>',
];

const run = async () => {
  for (const i of INPUT) {
    await publishCheckEmail(i, true);
  }
};

run();
