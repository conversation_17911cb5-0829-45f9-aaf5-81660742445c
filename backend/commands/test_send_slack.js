import { PubSub } from '@google-cloud/pubsub';
import { TOPIC_SEND_SLACK_MESSAGE } from '../constants/topics.js';

const test = {
  token: process.env.SECRET_SLACK_USER_TOKEN,
  channelId: process.env.SLACK_CHAT_CHANNEL_ID,
  thread: { ts: '' },
  message: 'hogehoge reply',
};
const pubSubClient = new PubSub({ projectId: process.env.PROJECT_ID });

const run = async () => {
  try {
    const topic = pubSubClient.topic(TOPIC_SEND_SLACK_MESSAGE);
    const data = Buffer.from(JSON.stringify(test));
    topic.publishMessage({ data });
    return 0;
  } catch (err) {
    console.error(err);
  }
};

run();
