import fs from 'fs';

fs.readFile('.env.yaml', 'utf8', (err, data) => {
  if (err) {
    console.error('ファイルの読み込みに失敗しました:', err);
    return;
  }

  const lines = data.split('\n').map(line => line.trim())
    .filter(line => line.length > 0 && !line.startsWith('#'));

  const keyValuePairs = lines.map((line) => {
    const [key, value] = line.split(': ').map(part => part.trim());
    return key + '=' + value.replace('(', '\\(').replace(')', '\\)');
  });

  const output = keyValuePairs.join(' ');

  console.log(output);
});
