{"name": "backend", "version": "1.0.0", "main": "index.js", "type": "module", "license": "MIT", "scripts": {"lint": "eslint .", "license-checker": "license-checker --onlyAllow 'MIT;ISC;Apache-2.0;BSD' --production", "start": "bash -c \"$(node commands/set_env.js) functions-framework --port=8080 --target=server\"", "start-bulk-check-api": "bash -c \"$(node commands/set_env.js) functions-framework --port=8081 --target=bulk-check-api\"", "start-watch-hibp-api": "bash -c \"$(node commands/set_env.js) functions-framework --port=8082 --target=watch-hibp-api\"", "start-fetch-usd-rate": "bash -c \"$(node commands/set_env.js) functions-framework --port=8083 --target=fetch-usd-rate-api\"", "subscribe-email": "bash -c \"$(node commands/set_env.js) functions-framework --port=18080 --target=email-subscriber --signature-type=event\"", "subscribe-hibp": "bash -c \"$(node commands/set_env.js) functions-framework --port=18081 --target=hibp-subscriber --signature-type=event\"", "subscribe-impersonation": "bash -c \"$(node commands/set_env.js) functions-framework --port=18082 --target=impersonation-subscriber --signature-type=event\"", "subscribe-nds": "bash -c \"$(node commands/set_env.js) functions-framework --port=18083 --target=nds-subscriber --signature-type=event\"", "subscribe-recon": "bash -c \"$(node commands/set_env.js) functions-framework --port=18084 --target=recon-subscriber --signature-type=event\"", "subscribe-slack-message": "bash -c \"$(node commands/set_env.js) functions-framework --port=18085 --target=slack-message-subscriber --signature-type=event\"", "subscribe-check-email-reservation": "bash -c \"$(node commands/set_env.js) functions-framework --port=18086 --target=check-email-reservation-subscriber --signature-type=event\"", "subscribe-check-fqdn-reservation": "bash -c \"$(node commands/set_env.js) functions-framework --port=18087 --target=check-fqdn-reservation-subscriber --signature-type=event\"", "subscribe-password-history": "bash -c \"$(node commands/set_env.js) functions-framework --port=18088 --target=password-history-subscriber --signature-type=event\"", "subscribe-site-risk-history": "bash -c \"$(node commands/set_env.js) functions-framework --port=18089 --target=site-risk-history-subscriber --signature-type=event\"", "subscribe-pre-check-email": "bash -c \"$(node commands/set_env.js) functions-framework --port=18091 --target=pre-check-email-subscriber --signature-type=event\"", "subscribe-pre-check-fqdn": "bash -c \"$(node commands/set_env.js) functions-framework --port=18092 --target=pre-check-fqdn-subscriber --signature-type=event\"", "subscribe-spreadsheet": "bash -c \"$(node commands/set_env.js) functions-framework --port=18090 --target=spreadsheet-subscriber --signature-type=event\"", "firestore": "gcloud emulators firestore start --host-port=localhost:8045 --project=test-security", "pubsub": "gcloud beta emulators pubsub start --host-port=localhost:8043 --project=test-security", "create-topic": "PUBSUB_EMULATOR_HOST=localhost:8043 PROJECT_ID=test-security node commands/create_topic.js", "manual-email-publish": "bash -c \"$(node commands/set_env.js) node commands/manual_email_publish \"", "rerun-check-fqdn": "bash -c \"$(node commands/set_env.js) node commands/rerun_check_fqdn \"", "rerun-check-email": "bash -c \"$(node commands/set_env.js) node commands/rerun_check_email \"", "extract-email": "bash -c \"$(node commands/set_env.js) node commands/extract_email \"", "test-site-risk": "bash -c \"$(node commands/set_env.js) node commands/test_site_risk \"", "test-password": "bash -c \"$(node commands/set_env.js) node commands/test_password_risk \"", "test-send-slack": "bash -c \"$(node commands/set_env.js) node commands/test_send_slack \"", "test-spreadsheet": "bash -c \"$(node commands/set_env.js) node commands/test_spreadsheet \"", "summary-audit": " bash -c \"$(node commands/set_env.js) node commands/summary_audit \\\"\\$@\\\" \" -- $@", "dev-deploy-server": "gcloud functions deploy server --project=dev-net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=server --env-vars-file=.env.dev.yaml --set-secrets='SECRET_CRYPTOGRAPHY_PASSWORD=SECRET_CRYPTOGRAPHY_PASSWORD:latest,SECRET_CRYPTOGRAPHY_SALT=SECRET_CRYPTOGRAPHY_SALT:latest,SECRET_RECAPTCHA_SECRET=SECRET_RECAPTCHA_SECRET:latest,SECRET_SLACK_AI_TOKEN=SECRET_SLACK_AI_TOKEN:latest,SECRET_SLACK_USER_TOKEN=SECRET_SLACK_USER_TOKEN:latest,SECRET_PUBLIC_KEY=SECRET_PUBLIC_KEY:latest,SECRET_SENDGRID_PUBLIC_KEY=SECRET_SENDGRID_PUBLIC_KEY:latest,SECRET_NDS_API_KEY=SECRET_NDS_API_KEY:latest' --trigger-http", "dev-deploy-bulk-check-api": "gcloud functions deploy bulk-check-api --project=dev-net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=bulk-check-api --env-vars-file=.env.dev.yaml --trigger-http", "dev-deploy-watch-hibp-api": "gcloud functions deploy watch-hibp-api --project=dev-net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=watch-hibp-api --env-vars-file=.env.dev.yaml --set-secrets='SECRET_SLACK_AI_TOKEN=SECRET_SLACK_AI_TOKEN:latest' --trigger-http", "dev-deploy-fetch-usd-rate": "gcloud functions deploy fetch-usd-rate-api --project=dev-net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=fetch-usd-rate-api --env-vars-file=.env.dev.yaml --set-secrets='SECRET_EXCHANGE_RATES_API_KEY=SECRET_EXCHANGE_RATES_API_KEY:latest' --trigger-http", "dev-deploy-check-email-reservation-subscriber": "gcloud functions deploy check-email-reservation-subscriber --project=dev-net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=check-email-reservation-subscriber --env-vars-file=.env.dev.yaml --set-secrets='SECRET_PUBLIC_KEY=SECRET_PUBLIC_KEY:latest' --trigger-topic=check_email --retry", "dev-deploy-check-fqdn-reservation-subscriber": "gcloud functions deploy check-fqdn-reservation-subscriber --project=dev-net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=check-fqdn-reservation-subscriber --env-vars-file=.env.dev.yaml --set-secrets='SECRET_PUBLIC_KEY=SECRET_PUBLIC_KEY:latest' --trigger-topic=check_fqdn --retry", "dev-deploy-dead-letter-subscriber": "gcloud functions deploy dead-letter-subscriber --project=dev-net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=dead-letter-subscriber --env-vars-file=.env.dev.yaml --trigger-topic=dead_letter", "dev-deploy-email-subscriber": "gcloud functions deploy email-subscriber --project=dev-net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=email-subscriber --env-vars-file=.env.dev.yaml --set-secrets='SECRET_SENDGRID_API_KEY=SECRET_SENDGRID_API_KEY:latest' --trigger-topic=send_email", "dev-deploy-hibp-subscriber": "gcloud functions deploy hibp-subscriber --project=dev-net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=hibp-subscriber --env-vars-file=.env.dev.yaml --set-secrets='SECRET_HIBP_API_KEY=SECRET_HIBP_API_KEY:latest' --trigger-topic=check_email --retry", "dev-deploy-impersonation-subscriber": "gcloud functions deploy impersonation-subscriber --project=dev-net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=impersonation-subscriber --env-vars-file=.env.dev.yaml --set-secrets='SECRET_SLACK_USER_TOKEN=SECRET_SLACK_USER_TOKEN:latest' --trigger-topic=check_fqdn --retry", "dev-deploy-nds-subscriber": "gcloud functions deploy nds-subscriber --project=dev-net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=nds-subscriber --env-vars-file=.env.dev.yaml --set-secrets='SECRET_NDS_API_KEY=SECRET_NDS_API_KEY:latest' --trigger-topic=check_fqdn --retry", "dev-deploy-password-history-subscriber": "gcloud functions deploy password-history-subscriber --project=dev-net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=password-history-subscriber --env-vars-file=.env.dev.yaml --trigger-topic=write_password_history --retry", "dev-deploy-pre-check-email-subscriber": "gcloud functions deploy pre-check-email-subscriber --project=dev-net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=pre-check-email-subscriber --env-vars-file=.env.dev.yaml --set-secrets='SECRET_CRYPTOGRAPHY_PASSWORD=SECRET_CRYPTOGRAPHY_PASSWORD:latest,SECRET_CRYPTOGRAPHY_SALT=SECRET_CRYPTOGRAPHY_SALT:latest,SECRET_PRIVATE_KEY=SECRET_PRIVATE_KEY:latest' --trigger-topic=pre_check_email --retry", "dev-deploy-pre-check-fqdn-subscriber": "gcloud functions deploy pre-check-fqdn-subscriber --project=dev-net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=pre-check-fqdn-subscriber --env-vars-file=.env.dev.yaml --set-secrets='SECRET_SLACK_USER_TOKEN=SECRET_SLACK_USER_TOKEN:latest,SECRET_CRYPTOGRAPHY_PASSWORD=SECRET_CRYPTOGRAPHY_PASSWORD:latest,SECRET_CRYPTOGRAPHY_SALT=SECRET_CRYPTOGRAPHY_SALT:latest,SECRET_PRIVATE_KEY=SECRET_PRIVATE_KEY:latest' --trigger-topic=pre_check_fqdn --retry", "dev-deploy-prompt-subscriber": "gcloud functions deploy prompt-subscriber --project=dev-net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=prompt-subscriber  --env-vars-file=.env.dev.yaml --trigger-bucket=dev-security-ai", "dev-deploy-static-subscriber": "gcloud functions deploy static-subscriber --project=dev-net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=static-subscriber  --env-vars-file=.env.dev.yaml --trigger-bucket=dev-security-static", "dev-deploy-recon-subscriber": "gcloud functions deploy recon-subscriber --project=dev-net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=recon-subscriber --env-vars-file=.env.dev.yaml --set-secrets='SECRET_SLACK_USER_TOKEN=SECRET_SLACK_USER_TOKEN:latest' --trigger-topic=check_fqdn --retry", "dev-deploy-site-risk-history-subscriber": "gcloud functions deploy site-risk-history-subscriber --project=dev-net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=site-risk-history-subscriber --env-vars-file=.env.dev.yaml --trigger-topic=write_site_risk_history --retry", "dev-deploy-slack-message-subscriber": "gcloud functions deploy slack-message-subscriber --project=dev-net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=slack-message-subscriber --env-vars-file=.env.dev.yaml --trigger-topic=send_slack_message --retry", "dev-deploy-spreadsheet-subscriber": "gcloud functions deploy spreadsheet-subscriber --project=dev-net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=spreadsheet-subscriber --env-vars-file=.env.dev.yaml --trigger-topic=write_spreadsheet --retry", "dev-deploy-all-subscribers": "yarn dev-deploy-email-subscriber && yarn dev-deploy-hibp-subscriber && yarn dev-deploy-impersonation-subscriber && yarn dev-deploy-nds-subscriber &&  yarn dev-deploy-recon-subscriber && yarn dev-deploy-slack-message-subscriber", "prod-deploy-server": "gcloud functions deploy server --project=net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=server --env-vars-file=.env.prod.yaml --set-secrets='SECRET_CRYPTOGRAPHY_PASSWORD=SECRET_CRYPTOGRAPHY_PASSWORD:latest,SECRET_CRYPTOGRAPHY_SALT=SECRET_CRYPTOGRAPHY_SALT:latest,SECRET_RECAPTCHA_SECRET=SECRET_RECAPTCHA_SECRET:latest,SECRET_SLACK_AI_TOKEN=SECRET_SLACK_AI_TOKEN:latest,SECRET_SLACK_USER_TOKEN=SECRET_SLACK_USER_TOKEN:latest,SECRET_PUBLIC_KEY=SECRET_PUBLIC_KEY:latest,SECRET_SENDGRID_PUBLIC_KEY=SECRET_SENDGRID_PUBLIC_KEY:latest,SECRET_NDS_API_KEY=SECRET_NDS_API_KEY:latest' --trigger-http", "prod-deploy-bulk-check-api": "gcloud functions deploy bulk-check-api --project=net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=bulk-check-api --env-vars-file=.env.prod.yaml --trigger-http", "prod-deploy-watch-hibp-api": "gcloud functions deploy watch-hibp-api --project=net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=watch-hibp-api --env-vars-file=.env.prod.yaml --set-secrets='SECRET_SLACK_AI_TOKEN=SECRET_SLACK_AI_TOKEN:latest' --trigger-http", "prod-deploy-fetch-usd-rate": "gcloud functions deploy fetch-usd-rate-api --project=net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=fetch-usd-rate-api --env-vars-file=.env.prod.yaml --set-secrets='SECRET_EXCHANGE_RATES_API_KEY=SECRET_EXCHANGE_RATES_API_KEY:latest' --trigger-http", "prod-deploy-check-email-reservation-subscriber": "gcloud functions deploy check-email-reservation-subscriber --project=net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=check-email-reservation-subscriber --env-vars-file=.env.prod.yaml --set-secrets='SECRET_PUBLIC_KEY=SECRET_PUBLIC_KEY:latest' --trigger-topic=check_email --retry", "prod-deploy-check-fqdn-reservation-subscriber": "gcloud functions deploy check-fqdn-reservation-subscriber --project=net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=check-fqdn-reservation-subscriber --env-vars-file=.env.prod.yaml --set-secrets='SECRET_PUBLIC_KEY=SECRET_PUBLIC_KEY:latest' --trigger-topic=check_fqdn --retry", "prod-deploy-dead-letter-subscriber": "gcloud functions deploy dead-letter-subscriber --project=net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=dead-letter-subscriber --env-vars-file=.env.prod.yaml --trigger-topic=dead_letter", "prod-deploy-email-subscriber": "gcloud functions deploy email-subscriber --project=net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=email-subscriber --env-vars-file=.env.prod.yaml --set-secrets='SECRET_SENDGRID_API_KEY=SECRET_SENDGRID_API_KEY:latest' --trigger-topic=send_email --retry", "prod-deploy-hibp-subscriber": "gcloud functions deploy hibp-subscriber --project=net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=hibp-subscriber --env-vars-file=.env.prod.yaml --set-secrets='SECRET_HIBP_API_KEY=SECRET_HIBP_API_KEY:latest' --trigger-topic=check_email --retry", "prod-deploy-impersonation-subscriber": "gcloud functions deploy impersonation-subscriber --project=net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=impersonation-subscriber --env-vars-file=.env.prod.yaml --set-secrets='SECRET_SLACK_USER_TOKEN=SECRET_SLACK_USER_TOKEN:latest' --trigger-topic=check_fqdn --retry", "prod-deploy-nds-subscriber": "gcloud functions deploy nds-subscriber --project=net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=nds-subscriber --env-vars-file=.env.prod.yaml --set-secrets='SECRET_NDS_API_KEY=SECRET_NDS_API_KEY:latest' --trigger-topic=check_fqdn --retry", "prod-deploy-password-history-subscriber": "gcloud functions deploy password-history-subscriber --project=net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=password-history-subscriber --env-vars-file=.env.prod.yaml --trigger-topic=write_password_history --retry", "prod-deploy-pre-check-email-subscriber": "gcloud functions deploy pre-check-email-subscriber--project=net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=pre-check-email-subscriber --env-vars-file=.env.prod.yaml --set-secrets='SECRET_CRYPTOGRAPHY_PASSWORD=SECRET_CRYPTOGRAPHY_PASSWORD:latest,SECRET_CRYPTOGRAPHY_SALT=SECRET_CRYPTOGRAPHY_SALT:latest,SECRET_PRIVATE_KEY=SECRET_PRIVATE_KEY:latest' --trigger-topic=pre_check_email --retry", "prod-deploy-pre-check-fqdn-subscriber": "gcloud functions deploy pre-check-fqdn-subscriber --project=net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=pre-check-fqdn-subscriber --env-vars-file=.env.prod.yaml --set-secrets='SECRET_SLACK_USER_TOKEN=SECRET_SLACK_USER_TOKEN:latest,SECRET_CRYPTOGRAPHY_PASSWORD=SECRET_CRYPTOGRAPHY_PASSWORD:latest,SECRET_CRYPTOGRAPHY_SALT=SECRET_CRYPTOGRAPHY_SALT:latest,SECRET_PRIVATE_KEY=SECRET_PRIVATE_KEY:latest' --trigger-topic=pre_check_fqdn --retry", "prod-deploy-prompt-subscriber": "gcloud functions deploy prompt-subscriber --project=net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=prompt-subscriber  --env-vars-file=.env.prod.yaml --trigger-bucket=security-prompt", "prod-deploy-static-subscriber": "gcloud functions deploy static-subscriber --project=net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=static-subscriber  --env-vars-file=.env.prod.yaml --trigger-bucket=security-static --trigger-location=asia", "prod-deploy-recon-subscriber": "gcloud functions deploy recon-subscriber --project=net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=recon-subscriber --env-vars-file=.env.prod.yaml --set-secrets='SECRET_SLACK_USER_TOKEN=SECRET_SLACK_USER_TOKEN:latest' --trigger-topic=check_fqdn --retry", "prod-deploy-site-risk-history-subscriber": "gcloud functions deploy site-risk-history-subscriber --project=net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=site-risk-history-subscriber --env-vars-file=.env.prod.yaml --trigger-topic=write_site_risk_history --retry", "prod-deploy-slack-message-subscriber": "gcloud functions deploy slack-message-subscriber --project=net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=slack-message-subscriber --env-vars-file=.env.prod.yaml --trigger-topic=send_slack_message --retry", "prod-deploy-spreadsheet-subscriber": "gcloud functions deploy spreadsheet-subscriber --project=net-security-marketing --gen2 --region=asia-northeast1 --runtime=nodejs20 --entry-point=spreadsheet-subscriber --env-vars-file=.env.prod.yaml --trigger-topic=write_spreadsheet --retry", "prod-deploy-all-subscribers": "yarn prod-deploy-email-subscriber && yarn prod-deploy-hibp-subscriber && yarn prod-deploy-impersonation-subscriber && yarn prod-deploy-nds-subscriber && yarn prod-deploy-recon-subscriber && yarn prod-deploy-slack-message-subscriber"}, "devDependencies": {"@stylistic/eslint-plugin": "^4.2.0", "eslint": "^9.25.1", "eslint-plugin-import": "^2.31.0", "license-checker": "^25.0.1"}, "dependencies": {"@fastify/cors": "^10.0.2", "@google-cloud/firestore": "^7.11.0", "@google-cloud/functions-framework": "^3.4.4", "@google-cloud/pubsub": "^4.9.0", "@google-cloud/storage": "^7.15.0", "@googleapis/sheets": "^9.6.0", "@sendgrid/eventwebhook": "^8.0.0", "@sendgrid/mail": "^8.1.4", "@slack/web-api": "^7.8.0", "ajv-keywords": "^5.1.0", "axios": "^1.7.9", "crypto": "^1.0.1", "fastify": "^5.1.0", "free-email-domains-list": "^1.0.16", "validator": "^13.12.0"}}